import {
  getCode
} from "../utils/login"
const app = getApp();
// 更新后台直播状态
function updateLiveStatus(roomId, liveStatus) {
  wx.$get({
    map: 'applet_appletlive_update_status',
    room_id: roomId,
    status: liveStatus,
  }, {
    pageList: true
  }).then(res => {}).catch(err => {})
}
let isLoadingReceive = false;
// 领取优惠券
function couponReceive(data = {}) {
  data['map'] = 'applet_coupon_receive';
  data['cid'] = data.id;
  if (isLoadingReceive) return;
  isLoadingReceive = true;
  return new Promise(function (resolve, reject) {

    wx.$get(data).then(res => {
      wx.showToast({
        title: res.data.msg,
        icon: 'none'
      })
      isLoadingReceive = false
      resolve(res);
    }).catch(err => {
      isLoadingReceive = false
      reject(err);
    })

  })
}
/**
 * 
 * @param {object} userdata   用户授权信息
 * @desc   获取用户信息  调用方式  authorizeUserInfo.call(this,userdata)
 */
function authorizeUserInfo(e) {
  //操作之前的slient值
  let oriSlient = app.globalData.slient;
  let info = app.globalData.userInfo;
  let userdata = "",
    userInfo = "";
  let sex = "";
  getProfile().then(async e => {
    userdata = e;
    userInfo = e.userInfo
    console.log("userInfo", userInfo)
    const noUpdate = wx.getStorageSync('noUpdate')
    if (userInfo.avatarUrl == 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132') {
      userInfo.avatarUrl = noUpdate.avatar
    }
    if (userInfo.nickName == '微信用户') {
      userInfo.nickName = noUpdate.nickname
    }
    // 刷新设置信息
    const Custom_avatar_nickname = wx.getStorageSync("Custom_avatar_nickname");
    if (Custom_avatar_nickname) {
      Custom_avatar_nickname.avatar ?
        (userInfo.avatarUrl = Custom_avatar_nickname.avatar) :
        userInfo.avatarUrl;
      Custom_avatar_nickname.nickname ?
        (userInfo.nickName = Custom_avatar_nickname.nickname) :
        userInfo.nickName;
    }
    sex = userInfo.gender == 2 ? '女' : '男';
    let data = {
      map: 'applet_update_member_info',
      avatar: userInfo.avatarUrl,
      nickname: userInfo.nickName,
      sex: sex,
      city: userInfo.city,
      province: userInfo.province,

    }
    if (app.globalData.isHasunionid == 0 && app.globalData.haveVcmCard == 1) {
      let reslogin = await getCode();
      data.iv = userdata.iv,
        data.encryptedData = userdata.encryptedData,
        data.code = reslogin.code
    }

    if (this.data.isupdateLoading) return;
    this.data.isupdateLoading = true
    //更新用户信息
    return wx.$get(data)
  }).then(res => {
    //设置数据
    setUserInfo.call(this, userInfo, e)
  }).catch(err => {
    console.log(err)
    this.data.isupdateLoading = false
  })
}
/**
 * 
 * @param {object} userdata   旧版使用button用户授权信息
 * @desc   获取用户信息  调用方式  oldAuthorizeUserInfo.call(this,userdata)
 */
async function oldAuthorizeUserInfo(e) {
  console.log(userdata)
  let userdata = e.detail;
  let userInfo = userdata.userInfo;

  let data = {
    map: 'applet_update_member_info',
    avatar: userInfo.avatarUrl,
    nickname: userInfo.nickName,
    sex: userInfo.gender == 2 ? '女' : '男',
    city: userInfo.city,
    province: userInfo.province,
  }
  if (app.globalData.isHasunionid == 0 && app.globalData.haveVcmCard == 1) {
    let reslogin = await getCode();
    data.iv = userdata.iv,
      data.encryptedData = userdata.encryptedData,
      data.code = reslogin.code
  }
  //更新用户信息

  wx.$get(data).then(res => {
    setUserInfo.call(this, userInfo, e)

  }).catch(err => {
    console.log(err)
    this.data.isupdateLoading = false

  })


}

function setUserInfo(userInfo, e) {
  // 用户优化设置头像
  // if (app.globalData.slient == 1) {
    this.setData({
      Custom_avatar_nickname: 1,
    });
  // }
  //操作之前的slient值
  let oriSlient = app.globalData.slient;
  let info = app.globalData.userInfo;
  let callback = e.currentTarget.dataset.callback;
  let sex = userInfo.gender == 2 ? '女' : '男';
  //设置数据
  app.globalData.slient = '0';
  info.avatar = userInfo.avatarUrl;
  info.nickname = userInfo.nickName;
  info.sex = sex;
  app.globalData.userInfo = info;
  app.globalData.isHasunionid = 1;

  this.data.isupdateLoading = false
  this.setData({
    userInfo: info,
    slient: 0,
    isHasunionid: app.globalData.isHasunionid,
  })
  let tipText = oriSlient == 1 ? '授权成功' : '同步成功'

  wx.showToast({
    title: tipText
  })
  if (this[callback] && typeof this[callback] == 'function') {
    if (this.data.canUseGetProfile) {
      this.setData({
        Custom_avatar_nickname: 1,
      });
    } else {
      this[callback](e)
    }
  }
  //  this[callback]&&typeof this[callback]=='function'&&this[callback](e)
}
//判断是否是tabbar页面
function isTabbarpath() {
  let indexUrl = this.route
  var menu = app.globalData.menuTitle ? app.globalData.menuTitle : '';
  var isTabpath = false;
  if (menu.hasOwnProperty(indexUrl)) {
    isTabpath = true;
  }
  return isTabpath;
}
//获取头像昵称信息
function getProfile() {
  return new Promise(function (resolve, reject) {
    if (!wx.getUserProfile) {
      wx.showModal({
        title: '提示',
        content: '您的微信版本过低，请更新微信版本,或进入个人中心进行授权',
        showCancel: false
      })
      reject();
      return
    }
    wx.getUserProfile({
      desc: '便于使用程序功能', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        console.log(err);
        reject(err);
      },
    })
  })
}
/**
 * @desc   获取当前位置信息
 * @param {number} lat 
 * @param {number} lng 
 * @desc   调用方式  getCurAddress.call(this)或者将该函数注册到page函数中
 */
function getCurAddress(lat, lng) {
  wx.$get({
    map: 'applet_get_address',
    lat: lat,
    lng: lng
  }).then(res => {
    this.setData({
      curLocation: res.data.address
    })
  }).catch(err => {
    console.log(err)
  })
}
/**
 * @desc   获取手机号
 * @desc   调用方式  getPhone.call(this)或者将该函数注册到page函数中
 */
function getPhone(e) {
  getCode().then(res => {
    var data = {
      map: 'applet_bind_member_mobile',
      code: res.code,
      encryptedData: e.detail.encryptedData,
      iv: e.detail.iv
    };
    return wx.$get(data)
  }).then(res => {
    app.globalData.contactPhone = res.data;
    this.setData({
      contactPhone: res.data
    })
  }).catch(err => {
    console.log(err)
  })
}
/**
 * @desc   获取unionId
 * @desc   调用方式  getUnionId.call(this)或者将该函数注册到page函数中
 */
function getUnionId() {
  let e = null
  getProfile().then(res => {
    e = res;
    return getCode()
  }).then(res => {
    var data = {
      map: 'applet_get_member_unionid',
      code: res.code,
      encryptedData: e.encryptedData,
      iv: e.iv
    };

    return wx.$get(data)

  }).then(res => {
    app.globalData.isHasunionid = 1
    if (typeof this.setData == 'function') {
      this.setData({
        isHasunionid: 1
      })
    }

  }).catch(err => {
    console.log(err)
  })
}

/**
 * 记录客服点击
 * @param {*} e 
 */
function contactRecord(e) {
  var _this = this;
  wx.$get({
    map: 'applet_kefu_click'
  }, {
    showLoading: false,
    showError: false
  }).then(res => {

  }).catch(err => {})
  if (e.detail.path) {
    let navUrl = e.detail.path + "?";
    for (let i in e.detail.query) {
      navUrl += (i + '=' + e.detail.query[i] + '&');
    };
    wx.navigateTo({
      url: navUrl,
    })
  }
}


module.exports = {
  updateLiveStatus,
  couponReceive,
  authorizeUserInfo,
  oldAuthorizeUserInfo,
  getCurAddress,
  getPhone,
  getUnionId,
  contactRecord
}