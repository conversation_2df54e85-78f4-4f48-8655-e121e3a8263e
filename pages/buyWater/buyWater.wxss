page {
  background-color: #f8f8f8;
}

.content-box>view {
  width: 708rpx;
  margin: 16rpx auto 0;
  background-color: #ffffff;
  border-radius: 16rpx;
}

cover-view {
  overflow: visible;
}

.payway-choose radio {
  position: relative;
  top: -4rpx;
}

.deliveryMethod {
  width: 750rpx !important;
  box-sizing: border-box;
  padding: 30rpx 25rpx;
  background-color: #fff;
  margin-bottom: 16rpx;
}

.deliveryMethod .flex-con {
  text-align: right;
}

.deliveryMethod .radio-group {
  font-size: 30rpx;
  flex-wrap: wrap;
}

.deliveryMethod .radio {
  color: #666;
  margin-left: 12rpx;
  display: inline-block;
  margin-bottom: 8rpx;
}

.deliveryMethod .radio text {
  position: relative;
  top: 3rpx;
}

.deliveryMethod .expressWay-show {
  color: #999;
}

.take-delivery {
  background-color: #fff;
  padding-left: 20rpx;
  box-sizing: border-box;
  box-shadow: 1rpx 0 3rpx #eee;
}

.take-delivery .delivery-item {
  padding: 20rpx 20rpx 20rpx 0;
}

.take-delivery .delivery-item .label-name {
  margin-right: 20rpx;
  width: 130rpx;
}

.take-delivery .delivery-item .right-input text {
  color: #777;
}

.take-delivery image.line {
  width: 102%;
  height: 5rpx;
  display: block;
  margin-left: -2%;
}

.see-deliveryarea {
  background-color: #fff;
  font-size: 28rpx;
  color: #157ffa;
  padding: 25rpx;
  position: relative;
  box-sizing: border-box;
}

.see-deliveryarea image {
  width: 24rpx;
  height: 24rpx;
}

.see-deliveryarea .see-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.address-wrap .address-con {
  background-color: #fff;
  padding: 30rpx 25rpx;
  position: relative;
}

.address-wrap .line {
  width: 100%;
  height: 5rpx;
  position: absolute;
  bottom: 0;
  left: 0;
}

.noaddress {
  display: none;
}

.noaddress.show {
  display: block;
}

.noaddress .icon-add {
  height: 110rpx;
  width: 110rpx;
  margin-right: 20rpx;
}

.noaddress image {
  display: block;
  height: 130rpx;
  width: 144rpx;
  margin: 0 auto;
}

.noaddress text {
  font-size: 28rpx;
  color: #555;
  display: block;
  text-align: center;
  margin-top: 8rpx;
}

.address-con .address-enter {
  width: 32rpx;
  height: 35rpx;
  margin-left: 15rpx;
}

.hasaddress {
  display: none;
}

.hasaddress.show {
  display: flex;
}

.hasaddress .address-icon {
  width: 34rpx;
  height: 34rpx;
  background: linear-gradient(90deg, #FE0100 0%, #FE6001 100%);
  border-radius: 50%;
  justify-content: center;
}

.hasaddress .icon_address {
  height: 18rpx;
  width: 18rpx;
  margin-top: 2rpx;
}

.hasaddress .edit-btn {
  width: 110rpx;
  height: 56rpx;
  border: 1rpx solid #B3B3B3;
  border-radius: 28rpx;
  margin-left: 20rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}

.hasaddress .address-detail {
  margin-left: 20rpx;
}

.hasaddress .address-detail .name-tel {
  margin-bottom: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.hasaddress .address-detail .tel {
  font-size: 24rpx;
  font-weight: 500;
  color: #666666;
  margin-left: 12rpx;
}

.hasaddress .address-detail .delivery-address {
  font-size: 26rpx;
  font-weight: 500;
  color: #666666;
}

/*商品信息*/
.good-info-wrap {
  border-bottom: 1px solid #eee;
  background-color: #fff;
  margin-top: 15rpx !important;
}

.shop-name {
  padding: 20rpx;
}

.shop-name .icon-shop {
  width: 26rpx;
  height: 26rpx;
  margin-right: 10rpx;
}

.good-info-wrap .price-limit-tip {
  padding: 0 20rpx 20rpx;
  font-size: 28rpx;
  color: #ff2e4b;
  font-weight: 600;
}

.good-info {
  padding: 0 20rpx;
  box-sizing: border-box;
}

.good-item {
  position: relative;
  padding: 28rpx 0;
  background-color: #ffffff;
  align-items: flex-start;
  border-bottom: 2rpx solid #E6E6E6;
}

.good-info .good-item:last-child {
  border-bottom: 0;
}

.good-item .good-img {
  width: 158rpx;
  height: 158rpx;
  margin-right: 30rpx;
  border-radius: 16rpx;
}

.good-item .good-name .name {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
  line-height: 1.2;
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.good-item .good-name .guige {
  font-size: 26rpx;
  font-weight: 500;
  color: #666666;
  margin-top: 15rpx;
}

.good-item .price-num {
  text-align: right;
  margin-top: 24rpx;
}

.good-item .price-num .num {
  font-size: 28rpx;
  font-weight: 500;
}

.good-item .price-num .icon-price {
  font-size: 20rpx;
  font-weight: 400;
}

.good-item .cant-store-buy {
  position: absolute;
  left: 240rpx;
  bottom: 15rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #ff2200;
  z-index: 9;
}

.good-fjinfo {
  padding-left: 25rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.kuajing {
  background-color: #fff;
  margin-top: 16rpx;
}

.kuajing>view {
  padding: 25rpx;
}

.delivery-method {
  padding: 25rpx 25rpx 25rpx 0;
  align-items: flex-start;
}

.free-delivery text {
  display: block;
  text-align: right;
  color: #666;
}

.free-delivery .delivery-kd {
  font-size: 26rpx;
}

.right-input input {
  padding: 0 10rpx;
  font-size: 30rpx;
  color: #666;
}

.right-input text {
  line-height: 50rpx;
  color: #666;
  font-size: 30rpx;
}

.right-price {
  text-align: right;
}

.right-price .price {
  color: #ff475b;
}

.info-item {
  padding: 14rpx 25rpx;
  box-sizing: border-box;
  background-color: #fff;
  margin-top: 16rpx;
  line-height: 1.8;
  font-size: 30rpx;
}

.info-item .icon-yhhb {
  width: 62rpx;
  height: 62rpx;
  margin-right: 16rpx;
}

.info-item .flex-wrap {
  padding: 10rpx 0;
}

/*支付方式  */
.pay-choose {
  margin-top: 16rpx;
}

.pay-choose .info-item {
  margin: 0;
}

.pay-choose .opencard-btn {
  font-size: 26rpx;
  border: 1px solid #157ffa;
  color: #157ffa;
  display: inline-block;
  vertical-align: middle;
  border-radius: 6rpx;
  margin-left: 10rpx;
  padding: 0 10rpx;
  position: relative;
  top: -2rpx;
}

.info-item .right-info {
  text-align: right;
  color: #666;
}

.info-item .right-info .flex-con view {
  text-align: left;
}

.pay-choose .radio-wrap {
  background-color: #fff;
  padding: 8rpx 25rpx;
  font-size: 30rpx;
}

.pay-choose .radio {
  display: block;
  height: 80rpx;
  line-height: 80rpx;
  padding: 0;
}

.pay-choose .radio radio {
  float: right;
  margin-right: -10rpx;
}

.pay-choose .balance {
  text-align: left;
  font-size: 24rpx;
  color: #999;
}

.pay-choose .balance text {
  color: #ff475b;
}

.right-info switch {
  margin-right: -10rpx;
}

.right-info .choose-coupon text {
  display: block;
  color: #333;
}

.right-info .choose-coupon .desc {
  font-size: 26rpx;
  color: #999;
}

.right-info .see-enter {
  vertical-align: middle;
  height: 26rpx;
  width: 30rpx;
  margin-right: -6rpx;
  position: relative;
}

.recharge-tip {
  color: #38f;
  font-size: 24rpx;
  display: inline-block;
  vertical-align: middle;
  position: relative;
}

.recharge-tip image {
  display: inline-block;
  margin-left: 8rpx;
  width: 90rpx;
  height: 40rpx;
  vertical-align: middle;
  position: relative;
  top: -2rpx;
}

.pay-choose .get-unionId {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  opacity: 0;
  z-index: 2;
}

/*底部操作*/
.bottom-zhanwei {
  height: 110rpx;
  margin-top: 20rpx;
}

.bottom-zhanwei cover-view {
  overflow: visible;
  pointer-events: none;
}

.bottom-opera {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 11;
  width: 100%;
  background-color: #fff;
}

.bottom-opera .sumprice {
  padding: 0;
  box-sizing: border-box;
  line-height: 110rpx;
  font-size: 0;
  padding-left: 25rpx;
}

.bottom-opera .sumprice .sum {
  display: inline-block;
  vertical-align: middle;
  font-size: 32rpx;
}

.bottom-opera .sumprice .price {
  color: #ff2e4b;
  font-size: 36rpx;
  display: inline-block;
  vertical-align: middle;
  padding-right: 20rpx;
  white-space: nowrap;
  min-width: 180rpx;
  text-align: left;
  position: relative;
}

.bottom-opera .buy-btn {
  width: 230rpx;
  height: 110rpx;
  line-height: 110rpx;
  text-align: center;
  background-color: #ff464f;
  color: #fff;
  font-size: 32rpx;
  position: relative;
  border-radius: 0;
}

.bottom-opera .buy-btn[disabled] {
  background-color: #ff464f;
  opacity: 0.7;
  color: #fff;
}

.bottom-opera .buy-btn button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  margin: 0;
}

.bottom-opera .buy-btn.gray {
  background-color: #ccc;
}

/* 优惠促销弹出层 */
.modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 3;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100008;
  width: 100%;
  background-color: #fff;
}

.modal-title {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  position: relative;
  font-weight: bold;
  font-size: 32rpx;
}

.modal-title .close {
  height: 44rpx;
  width: 44rpx;
  padding: 18rpx;
  position: absolute;
  top: 5rpx;
  right: 5rpx;
  z-index: 1;
}

.main-content {
  padding: 0 20rpx 30rpx;
}

.main-content .confirm-btn {
  width: 92%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  background-color: #FF464F;
  color: #fff;
  margin: 0 auto;
  margin-top: 30rpx;
}

.coupon .list-item {
  padding: 20rpx 0;
}

.coupon .list-item .checkbox {
  display: block;
  height: 40rpx;
  width: 40rpx;
  margin-right: 8rpx;
}

.coupon .list-item .flex-con text {
  display: block;
}

.coupon .list-item .flex-con .desc {
  font-size: 26rpx;
  color: #999;
}

.coupon .list-item .tag-tip {
  color: #666;
}

.promotion .list-item {
  padding: 20rpx 0;
}

.promotion .list-item .checkbox {
  display: block;
  height: 40rpx;
  width: 40rpx;
  margin-right: 8rpx;
}

.promotion .list-item .tag-tip {
  color: #666;
}

.recharge-tip {
  color: #38f;
}

/* 自定义表单 */
.good-fjinfo .delivery-method {
  padding: 0;
  align-items: center;
}

.good-fjinfo .delivery-method:last-child:after {
  height: 0;
}

.delivery-method .right-input {
  box-sizing: border-box;
  padding-right: 25rpx;
  overflow: hidden;
}

.right-input textarea,
.right-input .textarea-place {
  height: 150rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 10rpx 0;
  font-size: 30rpx;
  margin: 0 auto 18rpx;
  display: block;
  background-color: #fff;
  border-radius: 8rpx;
}

.right-input .textarea-place {
  color: #999;
}

.cusinput-label {
  width: 170rpx;
  text-align: left;
  font-size: 30rpx;
  line-height: 90rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cusinput-label.lineh {
  line-height: 70rpx;
}

.cusinput-label text.must {
  color: red;
  font-size: 28rpx;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
}

.cusinput-label .name {
  text-justify: distribute-all-lines;
  text-align-last: justify;
  text-align: justify;
  display: inline-block;
  vertical-align: middle;
  width: 120rpx;
  white-space: nowrap;
  overflow: hidden;
}

.right-input picker {
  line-height: 90rpx;
  position: relative;
  font-size: 30rpx;
  padding: 0 10rpx;
}

.right-input picker .icon_xiala {
  position: absolute;
  right: 20rpx;
  top: 0;
  width: 26rpx;
  height: 90rpx;
}

.right-input .img-box {
  width: 180rpx;
  height: 180rpx;
  border: 1rpx solid #eee;
  box-sizing: border-box;
  margin: 20rpx 0;
  display: inline-block;
  vertical-align: bottom;
}

.right-input .tips {
  display: inline-block;
  vertical-align: bottom;
  color: #38f;
  font-size: 24rpx;
  margin-left: 15rpx;
  position: relative;
  top: -10rpx;
}

.right-input .img-box image {
  width: 100%;
  height: 100%;
  display: block;
  background-color: #fff;
  border-radius: 8rpx;
}

.right-input .img-box .add-img {
  padding: 14%;
  box-sizing: border-box;
}

.right-input .radio-group .radio,
.right-input .checkbox-group .checkbox {
  font-size: 28rpx;
  color: #666;
  display: inline-block;
  vertical-align: middle;
  padding: 8rpx 0;
  width: 100%;
}

.right-input .radio-group .radio .radio-name,
.right-input .checkbox-group .checkbox .check-name {
  line-height: 34rpx;
  padding: 8rpx 0;
}

.right-input .radio-group .radio radio,
.right-input .checkbox-group .checkbox checkbox {
  width: 50rpx;
  position: relative;
  top: -2rpx;
  margin-right: 5rpx;
}

/* 配送范围及价格弹出层 */
.agremment-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100001;
}

.agremment-modal .modal-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  z-index: 1;
}

.agremment-modal .modal-con {
  position: absolute;
  left: 8%;
  top: 50%;
  width: 84%;
  background-color: #fff;
  border-radius: 6rpx;
  z-index: 2;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.agremment-modal .modal-con scroll-view {
  max-height: 800rpx;
  box-sizing: border-box;
}

.agremment-modal .modal-con .title {
  font-weight: bold;
  text-align: center;
  line-height: 2;
  font-size: 32rpx;
  padding: 20rpx 20rpx 0;
}

.agremment-modal .modal-con .agreement-txt {
  padding: 0 20rpx 0;
}

.agremment-modal .modal-con .wxParse {
  padding: 10rpx 0;
}

/* 不能门店自取商品列表 */
.cant-pick-wrap {
  padding: 0 20rpx 20rpx 20rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 640rpx;
  max-height: 680rpx;
  background: #fff;
  border-radius: 10rpx;
}

.cant-pick-wrap .tit {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  line-height: 100rpx;
}

.cant-pick-wrap .close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
}

.cant-pick-list {
  max-height: 540rpx;
}

.cant-pick-goods {
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  color: #666;
  border: 1rpx solid #eee;
}

.cant-pick-goods image {
  margin-right: 20rpx;
  width: 140rpx;
  height: 140rpx;
}

.cant-pick-goods .good-tit {
  width: 460rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.flex-box {
  align-items: flex-end;
}

.pick-card {
  width: 100%;
}

.pick-card image {
  width: 50rpx;
  height: 50rpx;
  margin-top: 10rpx;
}

.pick-card .card-text {
  letter-spacing: 16rpx;
  width: 140rpx;
}

.pick-card .card-box {
  padding: 10rpx;
  margin-right: 8rpx;
}

.crad-price {
  font-size: 24rpx;
  color: #fd3e33;
}

/* 圆角 */
.address-wrap {
  border-radius: 20rpx 20rpx 0 0 !important;
}

.see-deliveryarea {
  border-radius: 0 0 20rpx 20rpx !important;
  margin: 0 auto !important;
}



/* My style */
.goods-detail-border-bottom{
  border-bottom: 1px solid #f8f8f8 !important;
}
.computed-number {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10rpx;
}
.minus-and-add {
  width: 40rpx;
  height: 40rpx;
}

.number {
  margin: 0 10rpx;
}
.number-bucket{
  text-align: right;
  padding: 20rpx 0;
  font-size: 26rpx;
}
.pay-type{
  display: flex;
  justify-content: space-between !important;
}