<view class="flex-wrap flex-vertical content-box">
  <view class="address-wrap">
    <view class="address-con flex-wrap">
      <view class="noaddress flex-con {{address.length==0?'show':''}}" data-type="waitorder" bindtap="chooseAddress">
        <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/icon_noaddr_tip.png" mode="aspectFit">
        </image>
        <text>还没有收货地址，去添加</text>
      </view>
      <view class="hasaddress flex-con flex-wrap {{address.id?'show':''}}" data-type="waitorder" bindtap="chooseAddress">
        <view class="flex-con flex-wrap">
          <view class="flex-wrap address-icon">
            <image src="../../images/icon_wz.png" mode="aspectFit" class="icon_address"></image>
          </view>
          <view class="address-detail flex-con">
            <view class="name-tel flex-wrap">
              <text class="name">{{address.name}}</text>
              <text class="name">{{address.mobile}}</text>
            </view>
            <text class="delivery-address">{{address.detail}}</text>
          </view>
        </view>
        <image src="/images/icon_enter.png" mode="aspectFit" class="address-enter" wx:if="{{!orderstatus}}" wx:if="{{!payStatus}}">
        </image>
      </view>
    </view>
  </view>
  <view class="good-info-wrap border-t">
    <view class="shop-name flex-wrap">
      <image src="/images/icon_sangc.png" mode="aspectFit" class="icon-shop"></image>
      <text class="flex-con">商品信息</text>
    </view>
    <view class="good-info">
      <block wx:key="index" wx:for="{{cartList}}">
        <view class="good-item flex-wrap goods-detail-border-bottom">
          <image src="{{item.cover}}" mode="aspectFill" class="good-img"></image>
          <view class="good-name flex-con flex-wrap1 flex-vertical">
            <view class="name">{{item.name}}</view>
            <view class="flex-sp-wrap price-num">
              <view class="flex-bm num">原有空桶：{{emptyBarrelNumber}}</view>
            </view>
            <view class="computed-number">
              <image src="/images/waterMinus.png" class="minus-and-add" wx:if="{{item.num && !payStatus}}" data-type="minus" data-index="{{index}}" bindtap="minusAndadd"></image>
              <view class="number" wx:if="{{item.num}}">
                {{item.num}}
              </view>
              <image src="/images/waterAdd.png" class="minus-and-add" wx:if="{{!payStatus}}" style="margin-right: 5rpx;" data-type="add" data-index="{{index}}" bindtap="minusAndadd"></image>
              <text wx:if="{{item.num}}">桶</text>
            </view>
          </view>
        </view>
        <view class="number-bucket" wx:if="{{item.num}}">
          合计配送{{item.num}}桶
        </view>
      </block>
    </view>
  </view>
  <view class="pay-choose">
    <view class="flex-wrap border-t radio-wrap pay-type">
      <view style="padding:12rpx 0;">
        付款方式
      </view>
      <view>
        使用水票
      </view>
    </view>
    <view class="flex-wrap border-t radio-wrap pay-type">
      <view style="padding:12rpx 0;">
        水票
      </view>
      <view>
        剩余{{waterNum}}张
      </view>
    </view>
    <view class="flex-wrap border-t radio-wrap pay-type" wx:if="{{addEmptyBarrelNumber}}">
      <view style="padding:12rpx 0;">
        增加空桶数量
      </view>
      <view>
        {{addEmptyBarrelNumber}}
      </view>
    </view>
    <view class="flex-wrap border-t radio-wrap pay-type" wx:if="{{addEmptyBarrelNumber}}">
      <view style="padding:12rpx 0;">
        空桶押金总计
      </view>
      <view>
        {{emptyBarrelMoney}}
      </view>
    </view>
  </view>
  <view class="pay-choose">
    <radio-group class="radio-group" bindchange="payRadioChange">
      <view wx:key="index" wx:for="{{payType}}" wx:for-item="pay">
        <view class="flex-wrap border-t radio-wrap" wx:if="{{!payStatus || type == pay.type }}">
          <view style="padding:12rpx 0;">
            {{pay.typeNote}}
            <view class="balance" wx:if="{{pay.type==3||(pay.type==4&&pay.opened==1)}}">可用余额
              <text>￥{{pay.balance}}</text>
              <block wx:if="{{pay.type==3&&pay.balance<emptyBarrelMoney}}"> (余额不足)
                <view class="recharge-tip" bindtap="toRecharge">
                  <image src="/images/icon_chongzhi.png" mode="aspectFit"></image>
                </view>
              </block>
            </view>
          </view>
          <label class="radio flex-con">
            <radio value="{{pay.type}}" disabled="{{pay.type==3&&pay.balance<emptyBarrelMoney}}" checked="{{type==pay.type}}" color="{{themeColor1?themeColor1:'#F94700'}}" />
          </label>
        </view>
      </view>
    </radio-group>
  </view>
  <view style="display: flex;align-items: center;">
    <view style="margin-left: 20rpx;letter-spacing: 2em;">
      备
    </view>注
    <input disabled="{{payStatus}}" placeholder="请输入备注" bindinput="remark" />
  </view>

</view>
<view class="bottom-zhanwei safe-padding" style="margin-top:20rpx;"></view>
<cover-view class="bottom-opera flex-wrap border-t safe-padding" hidden="{{isShowModal||isShowAgreement}}">
  <cover-view class="flex-con sumprice">
    <cover-view class="sum">支付金额：</cover-view>
    <cover-view class="price">￥{{emptyBarrelMoney}}</cover-view>
  </cover-view>
  <block>
    <button wx:if="{{!payStatus}}" disabled="{{!setNum}}" style="background:{{themeColor1?themeColor1:'#F94700'}};" class="buy-btn" bindtap="submitOrder">提交订单</button>
    <button wx:else style="background:{{themeColor1?themeColor1:'#F94700'}};" class="buy-btn" bindtap="orderPay">去支付</button>
  </block>
</cover-view>
<!--错误提示-->
<view class="error-tip fade_in" wx:if="{{errorTip.isShow}}">
  {{errorTip.text}}
</view>