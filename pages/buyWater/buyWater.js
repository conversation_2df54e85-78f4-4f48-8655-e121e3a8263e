import {
  getGlobalData
} from "../../utils/reuseFunc"
const app = getApp();
Page({
  data: {
    address: [],
    orderstatus: '',
    payType: [],
    type: '',
    tid: '',
    note: '',
    cartList: [],
    payStatus: '', // 支付状态
    waterNum: 0, // 水票数量
    emptyBarrelNumber: 0, // 原有空桶数量
    addEmptyBarrelNumber: 0, // 增加的空桶数量
    setYajin: 0, // 每个桶的押金 金额
    emptyBarrelMoney: 0, // 空桶押金总计
    setNum: 0, // 商家设置最小起送数量
  },
  onLoad(e) {
    this.getGlobalData('themeColor'); //获取主题配色
  },
  onShow() {
    this.requestpayType();
    wx.getStorage({
      key: 'submitOrder',
      success: (res) => {
        this.setData({
          address: Object.keys(res.data.address).length > 0 ? res.data.address : [],
          cartList: res.data.goods,
          tid: res.data.trade.tid,
          waterNum: res.data.waterNum,
          emptyBarrelNumber: res.data.tongkongNum,
          setNum: res.data.setNum,
          setYajin: res.data.setYajin,
        })
      }
    })
  },
  getGlobalData,
  // 获取支付方式
  requestpayType() {
    wx.$get({
      map: 'applet_pay_cfg'
    }).then(res => {
      this.setData({
        payType: res.data,
        type: res.data[0].type
      })
    }).catch(err => {
      this.setData({
        payType: []
      })
    })
  },
  // 选择支付方式
  payRadioChange(e) {
    this.setData({
      type: e.detail.value
    })
  },
  toRecharge() {
    wx.navigateTo({
      url: '/subpages/walletRecharge/walletRecharge',
    })
  },
  // 选择收货地址
  chooseAddress(e) {
    if (this.data.payStatus == 'dzf') {
      return
    }
    var that = this;
    var type = e.currentTarget.dataset.type;
    var orderStatus = that.data.orderstatus;
    if (!orderStatus) {} else {
      return;
    }
    wx.navigateTo({
      url: '/subpages/addressManage/addressManage?type=' + type
    })
  },
  // 提交订单
  submitOrder() {
    // app.getSubId('applet_order_confirm').then(res => {

    // });
    if (this.data.cartList[0].num < this.data.setNum) return wx.$showToast(`最小起送数量为${this.data.setNum}`);
    if (this.data.address.length <= 0) return wx.$showToast('请添加收货地址');
    let params = {
      map: 'applet_goodswater_myorder_submit',
      tid: this.data.tid,
      note: this.data.note,
      payType: this.data.type,
      tongNum: this.data.addEmptyBarrelNumber, // 使用空桶
      address: JSON.stringify(this.data.address),
      addressId: this.data.address.id,
      waterNum: this.data.cartList[0].num,
      // yajintongNum: this.data.addEmptyBarrelNumber > 0 ? this.data.emptyBarrelNumber : this.data.cartList[0].num // 使用押金桶数量
    }
    wx.$get(params).then(res => {
      if (res.data.status == 'zfcg') {
        wx.navigateTo({
          url: `/pages/waterPaySuccess/waterPaySuccess?tid=` + this.data.tid,
        })
      } else {
        this.setData({
          payStatus: res.data.status
        })
      }
    })
  },
  // 去支付
  orderPay() {
    let params = {
      map: 'applet_order_pay',
      tid: this.data.tid
    }
    wx.$get(params).then(res => {
      return app.commonRequestPayment(res.data);
    }).then(res => {
      wx.navigateTo({
        url: `/pages/waterPaySuccess/waterPaySuccess?tid=` + this.data.tid,
      })
    })
  },
  // 加减水
  minusAndadd(e) {
    let {
      index,
      type
    } = e.currentTarget.dataset
    let num = type == 'add' ? ++this.data.cartList[index].num : --this.data.cartList[index].num
    if (num > this.data.waterNum) return wx.$showToast('剩余水票不足');
    this.setData({
      [`cartList[${index}].num`]: num
    })
    let addEmptyBarrelNumber = this.data.cartList[index].num - this.data.emptyBarrelNumber // 增加的空桶数量
    // let waterTicket =  this.data.cartList[index].num - this.data.waterNum// 超出剩余水票的数量
    if (addEmptyBarrelNumber >= 0) {
      this.setData({
        addEmptyBarrelNumber: addEmptyBarrelNumber,
        emptyBarrelMoney: addEmptyBarrelNumber * this.data.setYajin,
      })
    }
  },
  // 备注
  remark(e) {
    this.setData({
      note: e.detail.value
    })
  }
})