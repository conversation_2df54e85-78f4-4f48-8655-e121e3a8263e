// components/navbar/index.js
const app = getApp();
Component({
  properties: {
    
  },
  data: {

  },
  attached: function () {
    var that = this,
        menu = app.globalData.menuTitle,
        tabbarPage = [];
    for (var i in menu) {
      tabbarPage.push(i);
    }
    var path = getCurrentPages()[getCurrentPages().length - 1].__route__,
        isTabPage = false;
    for (var i=0;i<tabbarPage.length;i++){
      if (tabbarPage[i] == path){
        isTabPage = true;
      }
    }
    that.setData({
      isTabPage: isTabPage
    })
    var enterFrom = app.globalData.enterFrom;
    if(enterFrom=='share'){
      that.setData({
        showHome:true
      })
    }
    if (!isTabPage){
      that.setData({
        showSy:true
      })
    }
  },
  methods: {
    navBack: function () {
      wx.navigateBack({
        delta: 1
      })
    },
    toIndex: function () {
      app.globalData.enterFrom = '';
      app.backHome();
    },
  }
})