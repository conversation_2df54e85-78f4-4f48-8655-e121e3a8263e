<view class="skeleton-wrap index-skeleton" wx:if="{{type=='index'}}">
  <view class='swiper-block'></view>
  <view class="nav-wrap flex-sp-wrap">
    <view class="nav-item" wx:for="{{10}}" wx:key="this">
      <view class="nav-img"></view>
      <view class="nav-tit"></view>
    </view>
  </view>
  <view class="block-tit"></view>
  <view class="goods-wrap flex-sp-wrap">
    <view class="goods-item" wx:for="{{4}}" wx:key="this">
      <view class="goods-img"></view>
      <view class="goods-content">
        <view class="goods-tit"></view>
        <view class="goods-tit"></view>
        <view class="price-wrap flex-sp">
          <view>
            <view class="price-info"></view>
          </view>
          <view class="buy-btn"></view>
        </view>
      </view>
    </view>
  </view>
</view>
<view class="skeleton-wrap goods-skeleton" wx:if="{{type=='goodsDetail'}}">
  <view class="goods-tab flex-sp">
    <view class="tab-item"></view>
    <view class="tab-item"></view>
    <view class="tab-item"></view>
  </view>
  <view class="swiper-block">
  </view>
  <view class="goods-info">
    <view class="goods-tit"></view>
    <view class="goods-tit half-width"></view>
    <view class="flex-sp price-wrap">
      <view class="goods-price"></view>
      <view class="goods-trans"></view>
    </view>
    <view class="goods-tit"></view>
    <view class="goods-tit"></view>
  </view>
  <view class="bottom-opertaion flex-sp">
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="flex-wrap">
      <view class="oper-btn"></view>
      <view class="oper-btn red-bg"></view>
    </view>

  </view>
</view>
<view class="skeleton-wrap goods-skeleton" wx:if="{{type=='orderDetail'}}">
  <view class="header-box">
    <view class="header-box-item"></view>
  </view>
  <view class="goods-tab flex-sp">
    <view class="tab-item"></view>
    <view class="tab-item"></view>
    <view class="tab-item"></view>
  </view>
  <view class="goods-info goods-box">
    <view class="goods-tit goods-b"></view>
    <view class="goods-tit goods-b"></view>
    <view class="goods-tit goods-b"></view>
  </view>
  <view class="order-box">
    <view class="order-tit"></view>
    <view class="order-box-item"></view>
  </view>
  <view class="goods-info">
    <view class="goods-tit"></view>
    <view class="goods-tit half-width"></view>
    <view class="goods-tit"></view>
    <view class="goods-tit"></view>
  </view>
  <view class="bottom-opertaion flex-sp">
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="flex-wrap">
      <view class="oper-btn"></view>
      <view class="oper-btn red-bg"></view>
    </view>

  </view>
  <!-- <view class="goods-tab flex-sp">
   <view class="tab-item"></view>
   <view class="tab-item"></view>
   <view class="tab-item"></view>
  </view>
  <view class="swiper-block">
  </view>
  <view class="goods-info">
    <view class="goods-tit"></view>
    <view class="goods-tit half-width"></view>
    <view class="goods-tit"></view>
    <view class="goods-tit"></view>
  </view>
  <view class="bottom-opertaion flex-sp">
    <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
     <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
     <view class="icon-wrap">
      <view class="oper-icon"></view>
      <view class="oper-text"></view>
    </view>
    <view class="flex-wrap">
      <view class="oper-btn"></view>
      <view class="oper-btn red-bg"></view>
    </view>
    
  </view> -->
</view>