/* pages/skeleton/skeleton.wxss */
.flex-sp-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.flex-sp {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.index-skeleton {
  background: #fff;
  padding: 20rpx;
}

.index-skeleton .swiper-block {
  width: 710rpx;
  height: 240rpx;
  border-radius: 20rpx;
  background: #f7f7f7;
}

.index-skeleton .nav-wrap {
  margin: 20rpx 0;
}

.index-skeleton .nav-item {
  margin: 20rpx;
}

.index-skeleton .nav-item .nav-img {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: #f7f7f7;
}

.index-skeleton .nav-item .nav-tit {
  margin: 10rpx auto 0;
  width: 85rpx;
  height: 25rpx;
  background: #f7f7f7;

}

.index-skeleton .goods-wrap {}

.index-skeleton .block-tit {
  height: 80rpx;
  background: #f7f7f7;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.index-skeleton .goods-item {
  margin-bottom: 20rpx;
  width: 350rpx;

}

.index-skeleton .goods-img {
  height: 350rpx;
  background: #f7f7f7;
  border-radius: 10rpx;
}

.index-skeleton .goods-content {

  margin-top: 2rpx;
  height: 110rpx;
  border-radius: 10rpx;

}

.index-skeleton .goods-content .goods-tit {
  margin: 10rpx auto;
  height: 20rpx;
  background: #f7f7f7;

}

.goods-b {
  margin-bottom: 30rpx !important;
}

.index-skeleton .goods-content .price-info {
  margin: 5rpx 0;
  width: 200rpx;
  height: 20rpx;
  background: #f7f7f7;
}

.index-skeleton .goods-content .buy-btn {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #f7f7f7;
}

/* 商品详情 */
/* pages/goods/goods.wxss */
.flex-sp-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.flex-sp {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-wrap {
  display: flex;
  align-items: center;
}

.goods-skeleton {
  position: relative;
  height: 100vh;
  background: #fff;

}

.goods-skeleton .goods-tab {
  padding: 0 50rpx;
  height: 80rpx;
  /* box-shadow: 0 1rpx 2rpx #999; */
}

.goods-skeleton .tab-item {
  width: 120rpx;
  height: 30rpx;
  border-radius: 15rpx;
  background: #f7f7f7;

}

.goods-skeleton .swiper-block {
  background: #f7f7f7;
  height: 750rpx;
}

.goods-skeleton .goods-info {
  padding: 0 20rpx;
}

.goods-skeleton .goods-box {
  margin-bottom: 100rpx;
}

.goods-skeleton .goods-tit {
  margin: 20rpx 0;
  height: 30rpx;
  background: #f7f7f7;
}

.goods-skeleton .goods-tit.half-width {
  width: 450rpx;
}

.goods-skeleton .goods-price {
  width: 200rpx;
  height: 30rpx;
  background: rgba(255, 130, 50, .5);
}

.goods-skeleton .goods-trans {
  width: 100rpx;
  height: 30rpx;
  background: #f7f7f7;
}

.goods-skeleton .bottom-opertaion {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding-left: 30rpx;
  height: 100rpx;
  box-shadow: -1rpx 0 9rpx #e5e5e5;
}

.goods-skeleton .oper-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #f7f7f7;
}

.goods-skeleton .oper-btn {
  width: 210rpx;
  height: 100rpx;
  background: rgba(255, 130, 50, .5);
}

.goods-skeleton .oper-text {
  width: 50rpx;
  height: 20rpx;
  margin: 10rpx auto 0;
  border-radius: 10rpx;
  background: #f7f7f7;
}

.goods-skeleton .oper-btn.red-bg {
  background: rgba(255, 34, 0, .5);
}

.header-box {
  width: 100%;
  height: 160rpx;
  background-color: #fff;
  box-sizing: border-box;
  padding: 25rpx 20rpx;
}

.header-box .header-box-item {
  width: 100rpx;
  height: 100rpx;
  background-color: #f7f7f7;
  border-radius: 4rpx;
}

.order-box {
  margin-bottom: 100rpx;
  padding: 0 20rpx;
}

.order-box .order-tit {
  margin: 20rpx 0;
  height: 30rpx;
  background: #f7f7f7;
}

.order-box .order-box-item {
  width: 180rpx;
  height: 180rpx;
  border-radius: 20rpx;
  background: #f7f7f7;
}