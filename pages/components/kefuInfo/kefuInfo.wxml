<!-- 客服验证判断 -->
<!-- <view class="contact-btn" wx:if="{{serviceSetting.customerMobile==0||(serviceSetting.customerMobile==1&&serviceSetting.mobile!='')}}"> -->
<view class="contact-btn">
  <view class="contact-tip" wx:if="{{serviceSetting.tipOpen==1}}" data-tips="{{serviceSetting.tips}}" bindtap="replyTip"></view>
  <button class="contact-btn" open-type="getPhoneNumber" wx:elif="{{serviceSetting.customerMobile==1&&contactPhone==''}}" catchgetphonenumber="getPhoneNumber"> </button>
  <button class="contact-btn" open-type="contact" send-message-title="{{sendMessage&&sendMessage.title?sendMessage.title:''}}" send-message-path="{{sendMessage&&sendMessage.path?sendMessage.path:''}}" send-message-img="{{sendMessage&&sendMessage.img?sendMessage.img:''}}" show-message-card="{{sendMessage?true:false}}" session-from="{{sessionForm}}" wx:else bindcontact="contactRecord"> </button>
</view>