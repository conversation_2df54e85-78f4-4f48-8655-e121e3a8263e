// components/navbar/index.js
import  { getPhone ,contactRecord} from "../../../api/reuseRequest"
import {setNavColor} from "../../../utils/reuseFunc"
const app = getApp();
Component({
  properties: {
    serviceSetting: {
      type: Object,
      value: '',
      observer:''
    },
    sendMessage: {
      type: Object,
      value: ''
    }
  },
  data: {
    sessionForm: app.globalData.sessionForm ? app.globalData.sessionForm:''
  },
  pageLifetimes: {
    show: function () {
      var that = this;
      // 页面被展示
      that.setData({
        contactPhone: app.globalData.contactPhone
      })
      setNavColor.call(this);
    },
    hide: function () {
      // 页面被隐藏
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  },
  attached: function () {
    var that = this;
    that.setData({
      contactPhone: app.globalData.contactPhone
    })
    setNavColor.call(this);
  },
  methods: {
    getPhoneNumber: function (e) {
      getPhone.call(this)
    },
    contactRecord: function (e) {
      var that = this;
      that.setData({
        isShowkfreply:false
      })
     contactRecord(e);
    },
    replyTip:function(e){
      var that = this,
          tips = e.currentTarget.dataset.tips,
          replydata = {
        replyTips: tips,
        isShowkfreply: true,
        customerMobile: that.data.serviceSetting.customerMobile,
        contactPhone: that.data.contactPhone
      };
      that.setData(replydata)
      that.triggerEvent('getreply', replydata)
    },
    hideTip:function(){
      var that = this;
      that.setData({
        isShowkfreply: false
      })
    }
  }
})