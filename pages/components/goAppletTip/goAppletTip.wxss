/* 添加到桌面样式 */
.add-desktop-tip{
  position: fixed;
  bottom:40rpx;
  right: 20rpx;
  border-radius: 12rpx;
  background-color: #32302E;
  line-height: 1.4;
  z-index: 10000001;
  color: #FDE5AB;
  padding: 25rpx;
  font-size: 32rpx;
  white-space: nowrap;
}
.add-desktop-tip:before{
  content:'';
  position:absolute;
  top:100%;
  right:80rpx;
  border-width:20rpx;
  border-style: solid dashed dashed dashed;
  border-color:#32302E transparent transparent transparent;
  z-index:1;
}
/*图片渐入效果*/
@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
.fade_in {
    animation: fadeIn 1s both;
}