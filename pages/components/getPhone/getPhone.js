// pages/components/getPhone/getPhone.js
import { getCode } from  "../../../utils/login";
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {

  },
  /**
   * 组件的初始数据
   */
  data: {

  },
  attached: function () {
    // 在组件实例进入页面节点树时执行
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 获取手机号
    getPhoneNumber: function (e) { 
      let detail = e.detail;
      if(!(detail.encryptedData&&detail.iv))return;
      getCode().then(res=>{
        wx.$get({
          map: 'applet_bind_member_mobile',
          code: res.code,
          encryptedData: detail.encryptedData||'',
          iv: detail.iv||''
        }).then(res => {
          let mobile = res.data;
          this.triggerEvent('complete', {mobile:mobile}) 
        }).catch(err => {})
      }).catch(err=>{})
    },
  }
})
