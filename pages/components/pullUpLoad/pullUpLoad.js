// pages/components/pullUpLoad/pullUpLoad.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // wrapHeight:{
    //   type:Number,
    //   value:920
    // },
    wrapHeight:{
      type:String,
      value:'0'
    },
    triggerDistance:{
      type:Number,
      value:200
    },
    showTip:{
      type:Boolean,
      value:true
    },
    vertical:{
      type:Boolean,
      value:true
    },
    canMove:{ 
      type:Boolean,
      value:false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    verMoveDistance:0,
    honMoveDistance:0,
    transTime:'all 0'
  },
  attached:function(){

  },
  /**
   * 组件的方法列表
   */
  methods: {
    touchmoveEvent(e){
      if(this.data.vertical){
        // console.log("vertical",e.changedTouches[0].pageY,this.data.startPageY)
        var distance = e.changedTouches[0].pageY - this.data.startPageY,type='verMoveDistance';
        if(distance>0)return;
        // console.log('向上滑动距离',distance)
        
      } else{
        // console.log("vertical",e.changedTouches[0].pageY,this.data.startPageY)
        var distance = e.changedTouches[0].pageX - this.data.startPageX,type='honMoveDistance';
        if(distance>0)return;
        // console.log('向上滑动距离',distance)
        
      }
      this.setData({
        transTime:"all 0",
        [type]:distance
      })
      
      
    },
    touchstartEvent(e){
      if(this.data.vertical){
        var startPosition=e.changedTouches[0].pageY,type='startPageY'
      }else{
        var startPosition=e.changedTouches[0].pageX,type='startPageX'
      }
      this.setData({
        [type]:startPosition
      })
    },
    touchendEvent(e){
      let that=this,triggerDistance = this.data.triggerDistance;
      var type=this.data.vertical?'verMoveDistance':'honMoveDistance'
      
      var moveDistance=this.data[type]
      if(Math.abs(moveDistance)>=triggerDistance){
        this.triggerEvent('pullUpEnd')
        that.setData({
          [type]:0
        }) 
      }else{
        that.setData({
          transTime:"all .5s",
        },function(){
          that.setData({
            [type]:0
          }) 
        })
      }
      
      
    },
  }
})
