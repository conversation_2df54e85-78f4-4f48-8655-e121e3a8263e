/* pages/components/pullUpLoad/pullUpLoad.wxss */
.pull-up-wrap{
 position: relative;
 /* height: 100%; */
}
.pull-up-wrap .pull-tip{
  width: 100%;
  text-align: center;
  line-height: 80rpx;
  height: 80rpx;
  font-size: 24rpx;
  color: #999;
}
.icon-load{
  /* position: absolute;
  left: 50%;
  bottom: -40rpx;
  transform: translateX(-50%); */
  margin: 0 auto;
  display: block;
  width: 42rpx;
  height: 42rpx;
  transition: all .1s;
  
}
.circle{
  transform: rotate(180deg);
  
}