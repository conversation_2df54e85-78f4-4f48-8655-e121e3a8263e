<view class="pull-up-wrap" style="min-height:{{wrapHeight}};transform:translateY({{verMoveDistance}}rpx) translateX({{honMoveDistance}}rpx);transition:{{transTime}};" bindtouchmove="{{canMove?'touchmoveEvent':''}}" bindtouchend="{{canMove?'touchendEvent':''}}" bindtouchstart="{{canMove?'touchstartEvent':''}}">
  <slot></slot>
  <block wx:if="{{vertical}}">
    <view  class="pull-tip">
      <text hidden="{{!(showTip&&verMoveDistance>-triggerDistance)}}">上拉切换到下一分类~</text>
      <text hidden="{{verMoveDistance>-triggerDistance}}">释放切换到下一分类</text>
    </view>
    <image hidden="{{verMoveDistance>-triggerDistance}}" class="icon-load" style="transform:rotate({{-verMoveDistance*3}}deg)" src="/images/load-refresh.png"></image>
  </block>
  <view class="pull-tip" wx:else>左右滑动切换分类~</view>
</view>