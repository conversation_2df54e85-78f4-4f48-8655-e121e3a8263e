if (wx.onNeedPrivacyAuthorization) {
  wx.onNeedPrivacyAuthorization(resolve => {
    console.log('有隐私接口触发授权')
  })
}
Component({
  data: {
    privacyContractName: '',
    showPrivacy: false
  },
  pageLifetimes: {
    show() {
      wx.getPrivacySetting({
        success: res => {
          console.log('getPrivacySetting', res)
          if (res.errMsg == 'getPrivacySetting:ok') {
            this.setData({
              privacyContractName: res.privacyContractName,
              showPrivacy: res.needAuthorization
              // showPrivacy: true
            })
          }
        }
      })
    }
  },
  methods: {
    // 打开隐私协议页面
    openPrivacyContract() {
      wx.openPrivacyContract({
        fail: () => {
          wx.showToast({
            title: '遇到错误',
            icon: 'error'
          })
        }
      })
    },
    // 拒绝隐私协议
    exitMiniProgram() {
      // 直接退出小程序
      wx.exitMiniProgram()
    },
    // 同意隐私协议
    handleAgreePrivacyAuthorization() {
      this.setData({
        showPrivacy: false
      })
      this.triggerEvent('initialize')
    }
  }
})
