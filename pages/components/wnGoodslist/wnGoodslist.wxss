/* pages/components/wnGoodslist/wnGoodslist.wxss */
@import "../../../app.wxss";
.wn-box{
  height: 100vh;
  background-color: #f5f6f7;
}
.search-wrap {
  padding: 10rpx 20rpx;
  background-color: #fff;
}

.search-box1 {
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 35rpx;
}

.search-box1 image {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  vertical-align: middle;
  position: relative;
  top: -2rpx;
}

.search-box1 text {
  font-size: 28rpx;
  color: #999;
  vertical-align: middle;
  letter-spacing: 2rpx;
}

.search-box2 {
  height: 80rpx;
  line-height: 80rpx;
  background-color: #E0E0E0;
  border-radius: 35rpx;
  padding: 0 30rpx 0 20rpx;
}

.search-box2 input {
  padding: 0;
  color: #fff;
}

.search-box2 .placeholder {
  color: #fff;
}

.search-box2 .search-btn image {
  display: block;
  width: 48rpx;
  height: 48rpx;
  margin: 16rpx 0;
}

/* tab切换栏 */
/* .tab-title-zhanwei { height: 180rpx; } */
.top-part {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
}

.tab-title {
  height: 90rpx;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 4rpx 10rpx #e8e8e8;
  position: relative;
}

.tab-title scroll-view {
  width: 680rpx;
  white-space: nowrap;
}

.tab-title scroll-view .tab-item {
  display: inline-block;
  min-width: 18%;
  text-align: center;
  font-size: 30rpx;
  color: #5d5d5d;
  padding: 0 10rpx;
  box-sizing: border-box;
}

.tab-title scroll-view .tab-item text {
  display: inline-block;
  height: 90rpx;
  line-height: 90rpx;
}

.tab-title scroll-view .tab-item.active text {
  box-sizing: border-box;
  border-bottom: 6rpx solid #ff8132;
  color: #ff8132;
}

.tab-title .more-wrap {
  height: 78rpx;
  text-align: center;
  width: 90rpx;
  position: absolute;
  top: 6rpx;
  right: 0;
  /* padding:10rpx 0; */
}

.tab-title .more-wrap image.icon_more {
  display: block;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx 30rpx;
  transform: rotate(90deg);
}

.tab-title .more-type {
  padding: 10rpx 0;
}

.tab-title .more-type text {
  font-size: 24rpx;
  color: #60AFFE;
}

.tab-title .more-type image {
  display: block;
  width: 31rpx;
  height: 12rpx;
  margin: 0 auto;
}

.good-list-wrap {
  /* background-color: #fff; */
  /* height: 100vh; */
  background-color: #f5f6f7;
  padding-top: 180rpx;
  box-sizing: border-box;
}

.good-list {
  padding: 10rpx 1.5% 0;
  overflow: hidden;
  box-sizing: border-box;
}

.price-buy {
  font-size: 24rpx;
  line-height: 1.2;
  color: #ff2422;
}

.price-buy .now-price {
  font-size: 32rpx;
}

.price-buy .origin-price {
  margin-left: 20rpx;
  color: #999;
}

.buy-btn {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  /*   height: 46rpx; */
  /*   line-height: 48rpx; */
  padding: 5rpx 15rpx;
  min-width: 60rpx;
  text-align: center;
  -webkit-border-radius: 30rpx;
  border-radius: 30rpx;
  background-color: #ff8132;
  color: #fff;
  font-size: 26rpx;
}

.good-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* font-size: 32rpx;
  line-height: 1.5;
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; */
}

.good-image {
  display: block;
  height: 710rpx;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  position: relative;
}

.good-image .no-good {
  width: 120rpx;
  height: 120rpx;
  line-height: 120rpx;
  font-size: 26rpx;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -60rpx;
  letter-spacing: 1rpx;
  z-index: 10;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}

.good-image .vip-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 80rpx;
  height: 80rpx;
}

.good-image .img {
  display: block;
  height: 100%;
  width: 100%;
}

.good-intro {
  background-color: #fff;
  padding: 20rpx 0 10rpx;
  position: relative;
}

.good-view1 .item-wrap {
  padding: 0 1%;
}

.good-view1 .good-item {
  margin-bottom: 20rpx;
}

.good-view2 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

.good-view2 .good-item {
  width: 50%;
  /* float: left; */
  padding: 8rpx;
  box-sizing: border-box;
}

.good-view2 .good-intro .price-buy {
  margin-top: 16rpx;
}

.good-view2 .good-intro .price-buy .buy-btn {
  position: absolute;
  right: 0;
  bottom: 10rpx;
}

.good-view3 .good-item {
  width: 50%;
  float: left;
  margin-bottom: 14rpx;
}

.good-view3 .item-wrap {
  padding: 0 1.6%;
}

.good-view3 .good-item:nth-of-type(3n+1) {
  width: 100%;
}

.good-view3 .good-item:nth-of-type(3n+1) .item-wrap {
  padding: 0 1%;
}

.good-view1 .good-item:last-child {
  margin-bottom: 0;
}

.good-view2 .good-image {
  height: 346rpx;
  padding: 0;
}

.good-view2 .good-title {
  font-size: 30rpx;
  margin-bottom: 8rpx;
}

.good-view3 .good-item .good-image {
  height: 350rpx;
}

.good-view3 .good-item:nth-of-type(3n+1) .good-image {
  height: 710rpx;
}

.good-view4 {
  padding: 10rpx 2.5% 0;
}

.good-view4 .good-item {
  overflow: hidden;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.good-view4 .good-item .good-image {
  height: 260rpx;
  width: 260rpx;
  float: left;
}

.good-view4 .good-intro {
  background-color: #fff;
  padding: 10rpx 20rpx 15rpx;
  position: relative;
  margin-left: 250rpx;
  box-sizing: border-box;
  height: 260rpx;
}

.good-view4 .good-intro .good-title {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
  line-height: 1.4;
}

.good-view4 .good-intro .price-buy {
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 100%;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
}

.good-view4 .good-intro .price-buy .buy-btn {
  position: absolute;
  right: 0;
  bottom: 10rpx;
}

/* 商品类型的更多功能遮罩 */
.type-model {
  background-color: rgba(0, 0, 0, 0.4);
  position: fixed;
  top: 90rpx;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

.model-con {
  background-color: #fff;
  position: fixed;
  top: 90rpx;
  left: 0;
  right: 0;
  z-index: 11;
}

.model-con .model-title {
  padding: 0;
}

.model-con .model-title .tishi-text {
  color: #333333;
  font-size: 30rpx;
  padding-left: 20rpx;
}

.model-con .model-title .close-btn {
  width: 80rpx;
  height: 90rpx;
}

.model-con .model-title .close-btn image {
  display: block;
  width: 100%;
  height: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  transform: rotate(-90deg);
}

.model-con .type-labels {
  padding: 0 12rpx 15rpx;
  max-height: 660rpx;
  overflow: auto;
  font-size: 0;
}

.model-con .type-labels .label-box {
  display: inline-block;
  width: 25%;
  text-align: center;
  padding: 12rpx;
  box-sizing: border-box;
}

.model-con .type-labels .label-box text {
  height: 64rpx;
  line-height: 64rpx;
  color: #333;
  display: block;
  box-sizing: border-box;
  margin: 0 auto;
  overflow: hidden;
  white-space: nowrap;
  font-size: 28rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
}

.model-con .type-labels .label-box.active text {
  border-color: #ff8132;
  color: #ff8132;
}

.common-style text {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}