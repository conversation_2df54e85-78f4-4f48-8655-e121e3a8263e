<!--pages/components/wnGoodslist/wnGoodslist.wxml-->
<!--pages/wnGoodsList/wnGoodsList.wxml-->
<!-- <view class="search-wrap">
  <view class="search-box1" bindtap="searchShow">
    <image src="/images/search.png" mode="aspectFit"></image>
    <text>搜索</text>
  </view> 
  <view class="search-box2 flex-wrap" wx:if="{{searchShow}}">
    <view class="input-box flex-con">
      <input type="text" auto-focus placeholder="请输入搜索关键字" placeholder-class="placeholder" value="{{content}}" bindinput="searchChange"></input>
    </view>
    <view class="search-btn" bindtap="searchGoods">
      <image src="/images/search.png" mode="aspectFit"></image>
    </view>
  </view>
</view> -->
<view class="wn-box">
  <view class="tab-title-zhanwei" wx:if="{{category.length>0}}">
    <view class="top-part" style="top:{{navHeight}}px">
      <view class="search-wrap">
        <view class="search-box1" bindtap="searchPage">
          <image src="/images/sousuo.png" mode="aspectFit"></image>
          <text>搜索</text>
        </view>
      </view>
      <view class="tab-title">
        <scroll-view scroll-x scroll-with-animation scroll-into-view="{{toViewCategoryId}}">
          <block wx:key="index" wx:for="{{category}}">
            <view class="tab-item {{categoryId==item.id?'active':''}}" id="category{{index}}" data-categoryid="{{item.id}}" bindtap="toggleChange" data-index="{{index}}">
              <text style="color:{{categoryId==item.id?themeColor1:'#333'}};border-color:{{categoryId==item.id?themeColor1:'#fff'}};">{{item.name}}</text>
            </view>
          </block>
        </scroll-view>
        <view class="more-wrap" bindtap="openModel" wx:if="{{category}}">
          <!-- <view class="more-type border-l">
          <text>更多</text>
          <image src="/images/icon_xia.png" mode="aspectFit"></image>
        </view> -->
          <image src="/images/food-jt.png" class="icon_more" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
  <view class="good-list-wrap">
    <pull-up-load wrap-height="100%" show-tip="{{(showLoading||shopGoods.length<=0)?false:true}}" can-move="{{noMoretip}}" bindpullUpEnd="pullUpEnd">
      <!-- 商品列表 -->
      <view class="no-data" style="padding:200rpx 0;" wx:if="{{shopGoods.length<=0}}">
        <image src="/images/empty_img.png"></image>
        <text>暂无对应商品哦~</text>
      </view>
      <view class="good-list good-view2" wx:if="{{shopGoods.length>0}}">
        <block wx:key="index" wx:for="{{shopGoods}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap="openGoodDetail">
            <view class="item-wrap">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <!-- xs -- 修改 -->
                <view class="good-title {{fontsize_status == 1?'common-style':''}}"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
                <view class="price-buy {{fontsize_status == 1?'common-style':''}}">
                  <text>￥</text> <text class="now-price">{{good.price}}</text>
                  <text class="origin-price" wx:if="{{good.soldShow==1}}">销量{{good.sold}}</text>
                  <text class="buy-btn" style="background-color:{{themeColor1?themeColor1:navColor}};">购买</text>
                </view>
                <!-- <text class="buy-btn">购买</text> -->
              </view>
            </view>
          </view>
        </block>
      </view>
      <!--上拉加载提示-->
      <view class="loading-tip" wx:if="{{showLoading}}">
        <view class="icon_load">
          <view id="floatingBarsG">
            <view class="blockG" id="rotateG_01"></view>
            <view class="blockG" id="rotateG_02"></view>
            <view class="blockG" id="rotateG_03"></view>
            <view class="blockG" id="rotateG_04"></view>
            <view class="blockG" id="rotateG_05"></view>
            <view class="blockG" id="rotateG_06"></view>
            <view class="blockG" id="rotateG_07"></view>
            <view class="blockG" id="rotateG_08"></view>
          </view>
        </view>
        <text>努力加载中...</text>
      </view>
      <!-- <view class="nomore-tip" wx:if="{{noMoretip&&shopGoods.length>0}}">没有更多数据了</view> -->
    </pull-up-load>
  </view>
  <!-- 商品类型的更多功能遮罩 -->
  <view class="type-model" wx:if="{{modelShow}}" bindtap="closeModel" style="top:{{navHeight}}px"></view>
  <view class="model-con" wx:if="{{modelShow}}" style="top:{{navHeight}}px">
    <view class="model-title flex-wrap">
      <view class="tishi-text flex-con">全部分类</view>
      <view class="close-btn" bindtap="closeModel">
        <image src="/images/food-jt.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="type-labels">
      <block wx:key="index" wx:for="{{category}}">
        <view class="label-box {{categoryId==item.id?'active':''}}" data-model="model" data-categoryid="{{item.id}}" bindtap="toggleChange">
          <text style="color:{{categoryId==item.id?themeColor1:'#333'}};border-color:{{categoryId==item.id?themeColor1:'#fff'}};">{{item.name}}</text>
        </view>
      </block>
    </view>
  </view>
</view>