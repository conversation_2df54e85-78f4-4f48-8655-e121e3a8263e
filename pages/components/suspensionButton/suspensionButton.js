Component({
  properties: {
    type: {
      type: String,
      value: ''
    }
  },
  methods: {
    getWater(e) {
      let type = e.currentTarget.dataset.type
      if (type == '1') return this.toPath('/subpages1/oneKeyGetWater/oneKeyGetWater', type)
      if (type == '2') return this.toPath('/pages/orderWaterStore/orderWaterStore', type)
      if (type == '3') return this.toPath('/subpages1/myOrderWater/myOrderWater', type)
    },
    toPath(url, type) {
      // if (type == 1 || type == 3) {
      //   wx.redirectTo({
      //     url: url
      //   })
      //   return
      // }
      // if (wx.getStorageSync('menuJump') == 1) {
      //   wx.switchTab({
      //     url: url
      //   })
      //   return
      // }
      // wx.redirectTo({
      //   url: url,
      // })
      wx.navigateTo({
        url: url,
      })
    },
    bindtouchmove(e) {
      console.log(e)
    }
  }
})