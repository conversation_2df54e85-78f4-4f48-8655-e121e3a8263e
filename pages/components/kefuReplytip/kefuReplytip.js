// components/navbar/index.js
import { getGlobalData,setNavColor,contactRecord } from "../../../utils/reuseFunc"
import  { getPhone } from "../../../api/reuseRequest"
const app = getApp();
Component({
  properties: {
    isShowkfreply: {
      type: Boolean,
      value: false
    },
    replyTips:{
      type: String,
      value: false
    },
    customerMobile: {
      type: String,
      value:''
    },
    contactPhone: {
      type: String,
      value:''
    },
    sendMessage: {
      type: Object,
      value: ''
    }
  },
  data: {
    sessionForm: app.globalData.sessionForm ? app.globalData.sessionForm:''
  },
  attached: function () {
    var that = this;
    this.getGlobalData('themeColor');//获取主题配色
    that.setData({
      contactPhone: app.globalData.contactPhone
    })
    setNavColor.call(this);
  },
  methods: {
    getGlobalData,
    getPhoneNumber: function (e) {
      var that = this;
      getPhone.call(this)
    },
    contactRecord: function (e) {
      var that = this;
      that.setData({
        isShowkfreply:false
      })
      contactRecord(e);
    },
    hideTip:function(){
      var that = this;
      that.setData({
        isShowkfreply: false
      })
    }
  }
})