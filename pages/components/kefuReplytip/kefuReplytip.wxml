<!-- 客服回复弹窗提示 -->
<view class="reply-modal-mask" wx:if="{{isShowkfreply}}" bindtap="hideTip"></view>
<view class="reply-modal" wx:if="{{isShowkfreply}}">
  <view class="reply-title">客服提示</view>
  <text class="reply-tips">{{replyTips}}</text>
  <view class="kefu-btn" style="background-color:{{themeColor1?themeColor1:navColor}};">立即回复<button class="contact-btn" open-type="getPhoneNumber" wx:if="{{customerMobile==1&&contactPhone==''}}" catchgetphonenumber="getPhoneNumber"> </button><button class="contact-btn" open-type="contact" send-message-title="{{sendMessage&&sendMessage.title?sendMessage.title:''}}" send-message-path="{{sendMessage&&sendMessage.path?sendMessage.path:''}}" send-message-img="{{sendMessage&&sendMessage.img?sendMessage.img:''}}" show-message-card="{{sendMessage?true:false}}" session-from="{{sessionForm}}" bindcontact="contactRecord" wx:else> </button></view>
</view>