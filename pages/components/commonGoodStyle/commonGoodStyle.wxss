/* pages/components/commonGoodStyle/commonGoodStyle.wxss */
.flex-sp{display: flex; align-items: center;justify-content: space-between;}
.flex-bm{display: flex; align-items:baseline;}
.flex-wrap {display: flex; align-items: center; }
.flex-wrap1 {display: flex; }
.flex-con {flex: 1;min-width: 0; }
/* 分类商品组件-三张图片展示样式 */
.good-wrap{
  padding:20rpx 0 0;
}
.good-wrap .good-item{
  background-color:#fff;
  padding:25rpx 20rpx;
  box-shadow:0 4rpx 10rpx #eee;
  margin-bottom:16rpx;
}
.good-wrap .title-wrap .title{
  color:#333;
  min-width:0;
  display: -webkit-box !important; 
  overflow: hidden;text-overflow:ellipsis;
  word-break:break-all;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
  padding-right:20rpx;
  font-weight:1000;
}
.good-wrap .title-wrap .hot-num image{
  width:30rpx;
  height:30rpx;
  vertical-align: middle;
  margin-right:10rpx;
  margin-top:-3rpx;
}
.good-wrap .title-wrap .hot-num text{
  color:#999;
  font-size:24rpx;
  vertical-align: middle;
}
.good-wrap .label-wrap{
  padding-top:10rpx;
  font-size:0;
}
.good-wrap .label-wrap .label{
  display: inline-block;
  padding:5rpx 20rpx;
  font-size:22rpx;
  color:#F1522B;
  border:1px solid #F1522B;
  border-radius:30rpx;
  margin:0 15rpx 10rpx 0;
  letter-spacing: 1rpx;
}
.good-wrap .imgs-wrap{
  position:relative;
  margin-top:15rpx;
  font-size:0;
}
.good-wrap .imgs-wrap .img-box{
  display:inline-block;
  width:222rpx;
  height:222rpx;
  margin-right:15rpx;
  border-radius:4rpx;
}
.good-wrap .imgs-wrap .img-box:last-child{
  margin-right:0;
}
.good-wrap .imgs-wrap .img-box image{
  display: block;
  width:100%;
  height:100%;
  border-radius:4rpx;
  background-color:pink;
}
.good-wrap .good-label{
  width:30rpx;
  padding:15rpx 5rpx;
  text-align: center;
  background-color:#F1522B;
  color:#fff;
  font-size:24rpx;
  position:absolute;
  top:0;
  left:0;
  z-index:2;
  border-radius:4rpx 0 15rpx 15rpx;
  line-height:1.2;
}
.good-wrap .brief{
  margin-top:20rpx;
  color:#555;
  font-size:24rpx;
  line-height:1.4;
  letter-spacing: 2rpx;
  display: -webkit-box !important; 
  overflow: hidden;text-overflow:ellipsis;
  word-break:break-all;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:3;
}

.good-wrap .item-btn{margin-top:20rpx;}
.good-wrap .item-btn .classify-btn{width:280rpx;border-radius:40rpx;height:70rpx;line-height:70rpx;}
.good-wrap .item-btn .classify-btn .txt{padding:0 20rpx;text-align: center;text-align: center;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;font-size:30rpx;color:#fff;font-weight:700;}
.good-wrap .item-btn .classify-btn .btn-img{width:50rpx;height:50rpx;border-radius:50%;margin:10rpx 10rpx 10rpx 0;}
.good-wrap .item-btn .classify-btn .btn-img image{display: block;width:100%;height:100%;}

.good-wrap .choose-guige{background-color:#F1522B;color:#fff;height:70rpx;line-height:70rpx;border-radius:70rpx;font-size:30rpx;display: inline-block;width:280rpx;text-align: center;}

.good-wrap .price-wrap{margin-top:10rpx;}
.good-wrap .price-wrap .price{font-size:36rpx;font-weight:700;color:#f00;}
.good-wrap .price-wrap .ori-price{color:#999;font-size:28rpx;text-decoration: line-through;margin-left:16rpx;}
.good-wrap .price-wrap .sold{color:#999;font-size:24rpx;}