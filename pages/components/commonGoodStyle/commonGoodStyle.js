// pages/components/commonGoodStyle/commonGoodStyle.js
import {getGlobalData} from "../../../utils/reuseFunc"
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    'goodsList': {
      type: Array,
      value:[]
    },
    'componItem':{
      type:Object
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isShowModal:false,
  },
  attached: function () {
    this.getGlobalData('themeColor');//获取主题配色
    this.setData({
      goodsList: this.data.goodsList
    })
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getGlobalData,
    addtoCart: function(e){
      var that = this;
      var good = e.currentTarget.dataset.curgood;
      that.setData({
        isShowModal: true,
        curGoodinfo: good
      })
    },
    goodDetail: function (e) {
      let goodId = e.currentTarget.dataset.id;
      if (goodId) {
        wx.navigateTo({
          url: '/pages/goodDetail/goodDetail?id=' + goodId
        })
      }
    },
    //进入商品页面
    toFlgoods(e){
      let { goodSourceType='',goodSource='',sourceName='',kind1=''} = this.data.componItem;
      let path = '/subpages0/fenleiGoods/fenleiGoods',cate1=kind1||goodSource,cate2='';
      if(sourceName&&goodSource){//后台配置了链接
        if(goodSourceType==1){//商品分类页面
          cate2 = kind1?goodSource:'';
          path = path+'?cate1='+cate1+'&cate2='+cate2;
        }else if(goodSourceType==2){//商品分组页面
          path = '/pages/flGoodsList/flGoodsList?id='+goodSource+'&title='+sourceName;
        }
      }else{//后台没有配置商品内容
        let { id,title='',} = e.currentTarget.dataset;
        path = path+'?cate1='+id+'&title='+title;
      }

      wx.navigateTo({
        url: path,
      })

    }
  }
})
