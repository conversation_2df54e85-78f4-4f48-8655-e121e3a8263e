<!--pages/components/commonGoodStyle/commonGoodStyle.wxml-->
<view class="good-wrap">
  <block wx:key="id" wx:for="{{goodsList}}" wx:for-item="good" wx:for-index="goodIndex">
    <view class="good-item" data-id="{{good.id}}" bindtap='goodDetail'>
      <view class="title-wrap flex-wrap1">
        <view class="title flex-con" style="font-size:{{componItem.titleStyle.fontSize}};color:{{componItem.titleStyle.color}}">{{good.name}}</view>
      </view>
      <view class="label-wrap" wx:if="{{good.newLabel.length>0}}">
        <block wx:key="id" wx:for="{{good.newLabel}}" wx:for-item="label">
          <text class="label">{{label}}</text>
        </block>
      </view>
      <view class="imgs-wrap">
        <view class="good-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.stock<=0}}">售罄</view>
        <block wx:key="id" wx:for="{{good.slide}}" wx:for-item="img">
          <view class="img-box" wx:if="{{index<3}}">
            <image src="{{img}}" mode="aspectFill" lazy-load="true"></image>
          </view>
        </block>
      </view>
      <view class="brief" wx:if="{{good.brief}}">{{good.brief}}</view>
      <view class="price-wrap flex-wrap">
        <view class="price-box flex-con flex-bm">
          <view class="price" style="color:{{componItem.priceStyle.color}};font-size:{{componItem.priceStyle.fontSize}};">￥{{good.price}}</view>
          <view class="ori-price" wx:if="{{good.oriPrice>0}}">￥{{good.oriPrice}}</view>
        </view>
        <view class="sold" wx:if="{{good.soldShow==1}}">已售{{good.sold}}</view>
      </view>
      <view class="item-btn flex-sp">
        <view class="classify-btn flex-wrap" data-id="{{good.kind1}}" data-title="{{good.kind1Name}}" catchtap="toFlgoods" style="background-color:{{themeColor2||'#47CA05'}};">
          <view class="txt flex-con">进入{{componItem.sourceName||good.kind1Name}}</view>
          <view class="btn-img"><image src="/images/icon_tiaozhuan.png" mode="aspectFit"></image></view>
        </view>
        <view class="choose-btn flex-wrap">
          <text class="choose-guige" style="background-color:{{themeColor1||'#F1522B'}}" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addtoCart">加入购物车</text>
        </view>
      </view>
    </view>
  </block>
</view>
<!-- 选择商品规格 -->
<buy-modal cur-goodinfo="{{curGoodinfo}}"></buy-modal>
