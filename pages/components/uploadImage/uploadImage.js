const app=getApp();

Component({
  options:{
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  properties:{
    picsShow:{   
      type:Array,
      value:[]
    },
    imgArr:{
      type:Array,
    },
    maxLength:{
      type:[Number,String],
      value:1
    },
  },
  /**
   * 组件的初始数据
   */
  // attached() {
  //   // 在组件实例进入页面节点树时执行
  //   this.setData({
  //     imgArr: this.properties.imgArr,
  //   })
  // },
  data: {

  },
  methods: {

    // 上传图片选择
    chooseImage: function () {
      let that = this,length=this.data.maxImgLength;
      wx.showActionSheet({
        itemList: ['从相册中选择', '拍照'],
        itemColor: "#333",
        success: function (res) {
          if (!res.cancel) {
            if (res.tapIndex == 0) {
              that.chooseWxImage('album', length)
            } else if (res.tapIndex == 1) {
              that.chooseWxImage('camera', length)
            }
          }
        }
      })
    },
    chooseWxImage: function (type, length) {
      
      let that=this;
      wx.chooseImage({
        count: length,
        sizeType: ['compressed'],
        sourceType: [type],
        success: function (res) {
          var temPaths = res.tempFilePaths;
          that.uploadImage(temPaths);
        }
      })
    },
    uploadImage: function (temPaths) {//这里触发图片上传的方法
      var that = this;
      wx.showLoading({
        title: '正在上传',
        mask:true
      })
      that.uploadimg({
        url: app.globalData.requestUrl + '&map=applet_img_upload',//这里是你图片上传的接口
        path: temPaths//这里是选取的图片的地址数组
      }, []);
    },
    uploadimg: function (data, imgArr) {
      var that = this,
        i = data.i ? data.i : 0,
        success = data.success ? data.success : 0,
        fail = data.fail ? data.fail : 0;
      wx.uploadFile({
        url: data.url,
        filePath: data.path[i],
        name: 'image',
        success: (resp) => {
          success++;
          var data = JSON.parse(resp.data);
          var imgPath={
            relativePath:data.data.path,
            fullPath:app.globalData.domin + data.data.path
          }
          imgArr.push(imgPath);
         
        },
        fail: (res) => {
          fail++;
        },
        complete: () => {
          i++;
          if (i == data.path.length) {   //当图片传完时，停止调用        
            console.log('成功：' + success + " 失败：" + fail);
            // 使用 triggerEvent 方法触发自定义组件事件，指定事件名、detail对象和事件选项
            that.triggerEvent('uploadSuccess', {imgArr}, {})
            wx.hideLoading()
          } else {//若图片还没有传完，则继续调用函数
            data.i = i;
            data.success = success;
            data.fail = fail;
            that.uploadimg(data,imgArr);
          }
        }
      });
      
    },
  }
})