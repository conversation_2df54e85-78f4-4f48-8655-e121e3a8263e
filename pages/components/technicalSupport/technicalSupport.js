const app = getApp();
Component({
  properties: {
   
  },
  data: {
    
  },
  attached: function () {
    var that = this;
    that.requestSupport();
  },
  methods: {
    requestSupport: function () {
      var that = this;
      var isRequestMark = app.globalData.isRequestMark;
      if (!isRequestMark) {
        wx.$get({
          map: 'applet_water_mark'
        }).then(res=>{
          var dataInfo = res.data;
       
          app.globalData.isRequestMark = true;
          dataInfo.curVersion = app.globalData.version ? app.globalData.version : '';
          app.globalData.waterMarkData = dataInfo;

          that.setData({
            curVersion: app.globalData.version ? app.globalData.version : '',
            watermark: dataInfo.watermark,
            supportOpen: dataInfo.supportOpen,
            supportMobile: dataInfo.supportMobile,
            openWatermark: dataInfo.openWatermark,
            watermarkLogo: dataInfo.watermarkImg
          })
        }).catch(err=>{

        })
      } else {
        var dataInfo = app.globalData.waterMarkData;
        that.setData({
          curVersion: dataInfo.curVersion,
          watermark: dataInfo.watermark,
          supportOpen: dataInfo.supportOpen,
          supportMobile: dataInfo.supportMobile,
          openWatermark: dataInfo.openWatermark,
          watermarkLogo: dataInfo.watermarkImg
        })
      }
    },
    toTechnicalPage: function (e) {
      var that = this;
      var onoff = e.currentTarget.dataset.onoff;

      if (onoff == 2) {
        wx.setStorageSync('appletad', true);
        that.setData({
          appletad: true
        })
      }
      if (onoff) {
        wx.navigateTo({
          url: '/subpages/technicalPage/technicalPage'
        })
      }
    },
    supportMakecall: function (e) {
      var mobile = e.currentTarget.dataset.mobile;
      wx.showModal({
        title: '',
        content: mobile,
        confirmText: '拨打',
        confirmColor: '#48C23D',
        success: function (res) {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: mobile,
              success: function () {
                console.log("拨打电话成功！")
              },
              fail: function () {
                console.log("拨打电话失败！")

              }
            })
          }
        }
      })
    }
  }
})