<view class="technical-support" data-onoff="{{supportOpen==1}}" wx:if="{{openWatermark==1}}" bindtap="toTechnicalPage">
  <image src="{{watermarkLogo}}" class="company-logo" mode="aspectFit" wx:if="{{watermarkLogo}}"></image>
  <view class="text-wrap">
    <text class="text text-ver">{{watermark}}v{{curVersion}}</text>
    <view class="call-icon" wx:if="{{supportOpen==1&&supportMobile}}" data-mobile="{{supportMobile}}" catchtap="supportMakecall">
      <image class="icon-tel" src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/icon_support_tel.png" mode="aspectFit"></image>
      <text class="text tel">拨打电话</text>
    </view>
  </view>
  <view class="make-applet" wx:if="{{supportOpen==1}}">做一样的小程序</view>
</view>