// pages/components/pddComponent/pddComponent.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    'componentInfo': {
      type:Object,
      value: ''
    },
  },

  /**
   * 组件的初始数据
   */
  data: {

  },
  detached:function(){
  },
  /**
   * 组件的方法列表
   */
  methods: {
    //多多客商品详情
    toPddGoodDetail:function(e){
      var shopGid = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/subpages1/pddCoupon/pddGoodDetail/pddGoodDetail?shopGid='+shopGid,
      })
    },
    openLink:function(e){
      var link = e.currentTarget.dataset.url;
      if(link){
        wx.navigateTo({
          url: link,
        })
      }
    },
  }
})
