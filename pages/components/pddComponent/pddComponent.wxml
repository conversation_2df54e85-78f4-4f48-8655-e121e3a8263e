<view class="pdd-list style{{componentInfo.goodStyle}}">
  <block wx:key="index" wx:for="{{componentInfo.goodsData}}" wx:for-item="good">
    <view class="pdd-item {{componentInfo.goodStyle==2?'flex-wrap border-b':''}}" data-id="{{good.shopGid}}" bindtap="toPddGoodDetail">
      <view class="cover-img">
        <image src="{{good.cover}}" mode="aspectFill"></image>
      </view>
      <view class="pdd-info flex-con">
        <view class="title">
          <image src="/images/icon_pdd.png" mode="aspectFit"></image>{{good.name}}
        </view>
        <view class="price-wrap flex-bm {{!good.totalDiscount&&!good.groupPriceShareDeduct?'noeconomize':''}}">
          <view class="price">￥{{good.discountGroupPrice}}</view>
          <view class="ori-price" wx:if="{{good.oriGroupPrice}}">￥{{good.oriGroupPrice}}</view>
        </view>
        <view class="coupon-sold flex-wrap">
          <view class="coupon-wrap" wx:if="{{good.hasCoupon==1}}">
            <text class="label">券</text>
            <text class="con">{{good.couponDiscount}}元</text>
          </view>
          <view class="sold">已售 {{good.sold?good.sold:0}}</view>
        </view>
        <view class="economize-wrap flex-wrap" wx:if="{{good.totalDiscount||good.groupPriceShareDeduct}}">
          <view class="economize-item color1" wx:if="{{good.totalDiscount}}">
            <text class="label">自买省</text>
            <text class="con">{{good.totalDiscount}}元</text>
          </view>
          <view class="economize-item color2" wx:if="{{good.totalDiscount}}">
            <text class="label">分享赚</text>
            <text class="con">{{good.groupPriceShareDeduct}}元</text>
          </view>
        </view>
      </view>
    </view>
  </block>
</view>
<view wx:if="{{componentInfo.isShowmore}}" style="padding-bottom:18rpx;">
  <view class="see-more {{componentInfo.goodStyle==1?'morestyle1':''}}" data-id="{{componentInfo.goodSource}}" data-title="" data-type="" data-url="{{componentInfo.goodsLink}}" bindtap="openLink">查看全部商品</view>
</view>
