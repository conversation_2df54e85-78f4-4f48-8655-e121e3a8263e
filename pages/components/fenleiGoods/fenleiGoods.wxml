<!--pages/components/fenleiGoods/fenleiGoods.wxml-->
<!--折叠菜单  -->
<fold-menu></fold-menu>

<view class="page-con">
  <view class="no-data" wx:if="{{oneFl.length<=0}}">
    <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
    <text>暂无对应商品哦~</text>
  </view>
  <view class="tab-title-zhanwei">
    <view class="search-wrap" id="searchWrap">
      <view class="search-container" bindtap="searchPage">
        <image src="/images/icon_fl_sousuo.png" mode="aspectFit"></image>
        <text>在店内搜索</text>

      </view>
    </view>
    <view class="tab-title" wx:if="{{goodsFlList.length>0}}">
      <scroll-view scroll-x scroll-with-animation scroll-into-view="{{toViewCategoryId}}">
        <block wx:key="index" wx:for="{{goodsFlList}}">
          <view class="tab-item {{categoryId==item.id?'active':''}}" id="category{{item.id}}" data-categoryid="{{item.id}}" data-second="{{item.subordinate}}" bindtap="toggleChange">
            <text style="color:{{categoryId==item.id?themeColor1:'#5d5d5d'}};border-color:{{categoryId==item.id?themeColor1:'#fff'}};">{{item.name}}</text>
          </view>
        </block>
      </scroll-view>
      <view class="more-wrap" bindtap="openModel" wx:if="{{goodsFlList}}">
        <image src="/images/food-jt.png" class="icon_more" mode="aspectFit"></image>
      </view>
    </view>
  </view>
  <view class="goods-wrap" wx:if="{{cateSecondlist.length>0}}">
    <scroll-view class="one-fl border-r" scroll-y scroll-into-view="{{goIntoView}}" scroll-with-animation="true">
      <block wx:key="index" wx:for="{{cateSecondlist}}">
        <view wx:if="{{fontsize_status == 0}}" class="fl-item {{item.id==cursecondId?'active':''}}" id="secondfl{{item.id}} " data-id="{{item.id}}" bindtap="toggleSecondfl"><text style="background:{{item.id==cursecondId?themeColor1:'#fff'}};">{{item.name}}</text></view>
        <view wx:if="{{fontsize_status == 1}}" class="fl-item-two {{item.id==cursecondId?'active-one':''}}" id="secondfl{{item.id}} " data-id="{{item.id}}" bindtap="toggleSecondfl"><text style="background:{{item.id==cursecondId?themeColor1:'#fff'}}">{{item.name}}</text></view>
      </block>
    </scroll-view>
    <view class="goods-list-wrap">
      <pull-up-load wrap-height="100%" can-move="{{noMoretip}}" show-tip="{{(showLoading||shopGoods.length<=0)?false:true}}" bindpullUpEnd="pullUpEnd">
        <view class="goods-list" wx:if="{{shopGoods.length>0}}">
          <block wx:key="index" wx:for="{{shopGoods}}" wx:for-item="good">
            <view class="good-item border-b flex-wrap" data-id="{{good.id}}" bindtap="goodDetail">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="sold-tag" wx:if="{{good.stock==0}}">已售罄</view>
              </view>
              <view class="good-intro flex-con">
                <view class="good-title {{fontsize_status == 1 ? 'common-style':''}}"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text><text>{{good.name}}</text></view>
                <view class="good-price {{fontsize_status == 1 ? 'common-style':''}}"><text>￥{{good.price}}</text> <text wx:if="{{good.vipPrice > 0}}" style="font-size: 20rpx;margin-left: 5rpx;">会员价:￥{{good.vipPrice}}</text></view>
                <view class="num-change">
                  <!-- 购物车按钮 -->
                  <view class="good-add" wx:if="{{good.stock>0}}" mode="aspectFit" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addtoCart">
                    <image src="/images/icon_gwc_black.png" mode="aspectFit" class="img"></image>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="no-data" wx:if="{{shopGoods.length<=0&&!showLoading}}">
          <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
          <text>暂无对应商品哦~</text>
        </view>
        <!--上拉加载提示-->
        <view class="loading-tip" wx:if="{{showLoading}}">
          <view class="icon_load">
            <view id="floatingBarsG">
              <view class="blockG" id="rotateG_01"></view>
              <view class="blockG" id="rotateG_02"></view>
              <view class="blockG" id="rotateG_03"></view>
              <view class="blockG" id="rotateG_04"></view>
              <view class="blockG" id="rotateG_05"></view>
              <view class="blockG" id="rotateG_06"></view>
              <view class="blockG" id="rotateG_07"></view>
              <view class="blockG" id="rotateG_08"></view>
            </view>
          </view>
          <text>努力加载中...</text>
        </view>
        <!-- <view class="nomore-tip" wx:if="{{noMoretip&&shopGoods.length>0}}">没有更多数据了</view> -->
      </pull-up-load>
    </view>
  </view>
  <view class="no-data" wx:if="{{cateSecondlist.length<=0}}">
    <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
    <text>暂无对应商品哦~</text>
  </view>
  <!-- 商品类型的更多功能遮罩 -->
  <view class="type-model" wx:if="{{modelShow}}" bindtap="closeModel"></view>
  <view class="model-con" wx:if="{{modelShow}}">
    <view class="model-title flex-wrap">
      <view class="tishi-text flex-con">全部分类</view>
      <view class="close-btn" bindtap="closeModel">
        <image src="/images/food-jt.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="type-labels">
      <block wx:key="index" wx:for="{{goodsFlList}}">
        <view class="label-box {{categoryId==item.id?'active':''}}" data-model="model" data-categoryid="{{item.id}}" data-second="{{item.subordinate}}" bindtap="toggleChange">
          <text style="color:{{categoryId==item.id?themeColor1:'#5d5d5d'}};border-color:{{categoryId==item.id?themeColor1:'#fff'}};">{{item.name}}</text>
        </view>
      </block>
    </view>
  </view>
  <!-- 选择商品规格 -->
  <buy-modal cur-goodinfo="{{curGoodinfo}}"></buy-modal>
</view>
<!--错误提示-->