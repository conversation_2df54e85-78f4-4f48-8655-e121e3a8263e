// pages/components/fenleiGoods/fenleiGoods.js
import {
  getGlobalData,
  setNavColor
} from "../../../utils/reuseFunc"
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isRefresh: {
      type: Boolean,
      value: false,
      observer: 'onPullDownRefresh'
    },
    isBottom: {
      type: Boolean,
      value: false,
      observer: 'onReachBottom'
    },
    fontsizeStatus: {
      type: String,
      value: ''
    },
    queryData: {
      type: Object,
      value: {}
    },
    categoryObject: {
      type: Object,
      value: {}
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    showLoading: true,
    noMoretip: false,
    page: 0,
    curId: '',
    goIntoView: '',
  },
  pageLifetimes: {
    attached() {
      this.initData();
    },
    show() {
      app.setCartnum(); //更新购物车数量
    },
  },
  attached: function () {
    this.getGlobalData('themeColor'); //获取主题配色
    this.initData();
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getGlobalData,
    initData: function () {
      console.log("123412312", this.data.categoryObject)

      var that = this;
      that.setData({
        fontsize_status: that.properties.fontsizeStatus
      })
      console.log(that)
      app.setNavtitle('分类商品');
      setNavColor.call(this);
      var queryData = that.data.categoryObject;
      if (queryData && queryData.categoryId) {
        that.setData({
          categoryId: queryData.categoryId
        })
      }
      if (queryData && queryData.cursecondId) {
        that.setData({
          cursecondId: queryData.cursecondId
        })
      }
      that.requestGoodFl();
      app.setCartnum(); //更新购物车数量
    },
    requestGoodFl: function () {
      var that = this,
        data = {};
      data.map = 'applet_goods_category_list';
      wx.$get(data, {
        stopPull: true
      }).then(res => {
        var goodsFlList = res.data;
        if (goodsFlList && goodsFlList.length > 0) {
          var cateSecondlist = goodsFlList[0].subordinate,
            cursecondId = '';
          if (cateSecondlist.length > 0) {
            cursecondId = cateSecondlist[0].id
          }
          if (that.data.categoryId) {
            for (let j = 0; j < goodsFlList.length; j++) {
              if (that.data.categoryId == goodsFlList[j].id) {
                cateSecondlist = goodsFlList[j].subordinate;
              }
            }
            that.setData({
              toViewCategoryId: 'category' + that.data.categoryId,
              cateSecondlist: cateSecondlist
            })
          } else {
            that.setData({
              categoryId: goodsFlList[0].id,
              toViewCategoryId: 'category' + goodsFlList[0].id,
              cateSecondlist: cateSecondlist,
              cursecondId: cursecondId
            })
          }
        }
        that.requestAllGoods('one');
        that.setData({
          goodsFlList: res.data,
        })
      })
    },
    toggleSecondfl(e) {
      this.setData({
        cursecondId: e.currentTarget.dataset.id
      })
      this.triggerEvent('getCategoryId', {
        categoryId: this.data.categoryId,
        cursecondId: e.currentTarget.dataset.id
      })
      var that = this,
        id = e.currentTarget.dataset.id;
      that.setData({
        page: 0,
        noMoretip: false,
        showLoading: true,
        cursecondId: id,
        goIntoView: 'secondfl' + id,
        shopGoods: null
      })
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })
      var queryData = that.data.queryData;
      queryData.cate2 = that.data.cursecondId;
      that.triggerEvent('changeQuery', queryData)
      that.requestAllGoods();
    },
    requestAllGoods: function (type) {
      var that = this,
        data = {},
        page = that.data.page;
      data.map = 'applet_mall_category_goodsList_new';
      data.page = page;
      data.cate = that.data.categoryId ? that.data.categoryId : '';
      data.secondCate = that.data.cursecondId ? that.data.cursecondId : '';
      // if (type == 'one') {
      //   wx.showLoading({
      //     title: '加载中',
      //   })
      // }
      wx.$get(data, {
        pageList: true
      }).then(res => {
        console.log(res)
        let responseData = res.data;
        var allArr = [],
          initArr = that.data.shopGoods ? that.data.shopGoods : '[]',
          curArr = responseData.goods,
          lastPageLength = curArr.length;
        if (page > 0) {
          allArr = initArr.concat(curArr);
        } else {
          allArr = responseData.goods
        }
        if (type == 'one') {
          that.setData({
            oneFl: responseData.category,
            curId: responseData.category && responseData.category.length > 0 ? responseData.category[0].id : '',
            goIntoView: responseData.category && responseData.category.length > 0 ? 'onefl' + responseData.category[0].id : ''
          })
        }
        that.setData({
          /*  fontsize_status: responseData.fontsize_status, */
          shopGoods: allArr.length > 0 ? allArr : [],
          cartNum: responseData.cartNum
        })
        if (lastPageLength < 10) {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      }).catch(err => {
        if (page <= 0) {
          that.setData({
            shopGoods: []
          })
        } else {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      })
    },
    requestFltype: function () {
      var that = this;
      wx.$get({
        map: 'applet_goods_style'
      }).then(res => {
        console.log(res)
        var datainfo = res.data;
        that.setData({
          fontsize_status: datainfo.fontsize_status,
        })
        console.log(that.data.fontsize_status)
      }).catch(err => {
        console.log(err)
      })
    },
    onPullDownRefresh: function () {
      var that = this;
      console.log(123)
      that.setData({
        page: 0,
        noMoretip: false,
        showLoading: true,
        categoryId: ''
      });
      that.setData({
        fontsize_status: that.properties.fontsizeStatus
      })
      that.requestFltype();
      that.requestGoodFl();
    },
    onReachBottom: function () {
      var that = this,
        isMore = that.data.noMoretip,
        page = that.data.page;

      page++;
      that.setData({
        page: page
      });
      if (!isMore) {
        that.requestAllGoods();
      }
    },
    goodDetail: function (e) {
      var goodId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/pages/goodDetail/goodDetail?id=' + goodId
      })
    },
    changeShowType: function (e) {
      var type = e.target.dataset.type;
      this.setData({
        showType: type
      })
    },
    searchPage: function () {
      wx.navigateTo({
        url: '/pages/searchList/searchList'
      })
    },
    toCartPage: function () {
      wx.navigateTo({
        url: '/subpages/mycart/mycart'
      })
    },
    //打开弹窗
    openModel: function () {
      var that = this;
      that.setData({
        modelShow: true
      })
    },
    //关闭弹窗
    closeModel: function () {
      var that = this;
      that.setData({
        modelShow: false
      })
    },
    //类型切换
    toggleChange: function (e) {
      this.setData({
        categoryId: e.currentTarget.dataset.categoryid
      })

      var that = this,
        categoryId = e.currentTarget.dataset.categoryid,
        secondFl = e.currentTarget.dataset.second;
      if (e.currentTarget.dataset.model) {
        that.setData({
          modelShow: false
        });
      }
      that.setData({
        categoryId: categoryId,
        toViewCategoryId: 'category' + categoryId,
        cateSecondlist: secondFl,
        cursecondId: secondFl.length > 0 ? secondFl[0].id : '',
        showLoading: true,
        noMoretip: false,
        page: 0,
      });
      var queryData = that.data.queryData;
      queryData.cate1 = that.data.categoryId;
      queryData.cate2 = that.data.cursecondId;

      this.triggerEvent('getCategoryId', {
        categoryId: this.data.categoryId,
        cursecondId: this.data.cursecondId
      })


      that.triggerEvent('changeQuery', queryData)
      that.requestAllGoods();
    },
    searchPage: function () {
      wx.navigateTo({
        url: '/pages/searchList/searchList'
      })
    },
    addtoCart: function (e) {
      var that = this,
        good = e.currentTarget.dataset.curgood;
      that.setData({
        isShowModal: true,
        curGoodinfo: good
      })
    },

    backsy: function () {
      app.backHome();
    },
    pullUpEnd(e) {
      var that = this,
        cursecondId = that.data.cursecondId,
        cateSecondlist = that.data.cateSecondlist,
        nextIndex = 0;
      for (let i = 0; i < cateSecondlist.length; i++) {
        if (cursecondId == cateSecondlist[i].id) {
          if (i < cateSecondlist.length - 1) {
            nextIndex = i + 1;
          }
        }
      }
      let event = {
        currentTarget: {
          dataset: {
            id: cateSecondlist[nextIndex].id
          }
        }
      }
      this.toggleSecondfl(event)
    },
    onShareAppMessage: function () {
      var that = this,
        title = '分类商品',
        shareInfo = app.globalData.shareInfo;
      title = shareInfo.shareTitle ? shareInfo.shareTitle : title;
      app.getPoint(that);
      var cate1 = that.data.categoryId ? that.data.categoryId : '',
        cate2 = that.data.cursecondId ? that.data.cursecondId : '';
      return {
        title: title,
        path: '/pages/fenleiGoods/fenleiGoods?cate1=' + cate1 + '&cate2=' + cate2
      }
    },
  }
})