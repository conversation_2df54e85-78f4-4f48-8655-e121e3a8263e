// pages/components/buyModal/buyModal.js
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    curGoodinfo: {
      type: Object,
      observer: 'addtoCart'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    buyNumber: 1
  },
  attached: function(){
   
  },
  /**
   * 组件的方法列表
   */
  methods: {
    addtoCart: function (e) {
      var that = this;
      var good = that.data.curGoodinfo;
      that.setData({
        curGoodinfo: good,
        chooseGuige: '',
        buyNumber: 1
      })
      if (good.hasFormat) {
        // that.setData({
        //   isShowmodal: true,
        //   modalOperaType: 'addcart'
        // })
        app.errorTip(that, '多规格商品请在商品详情页添加', 2000);
      } else {
        that.confirmAddcart();
      }
    },
    confirmAddcart: function (e) {
      var that = this,
          esid = that.data.curGoodinfo.esid,
          gid = that.data.curGoodinfo.id,
          gfid = that.data.chooseGuige && that.data.chooseGuige.id ? that.data.chooseGuige.id : '',
          num = parseInt(that.data.buyNumber),
          isFormat = that.data.curGoodinfo.hasFormat;
      if (isFormat) {
        var stock = parseInt(that.data.chooseGuige.stock);
      } else {
        var stock = parseInt(that.data.curGoodinfo.stock);
      }
      var data = {
        map: 'applet_add_cart',
        esid: esid,
        gid: gid,
        gfid: gfid,
        num: num,
        add: 1
      }

   
      if (isFormat && !gfid) {
        wx.$showToast( '请选择商品规格！');
        return;
      }
      if (!data.num || data.num <= 0) {
        wx.$showToast( '请输入正确的商品数量');
        return;
      }
      if (data.num > stock) {
        wx.$showToast( '商品数量超出库存');
        return;
      }
      
      wx.$get(data).then(res=>{
        /* wx.showToast({
          title: '已加入购物车',
        }) */
        app.errorTip(that,'已加入购物车',1000)
       /*  wx.$showToast(res.data.msg,'success') */
        that.hideModal();
        that.setData({
          cartNumber: res.data.cartNum
        })
        app.globalData.requestCartNum=res.data.cartNum;
        app.setCartnum()
      })
    },
    hideModal: function () {
      var that = this;
      that.setData({
        isShowmodal: false
      })
    },
    chooseGuige: function (e) {
      var that = this,
          dataset = e.currentTarget.dataset,
          findex = dataset.findex,
          index = dataset.index,
          goodDetail = that.data.curGoodinfo;
      if (findex == 0) {
        var curImgformat = goodDetail.formatList[0].value[index];
        that.setData({
          curImgformat: curImgformat,
        })
      }
      var checkFormat = [];
      for (var i = 0; i < goodDetail.formatList[findex].value.length; i++) {
        goodDetail.formatList[findex].value[i].checked = false;
      }
      goodDetail.formatList[findex].value[index].checked = true;
      for (var i = 0; i < goodDetail.formatList.length; i++) {
        for (var j = 0; j < goodDetail.formatList[i].value.length; j++) {
          if (goodDetail.formatList[i].value[j].checked) {
            checkFormat.push(goodDetail.formatList[i].value[j]);
          }
        }
      }
      that.setData({
        curGoodinfo: goodDetail,
        checkFormat: checkFormat
      })
      if (checkFormat.length == goodDetail.formatList.length) {
        var formatName = '';
        for (var i = 0; i < checkFormat.length; i++) {
          formatName += checkFormat[i].name;
        }
        var chooseGuige = goodDetail.formatValue[formatName];
        chooseGuige.formatName = formatName;
        console.log(chooseGuige);
        that.setData({
          chooseGuige: chooseGuige
        })
      }
    },
    changeNum: function (e) {
      var that = this,
          type = e.currentTarget.dataset.type,
          buyNum = parseInt(that.data.buyNumber),
          stock = parseInt(that.data.chooseGuige.stock);
      if (type == 'add') {
        if (stock <= 0) {
          wx.$showToast( '商品库存不足');
          return;
        } else {
          if (buyNum < stock) {
            buyNum++;
          } else {
            wx.$showToast( '超出库存数量');
            return;
          }
        }
      } else {
        if (buyNum > 1) {
          buyNum--;
        } else {
          buyNum = 1;
        }
      }
      that.setData({
        buyNumber: buyNum
      })
    },
    numChange: function (e) {
      var that = this,
          stock = that.data.chooseGuige.stock,
          val = e.detail.value;
      val = val.replace(/\D/g, '');
      if ((parseInt(val)) > parseInt(stock)) {
        that.setData({
          buyNumber: stock
        })
      } else {
        that.setData({
          buyNumber: val
        })
      }
    },
    //单张图片预览
    singlePeiviewImg: function (e) {
      var curimg = e.currentTarget.dataset.curimg;
      var imgs = new Array(e.currentTarget.dataset.imgs);
      wx.previewImage({
        current: curimg, // 当前显示图片的http链接
        urls: imgs // 需要预览的图片http链接列表
      })
    },
  }
})
