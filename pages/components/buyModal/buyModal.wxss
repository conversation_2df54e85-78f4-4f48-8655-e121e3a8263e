/* pages/components/buyModal/buyModal.wxss */
.flex-wrap{display: flex;align-items: center;}
.flex-con{flex: 1;}
/*立即购买弹出层*/
.modal-mask { position: fixed; width: 100%; height: 100%; left: 0; top: 0; z-index: 10000; background-color: rgba(0, 0, 0, 0.6); display: none; }
.modal-mask.show { display: block; }
.buy-modal { position: fixed; width: 100%; left: 0; bottom: 0; z-index: 10001; background-color: #fff; -webkit-transition: transform 0.5s; transition: transform 0.5s; -webkit-transform: translateY(1000px); transform: translateY(1000px); }
.buy-modal.show { -webkit-transform: translateY(0); transform: translateY(0); }
.buy-modal .good-info-choose { padding-left: 25rpx; background-color: #fff; }
.good-pic-name .good-pic { width: 180rpx; height: 180rpx; background-color: #f0f0f0; border: 1px solid #d7d9d7; border-radius: 3px; margin-top: -24rpx; float: left; }
.good-pic-name .name-info { height: 180rpx; margin-left: 200rpx; padding-top: 30rpx; -webkit-box-sizing: border-box; box-sizing: border-box; }
.good-pic-name .name-info .name { max-width: 85%; display: block; margin-bottom: 5rpx; height: 80rpx; display: -webkit-box !important; overflow: hidden; text-overflow: ellipsis; word-break: break-all; -webkit-box-orient: vertical; -webkit-line-clamp: 2; font-size: 28rpx; }
.good-pic-name .name-info .price { max-width: 90%; display: block; color: #ff3e46; font-size: 32rpx; }
.good-pic-name .close-btn { height: 45rpx; width: 45rpx; position: absolute; right: 12px; top: 12px; }
.good-info-choose .guige-info { padding: 6rpx 25rpx 6rpx 0; }
.guige-info .guige-detail { padding: 10rpx 0; }
.good-info-choose .guige-detail .text { padding: 10rpx;font-size: 26rpx;border-radius: 3px;margin-right: 10rpx;margin-bottom: 10rpx;display: inline-block;min-width: 70rpx;text-align: center;line-height: 36rpx;background-color: #f5f6f7;color: #666;}
.good-info-choose .guige-detail .text.active { background-color: #ff424a; border-color: #fb1829; color: #fff; }
.guige-list .guige-text { padding: 10rpx; border-radius: 6rpx; margin-right: 16rpx; margin-bottom: 10rpx; display: inline-block; min-width: 60rpx; text-align: center; background-color: #f5f5f5;font-size: 0;border:2rpx solid #f5f5f5; }
.guige-list .guige-text.active { color: #ff424a;border-color: #ff424a; background-color: #FFEBEC;}
.guige-list .guige-text image{display: inline-block;vertical-align: middle;height: 34rpx;width: 34rpx;margin-right: 6rpx;border-radius: 6rpx;}
.guige-list .guige-text text{display: inline-block;vertical-align: middle;line-height: 34rpx;font-size: 26rpx;color: #444;}
.guige-list .guige-text text.disabled{color: #ccc;}
.guige-list .guige-text.active text{color: #ff424a;}
.buy-number { padding: 20rpx 25rpx 20rpx 0; }
.remain-number .text { display: block; }
.remain-number .remain { font-size: 26rpx; color: #666; }
.change-num .text { border: 1px solid #ddd; height: 70rpx; float: left; width: 70rpx; text-align: center; box-sizing: border-box; line-height: 66rpx; }
.change-num .number { float: left; width: 80rpx; height: 70rpx; border: 2rpx solid #ddd; padding: 0; box-sizing: border-box; text-align: center; }
.change-num .minus { border-right: none; color: #666;border-radius: 6rpx 0 0 6rpx; }
.change-num .minus.disabled { border-color: #efefef; background-color: #f8f8f8; color: #ccc; }
.change-num .add { border-left: none; color: #666;border-radius: 0 6rpx 6rpx 0; }
.change-num .add.disabled { border-color: #ddd; background-color: #f8f8f8; color: #ccc; }
.buy-modal .btn-area .btn { height: 100rpx; line-height: 100rpx; text-align: center; background-color: #fe585d; color: #fff; font-size: 32rpx; display: block; position: relative;}
.buy-modal .btn-area .btn .button{position: absolute;left:0;top:0;width: 100%;height: 100%;margin:0;padding: 0;z-index: 2;background: transparent;}
.contact-wrap { right: auto; left: 20rpx; bottom: 125rpx; }

.error-tip { padding: 20rpx 25rpx; background-color: rgba(0, 0, 0, 0.7); color: #fff; position: fixed; top: 50%; left: 50%; border-radius: 4px; transform: translate(-50%, -50%); z-index: 10000009; max-width: 80%; display: inline-block; text-align: center; }