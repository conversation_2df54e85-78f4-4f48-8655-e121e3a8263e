<!--pages/components/buyModal/buyModal.wxml-->
<view class="modal-mask {{isShowmodal? 'show' : ''}}" catchtouchmove="stoptouchmove" bindtap="hideModal"></view>
<view class="buy-modal  {{isShowmodal? 'show' : ''}}">
  <view class="good-info-choose border-b">
    <view class="good-pic-name"> 
      <image src="{{curImgformat.img?curImgformat.img:curGoodinfo.cover}}" class="good-pic" mode="aspectFill" data-curimg="{{curImgformat.img?curImgformat.img:curGoodinfo.cover}}" data-imgs="{{curImgformat.img?curImgformat.img:curGoodinfo.cover}}" bindtap="singlePeiviewImg"></image>
      <view class="name-info">
        <text class="name">{{curGoodinfo.name}}</text>
        <text class="price">￥{{chooseGuige.price ? chooseGuige.price : curGoodinfo.price}}</text>
      </view>
      <image src="/images/icon_close.png" mode="aspectFit" class="close-btn" bindtap="hideModal"></image>
    </view>
    <scroll-view scroll-y class="format-wrap" style="padding:10rpx 0;max-height:500rpx;" wx:if="{{curGoodinfo.formatList.length>0}}">
      <block wx:key="index" wx:for="{{curGoodinfo.formatList}}">
        <view class="guige-info">
          <view class="guige-item">
            <text class="guige-name">{{item.name}}</text>
            <view class="guige-list">
              <block wx:key="index" wx:for="{{item.value}}" wx:for-item="format">
                <view class="guige-text {{format.checked ? 'active' : ''}}" data-findex="{{format.fIndex}}" data-index="{{index}}" data-disabled="{{format.noCheck}}" catchtap="chooseGuige">
                  <image wx:if="{{format.img&&format.fIndex==0}}" src="{{format.img}}" mode="aspecFit"></image>
                  <text class="{{format.checked ? 'active' : ''}} {{format.noCheck?'disabled':''}}">{{format.name}}</text>
                </view>
              </block>
            </view>
            <!-- <view class="guige-detail">
              <block wx:key="index" wx:for="{{item.value}}" wx:for-item="format">
                <text class="text {{format.checked ? 'active' : ''}}" data-findex="{{format.fIndex}}" data-index="{{index}}" catchtap="chooseGuige">{{format.name}}</text>
              </block>
            </view> -->
          </view>
        </view>
      </block>
    </scroll-view>
    <view class="buy-number flex-wrap border-t">
      <view class="remain-number flex-con">
        <text class="text">购买数量:</text>
        <text class="text remain">剩余{{chooseGuige.stock?chooseGuige.stock:curGoodinfo.stock}}件</text>
      </view>
      <view class="change-num">
        <text class="text minus {{buyNumber<=1 ? 'disabled' : ''}}" data-type="minus" bindtap="changeNum">-</text>
        <input type="number" value="{{buyNumber}}" class="number" bindinput="numChange"></input>
        <text class="text add {{buyNumber>=chooseGuige.stock ? 'disabled' : ''}}" data-type="add" bindtap="changeNum">+</text>
      </view>
    </view>
  </view>
  <view class="btn-area">
    <view class="btn confirm-addcart" data-gid="{{curGoodinfo.id}}" bindtap="confirmAddcart" wx:if="{{modalOperaType=='addcart'}}">确定</view>
  </view>
</view> 
<!--错误提示-->

<view class="error-tip fade_in" wx:if="{{errorTip.isShow}}">
  {{errorTip.text}}
</view>