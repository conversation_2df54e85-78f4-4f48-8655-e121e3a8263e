<view class="liver-list style{{componentInfo.liveStyle}}">
  <block wx:key="index" wx:for="{{componentInfo.liveList}}" wx:for-item="liver">
    <view class="liver-item" data-title="{{liver.room_name}}"  data-id="{{liver.room_id}}" bindtap="toLiveDetail">
      <!-- <navigator url="plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id={{liver.room_id}}" class="liver-btn" wx:if="{{liver.live_status!='102'}}"></navigator> -->
      <view class="liver-cover">
        <image class="cover" src="{{liver.anchor_img}}" mode="aspectFill"></image>
        <view class="status-wrap flex-wrap" wx:if="{{liver.live_status=='101'}}">
          <view class="status start">正在直播</view>
        </view>
        <view class="status-wrap flex-wrap" wx:if="{{liver.live_status=='102'}}">
          <view class="status nostart">预告</view>
          <view class="time">{{liver.start_time}}</view>
        </view>
        <view class="status-wrap flex-wrap" wx:if="{{liver.live_status!='101'&&liver.live_status!='102'}}">
          <view class="status end" wx:if="{{liver.live_status=='103'}}">已结束</view>
          <view class="status end" wx:if="{{liver.live_status=='104'}}">禁播</view>
          <view class="status end" wx:if="{{liver.live_status=='105'}}">暂停中</view>
          <view class="status end" wx:if="{{liver.live_status=='106'}}">异常</view>
          <view class="status end" wx:if="{{liver.live_status=='107'}}">已过期</view>
          <view class="time">{{liver.end_time}}</view>
        </view>
        <view class="title">主播：{{liver.anchor_name}}</view>
      </view>
      <view class="avatar-nickname flex-wrap">
          <view class="nickname flex-con" style="color:{{componentInfo.titleStyle.color}};font-size:{{componentInfo.titleStyle.fontSize}}px">{{liver.room_name}}</view>
          <image class="avatar" catchtap="showSharemodal" data-id="{{liver.room_id}}" src="/images/live/icon_fenxiang.png" mode="aspectFill"></image>
        </view>
    </view>
  </block>
  <view wx:if="{{componentInfo.isShowmore}}" style="padding-bottom:20rpx;">
    <view class="see-more" bindtap="toLiverPage">查看全部直播</view>
  </view>
</view>
<!-- 页面分享弹出层组件调用方法 -->
<page-share class="page-share"  poster-type="live" request-data="{{posterData}}" use-livePlugin="{{true}}"></page-share>