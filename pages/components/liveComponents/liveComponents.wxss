/* pages/components/liveComponents/liveComponents.wxss */

/* 直播组件 */
.flex-sp { display: flex; align-items: center; justify-content: space-between; }
.flex-wrap { display: flex; align-items: center; }
.flex-wrap1 { display: flex; }
.flex-con { flex: 1; min-width: 0; }
.liver-list { padding: 20rpx 30rpx 0 30rpx; box-sizing: border-box; background-color: #fff; }
.liver-item { background-color: #fff; border-radius: 10rpx; box-shadow: 2rpx 2rpx 10rpx #dfdfdf; margin-bottom: 30rpx; position: relative; }
.liver-list.style3 .liver-item { padding: 20rpx; }
.liver-item:last-child { margin-bottom: 0; }
.liver-item .liver-btn { position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 2; }
.liver-item .liver-cover { position: relative; height: 360rpx; margin-bottom: 20rpx; }
.liver-item .liver-cover .cover { display: block; width: 100%; height: 100%; border-radius: 10rpx 10rpx 0 0; }
.liver-item .status-wrap { position: absolute; top: 20rpx; left: 20rpx; }
.liver-item .status-wrap .status { color: #fff; font-size: 22rpx; letter-spacing: 1rpx; position: relative; padding: 0 10rpx 0 30rpx; height: 36rpx; line-height: 36rpx; border-radius: 6rpx; }
.liver-item .status-wrap .status::before { content: ''; width: 10rpx; height: 10rpx; background-color: #fff; border-radius: 50%; position: absolute; top: 50%; margin-top: -5rpx; left: 10rpx; }
.liver-item .status-wrap .status.start { background: -webkit-linear-gradient(left, #FF553D, #FF6278); background: -o-linear-gradient(right, #FF553D, #FF6278); background: -moz-linear-gradient(right, #FF553D, #FF6278); background: linear-gradient(to right, #FF553D, #FF6278); }
.liver-item .status-wrap .status.nostart { background: -webkit-linear-gradient(left, #00CD81, #00D7A1); background: -o-linear-gradient(right, #00CD81, #00D7A1); background: -moz-linear-gradient(right, #00CD81, #00D7A1); background: linear-gradient(to right, #00CD81, #00D7A1); border-radius: 6rpx 0 0 6rpx; }
.liver-item .status-wrap .status.end { background: -webkit-linear-gradient(left, #2875FE, #339CFE); background: -o-linear-gradient(right, #2875FE, #339CFE); background: -moz-linear-gradient(right, #2875FE, #339CFE); background: linear-gradient(to right, #2875FE, #339CFE); border-radius: 6rpx 0 0 6rpx; }
.liver-item .status-wrap .time { color: #fff; font-size: 22rpx; letter-spacing: 1rpx; position: relative; height: 36rpx; line-height: 36rpx; padding: 0 10rpx; background-color: rgba(0, 0, 0, .5); border-radius: 0 6rpx 6rpx 0; }
.liver-item .title { padding: 15rpx 20rpx; position: absolute; bottom: 0rpx; left: 0rpx; right: 0; color: #fff; font-size: 26rpx; letter-spacing: 1rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; background: rgba(0, 0, 0, .4); }
.liver-item .avatar-nickname { padding: 15rpx 10rpx 15rpx 20rpx; color: #999; font-size: 26rpx; letter-spacing: 1rpx; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
.liver-item .avatar-nickname .avatar { padding: 12rpx; width: 32rpx; height: 32rpx; }
.liver-list .avatar-nickname .nickname { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.liver-list.style3 .avatar-nickname { padding: 0; }
.liver-list.style1 { font-size: 0; }
.liver-list.style1 .liver-item { width: 330rpx; display: inline-block; margin-right: 30rpx; }
.liver-list.style1 .liver-item:nth-child(2n) { margin-right: 0; }
.liver-list.style1 .liver-cover { height: 264rpx; margin-bottom: 0; }

.see-more{height: 84rpx;line-height: 84rpx;text-align: center;background-color: #fff;box-shadow: 2rpx 2rpx 10rpx #dfdfdf;color: #999;font-size: 28rpx;border-radius: 10rpx;}