// pages/components/liveComponents/liveComponents.js
const app = getApp();
Component({
  properties: {
    'componentInfo': {
      type:Object,
      value: ''
    },
  },
  data: {

  },
  methods: {
    toLiverPrediction: function (e) {
      var roomId = e.currentTarget.dataset.id;
      var title = e.currentTarget.dataset.title;
      // console.log(roomId);
      wx.navigateTo({
        url: '/subpages0/liverPrediction/liverPrediction?roomId=' + roomId + '&title=' + title,
      })
    },
    toLiveDetail(e){
      wx.navigateTo({
        url: 'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id='+e.currentTarget.dataset.id
      })
    },
    showSharemodal(e){
      let roomId=e.currentTarget.dataset.id;
      var pageShare = this.selectComponent('.page-share'), 
      posterData={map:'applet_appletlive_live_poster',id:roomId};
      this.setData({
        posterData:posterData,
      })
      pageShare.previewPoster()
    },
    //打开直播列表
    toLiverPage:function(){
      wx.navigateTo({
        url: '/subpages0/liverPage/liverPage',
      })
    },
  }
})
