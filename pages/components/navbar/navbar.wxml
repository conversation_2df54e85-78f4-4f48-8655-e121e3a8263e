<view class="navbar-zhanwei" style='display: {{showBar?"block":"none"}};height:{{navHeight}}px;'>
  <view class="navbar" style='height:{{navHeight}}px;background-color:{{navBgcolor}};opacity: {{isGradient?opacityValue:1}};'>
    <view class="navbar-action-wrap  {{showHome ? 'navbar-action-group' : ''}} row item-center" wx:if="{{showNav}}">
      <block wx:if="{{navTextStyle=='black'}}">
        <view class="icon-back" bindtap="navBack"><image class="icon" src="./nav-icon/nav_back.png" mode="aspectFit"></image></view>
        <view class="icon-home" bindtap="toIndex"><image class="icon" src="./nav-icon/nav_icon_home.png" mode="aspectFit"></image></view>
        </block>
        <block wx:else>
        <view class="icon-back" bindtap="navBack"><image class="icon" src="./nav-icon/nav_back1.png" mode="aspectFit"></image></view>
        <view class="icon-home" bindtap="toIndex"><image class="icon" src="./nav-icon/nav_icon_home1.png" mode="aspectFit"></image></view>
      </block>
    </view>
    <view class='navbar-title' style="color:{{navTextStyle=='black'?'#000':'#fff'}};lineHeight:{{navTitHeight}}px">
      {{pageName}}
    </view>
  </view>
</view>