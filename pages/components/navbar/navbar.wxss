/* components/navbar/index.wxss */
.navbar {
  width: 100%;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index:999;
  background-color: #fff
}


.navbar-title {
  width: 100%;
  line-height: 46px;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 50%;
  z-index: 10;
  color: #333;
  font-size: 32rpx;
  max-width: 360rpx;
  transform: translateX(-50%);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.navbar-action-wrap{
  position: absolute;
  left: 10px;
  bottom: 7px;
  z-index: 100;
  line-height: 1;
  /* padding-top: 3px;
  padding-bottom: 4px; */
}
.navbar-action-wrap .icon-back,.navbar-action-wrap .icon-home{
  padding: 4px 0;
  display: inline-block;
  vertical-align: middle;
}
.navbar-action-wrap .icon-back{
  border-right:1px solid #e8e8e8;
  width: 70rpx;
  height: 30rpx;
}
.navbar-action-wrap .icon-home{
  width: 80rpx;
  height: 36rpx;
}
.navbar-action-wrap .icon-back .icon,.navbar-action-wrap .icon-home .icon{
  display: block;
  height: 100%;
  width: 100%;
}
.navbar-action-group {  
  border:1px solid #e8e8e8;
  border-radius: 20px;
  overflow: hidden
}
.navbar-action_item{
  padding:3px 0;
  color: #333;
}
.navbar-action-group .navbar-action_item{
 border-right: 1px solid #e8e8e8;
 padding:3px 12px;
}