// components/navbar/index.js
const app = getApp()
Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    pageName: String,
    showNav: {
      type: Boolean,
      value: true
    },
    showHome: {
      type: Boolean,
      value: true
    },
    navBgcolor: {
      type: String,
      value: ''
    },
    isGradient: {
      type: <PERSON>olean,
      value: false
    },
    opacityValue: {
      type: Number,
      value: 0
    },
    showBar: {
      type: Boolean,
      value: true
    }
  },
  data: {
    navTextStyle: 'black'
  },
  attached: function () {
    var that = this
    if (app.globalData.navHeight) {
      this.setData({
        navHeight: app.globalData.navHeight,
        navTitHeight: app.globalData.navTitHeight
      })
    } else {
      this.getNavheight()
    }
    that.setData({
      navBgcolor: that.data.navBgcolor ? that.data.navBgcolor : app.globalData.navBgcolor,
      navTextStyle: that.data.navBgcolor ? 'light' : app.globalData.navTextStyle
    })
  },
  methods: {
    navBack: function () {
      var pathArr = getCurrentPages()
      if (pathArr.length <= 1) {
        app.backHome()
      } else {
        wx.navigateBack({
          delta: 1
        })
      }
    },
    toIndex: function () {
      app.backHome()
    },
    getNavheight: function () {
      var buttonInfo = wx.getMenuButtonBoundingClientRect()
      var navHeight = 66,
        navTitHeight = 46 //胶囊下方留白7px

      if (buttonInfo) {
        var navHeight = buttonInfo.bottom + 7 //胶囊下方留白7px
        var navTitHeight = buttonInfo.height + 14
      }
      this.setData({
        navHeight: navHeight,
        navTitHeight: navTitHeight
      })
      app.globalData.navHeight = navHeight
      app.globalData.navTitHeight = navTitHeight
      return navHeight
    }
  }
})
