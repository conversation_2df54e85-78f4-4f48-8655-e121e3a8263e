/* 添加到桌面样式 */
.add-desktop-tip{
  position: fixed;
  top:16rpx;
  right: 100rpx;
  border-radius: 8rpx;
  background-color: rgba(0, 0, 0, .5);
  line-height: 1.4;
  z-index: 10000001;
  color: #fff;
  padding: 16rpx 20rpx;
  font-size: 30rpx;
  max-width:330rpx;
}
.add-desktop-tip:before{
  content:'';
  position:absolute;
  top:-22rpx;
  right:24rpx;
  border-width:12rpx;
  border-style:dashed dashed solid dashed;
  border-color:transparent transparent rgba(0, 0, 0, .5) transparent;
  z-index:1;
}
/*图片渐入效果*/
@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
.fade_in {
    animation: fadeIn 1s both;
}