// pages/components/searchList/searchList.js
import {
  getGlobalData
} from "../../../utils/reuseFunc"
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isRefresh: {
      type: Boolean,
      value: false,
      observer: 'onPullDownRefresh'
    },
    fontsizeStatus: {
      type: String,
      value: ''
    },
    isBottom: {
      type: Boolean,
      value: false,
      observer: 'onReachBottom'
    },
    queryData: {
      type: Object,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showLoading: true,
    noMoretip: false,
    page: 0,
    searchVal: '',
  },
  pageLifetimes: {
    attached: function () {
      var that = this;
      that.initData();
    },
    show: function () {
      var that = this;
    },
    hide: function () {
      // 页面被隐藏
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  },
  attached: function () {
    var that = this;
    this.getGlobalData('themeColor'); //获取主题配色
    that.initData();
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getGlobalData,
    initData: function (e) {
      var that = this;
      that.setData({
        fontsize_status: that.properties.fontsizeStatus
      })
      var queryData = that.data.queryData;
      var id = '',
        type = '',
        cvalue = '',
        climit = '';
      if (queryData && queryData.id) {
        that.setData({
          cid: queryData.id
        })
      }
      if (queryData && queryData.type) {
        that.setData({
          ctype: queryData.type
        })
      }
      if (queryData && queryData.value) {
        that.setData({
          cvalue: queryData.value
        })
      }
      if (queryData && queryData.limit) {
        that.setData({
          climit: queryData.limit
        })
      }
      if (queryData && queryData.title) {
        that.setData({
          title: queryData.title
        })
        app.setNavtitle(queryData.title);
      } else {
        app.setNavtitle('商品搜索');
      }
      that.requestGoodslist();
    },
    requestGoodslist: function () {
      var that = this;
      var data = {};
      var page = that.data.page;
      var searchVal = that.data.searchVal;
      data.map = 'applet_goods_list';
      data.page = page;
      data.cid = that.data.cid ? that.data.cid : '';
      data.ctype = that.data.ctype ? that.data.ctype : '';
      if (searchVal) {
        data.keyword = searchVal;
        console.log("搜索关键字", searchVal);
      }

      wx.$get(data, {
        pageList: true
      }).then(res => {
        let responseData = res.data;
        var allArr = [];
        var initArr = that.data.shopGoods ? that.data.shopGoods : '[]';
        var curArr = responseData;
        var lastPageLength = curArr.length;
        if (page > 0) {
          allArr = initArr.concat(curArr);
        } else {
          allArr = responseData;
        }
        that.setData({
          shopGoods: allArr
        })
        if (lastPageLength < 10) {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      }).catch(err => {
        if (page <= 0) {
          that.setData({
            shopGoods: []
          })
        } else {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      })
    },
    requestFltype: function () {
      var that = this;
      wx.$get({
        map: 'applet_goods_style'
      }).then(res => {
        console.log(res)
        var datainfo = res.data;
        that.setData({
          fontsize_status: datainfo.fontsize_status,
        })
        console.log(that.data.fontsize_status)
      }).catch(err => {
        console.log(err)
      })
    },
    onPullDownRefresh: function () {
      this.setData({
        page: 0,
        noMoretip: false,
        showLoading: true
      });
      this.requestFltype();
      this.requestGoodslist();

    },
    onReachBottom: function () {
      var that = this;

      var isMore = that.data.noMoretip;
      var page = that.data.page;
      if (!isMore) {

        page++;
        that.setData({
          page: page,
        });
        that.requestGoodslist();
      }
    },
    goodDetail: function (e) {
      var goodId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/pages/goodDetail/goodDetail?id=' + goodId
      })
    },
    cancel: function () {
      wx.navigateBack({
        delta: 1
      })
    },
    searchValChange: function (e) {
      var searchVal = e.detail.value;
      this.setData({
        searchVal: searchVal
      });
    },
    searchGood: function () {
      var that = this;
      var searchVal = that.data.searchVal;
      if (searchVal.length > 0) {
        that.onPullDownRefresh();
      } else {
        wx.$showToast('请输入搜索商品关键字');
      }

    },
    addtoCart: function (e) {
      var that = this;
      var good = e.currentTarget.dataset.curgood;
      that.setData({
        isShowModal: true,
        curGoodinfo: good
      })
    },
  }
})