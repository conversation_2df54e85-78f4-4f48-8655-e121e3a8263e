/* pages/components/searchList/searchList.wxss */
@import "../../../app.wxss";

.page-con {
  height: 100vh;
  background-color: #f5f6f7;
}

.specific-use-tip {
  background-color: #f0f0f0;
  padding: 15rpx 25rpx;
  font-size: 28rpx;
  text-align: center;
  color: #666;
  margin-bottom: 10rpx;
  border-top: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  box-sizing: border-box;
}

.no-data {
  padding: 40% 0;
}

.no-data image {
  height: 160rpx;
  width: 160rpx;
  display: block;
  margin: 0 auto;
}

.no-data text {
  display: block;
  line-height: 2;
  font-size: 26rpx;
  text-align: center;
  color: #aaa;
}

.search-wrap {
  padding: 15rpx 3%;
  align-items: center;
}

.search-container {
  width: 88%;
  box-sizing: border-box;
  background-color: #fff;
  text-align: center;
  padding: 3rpx 0;
  border-radius: 3px;
  align-items: center;
}

.search-wrap image {
  height: 80rpx;
  width: 60rpx;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.search-wrap input {
  padding: 8rpx 20rpx;
  text-align: left;
  font-size: 30rpx;
}

.search-wrap .cancel-btn {
  width: 80rpx;
  text-align: center;
  display: block;
  padding-left: 15rpx;
  color: #333;
  font-size: 30rpx;
}

.title-name {
  text-align: center;
  padding: 20rpx;
  position: relative;
}

.good-fenlei {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.fenlei-list {
  width: 100%;
  box-sizing: border-box;
  padding: 25rpx 20rpx 15rpx;
  overflow: hidden;
}

.fenlei-list .fenlei-item {
  width: 25%;
  float: left;
}

.fenlei-list .fenlei-item image {
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  display: block;
  margin: 0 auto;
  margin-bottom: 8rpx;
}

.fenlei-list .fenlei-item text {
  display: block;
  text-align: center;
  font-size: 26rpx;
  line-height: 2;
}

.title-name text {
  padding: 0 20rpx;
  position: relative;
  font-size: 32rpx;
}

.good-list-wrap {
  /* background-color: #fff; */
  background-color: #f5f6f7;
  padding: 10rpx 0 0;
}

.good-list {
  padding: 0 1.5%;
  overflow: hidden;
}

.price-buy {
  font-size: 24rpx;
  line-height: 1.2;
  color: #ff2422;
}

.price-buy .now-price {
  font-size: 32rpx;
}

.price-buy .origin-price {
  margin-left: 10rpx;
  color: #999;
  text-decoration: line-through;
}

/* .good-title { margin-bottom: 10rpx; display: -webkit-box !important; overflow: hidden; text-overflow: ellipsis; word-break: break-all; -webkit-box-orient: vertical; -webkit-line-clamp: 2; height: 88rpx; }  */
.good-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 40rpx;
  height: 40rpx;
}

.good-image {
  display: block;
  height: 710rpx;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  position: relative;
}

.good-image .no-good {
  width: 120rpx;
  height: 120rpx;
  line-height: 120rpx;
  font-size: 26rpx;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -60rpx;
  letter-spacing: 1rpx;
  z-index: 10;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}

.good-image .vip-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 80rpx;
  height: 80rpx;
}

.good-image .img {
  display: block;
  height: 100%;
  width: 100%;
}

.good-intro {
  background-color: #fff;
  padding: 20rpx 0 10rpx;
  position: relative;
}

.good-intro .good-add {
  position: absolute;
  bottom: 10rpx;
  right: 0;
  height: 45rpx;
  width: 45rpx;
  border-radius: 50%;
  background-color: #F2F2F2;
  padding: 10rpx;
  box-sizing: border-box;
}

.good-intro .good-add .img {
  width: 100%;
  height: 100%;
  display: block;
}

.good-view1 .item-wrap {
  padding: 0 1%;
}

.good-view1 .good-item {
  margin-bottom: 20rpx;
}

.good-view2 .good-item {
  width: 50%;
  float: left;
  padding: 10rpx;
  box-sizing: border-box;
}

.good-view3 .good-item {
  width: 50%;
  float: left;
  margin-bottom: 14rpx;
}

.good-view3 .item-wrap {
  padding: 0 1.6%;
}

.good-view3 .good-item:nth-of-type(3n+1) {
  width: 100%;
}

.good-view3 .good-item:nth-of-type(3n+1) .item-wrap {
  padding: 0 1%;
}

.good-view1 .good-item:last-child {
  margin-bottom: 0;
}

.good-view2 .good-image {
  height: 346rpx;
  padding: 0;
}

.good-view2 .good-title {
  font-size: 30rpx;
  margin-bottom: 8rpx;
}

.good-view3 .good-item .good-image {
  height: 350rpx;
}

.good-view3 .good-item:nth-of-type(3n+1) .good-image {
  height: 710rpx;
}

.good-view4 .good-item {
  overflow: hidden;
  background-color: #fff;
  margin-bottom: 15rpx;
}

.good-view4 .good-item .good-image {
  height: 260rpx;
  width: 260rpx;
  float: left;
}

.good-view4 .good-intro {
  background-color: #fff;
  padding: 10rpx 20rpx 15rpx;
  position: relative;
  margin-left: 250rpx;
  box-sizing: border-box;
  height: 260rpx;
}

/* .good-view2 .price-buy .origin-price { display: none; } */
.buy-btn {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  height: 58rpx;
  line-height: 58rpx;
  padding: 0 20rpx;
  min-width: 60rpx;
  text-align: center;
  -webkit-border-radius: 30rpx;
  border-radius: 30rpx;
  background-color: #ff8132;
  color: #fff;
  font-size: 26rpx;
}

.end-tip {
  width: 80%;
  margin: 15rpx auto;
  text-align: center;
  position: relative;
}

.end-tip:before,
.end-tip:after {
  content: '';
  position: absolute;
  top: 50%;
  height: 2rpx;
  background-color: #e5e5e5;
  left: 0;
  margin-top: -1rpx;
  width: 47%;
}

.end-tip:before {
  left: auto;
  right: 0;
}

.end-tip text {
  height: 12rpx;
  width: 12rpx;
  display: inline-block;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  background-color: #e5e5e5;
  position: relative;
  top: -3rpx;
}

/* 加大加粗 */
.common-style text {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}