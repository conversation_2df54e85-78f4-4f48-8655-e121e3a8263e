<!--pages/components/searchList/searchList.wxml-->
<!--折叠菜单  -->
<fold-menu></fold-menu>
<view class="page-con">
  <view class="goodlist-container">
    <view class="search-wrap flex-wrap">
      <view class="search-container flex-wrap flex-con">
        <input type="text" class="flex-con" value="{{searchVal}}" bindinput="searchValChange" placeholder="输入搜索商品关键字" confirm-type="search" bindconfirm="searchGood" />
        <image src="/images/icon_search.png" bindtap="searchGood" background-size="70%" background-position="center"></image>
      </view>
      <text class="cancel-btn" bindtap="cancel">取消</text>
    </view>
    <view class="no-data" wx:if="{{shopGoods.length<=0}}">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
      <text>暂无对应商品哦~</text>
    </view>
    <view class="specific-use-tip" wx:if="{{ctype==2&&shopGoods.length>0}}">
      以下商品可以使用<block wx:if="{{climit>0}}">满{{climit}}减{{cvalue}}</block>的优惠券
    </view>
    <block wx:if="{{shopGoods.length>0}}">
      <view class="good-list-wrap">
        <view class="good-list good-view2">
          <block wx:key="index" wx:for="{{shopGoods}}" wx:for-item="good">
            <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
              <view class="item-wrap">
                <view class="good-image">
                  <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                  <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                  <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
                </view>
                <view class="good-intro">
                  <view class="good-title {{fontsize_status == 1 ?'common-style':''}}"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text><text>{{good.name}}</text> </view>
                  <view class="price-buy {{fontsize_status == 1 ?'common-style':''}}">
                    <text>￥</text> <text class="now-price">{{good.price}}</text>
                    <text class="origin-price" wx:if="{{good.oriPrice>0}}">￥{{good.oriPrice}}</text>
                  </view>
                  <!-- 购物车按钮 -->
                  <view class="good-add" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addtoCart">
                    <image src="/images/icon_gwc_black.png" mode="aspectFit" class="img"></image>
                  </view>
                  <!-- <text class="buy-btn">购买</text> -->
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
      <!--上拉加载提示-->
      <view class="loading-tip" wx:if="{{showLoading}}" style='padding-bottom:{{paddingBottom}}px'>
        <view class="icon_load">
          <view id="floatingBarsG">
            <view class="blockG" id="rotateG_01"></view>
            <view class="blockG" id="rotateG_02"></view>
            <view class="blockG" id="rotateG_03"></view>
            <view class="blockG" id="rotateG_04"></view>
            <view class="blockG" id="rotateG_05"></view>
            <view class="blockG" id="rotateG_06"></view>
            <view class="blockG" id="rotateG_07"></view>
            <view class="blockG" id="rotateG_08"></view>
          </view>
        </view>
        <text>努力加载中...</text>
      </view>
      <view class="nomore-tip" wx:if="{{noMoretip&&shopGoods.length>0}}">没有更多数据了</view>
      <view class='bottom-block' wx:if="{{!showLoading&&!noMoretip}}"></view>
    </block>
  </view>
  <!-- 选择商品规格 -->
  <buy-modal cur-goodinfo="{{curGoodinfo}}"></buy-modal>
</view>
<!--错误提示-->