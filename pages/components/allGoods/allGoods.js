// pages/components/allGoods/allGoods.js
import {
  getGlobalData,
  setNavColor
} from "../../../utils/reuseFunc"
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isRefresh: {
      type: Boolean,
      value: false,
      observer: 'onPullDownRefresh'
    },
    fontsizeStatus: {
      value: '',
      type: String
    },
    isBottom: {
      type: Boolean,
      value: false,
      observer: 'onReachBottom'
    },
    queryData: {
      type: Object,
      value: {}
    },
    navHeight: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    sortType: 6,
    showLoading: true,
    noMoretip: false,
    page: 0,
    curListType: 2,
    pricesort: '',
    allGoodsList: [{},
      { //价格排序
        pageUp: 0,
        pageDown: 0,
        noMoretipUp: false,
        noMoretipDown: false,
        goodsListUp: [],
        goodsListDown: []
      },
      { //销量排序
        page: 0,
        noMoretip: false,
        goodsList: [],

      },
      {},
      {},
      { //新品排序
        page: 0,
        noMoretip: false,
        goodsList: [],

      },
      { //综合排序
        page: 0,
        noMoretip: false,
        goodsList: [],
      },
    ], //存放所有分类商品内容，页码
    title: '商品'
  },
  pageLifetimes: {
    attached: function () {
      var that = this;
      that.initData();
    },
    show: function () {
      var that = this;
      if (that.data.title) {
        app.setNavtitle(that.data.title);
      }
      app.setCartnum(); //更新购物车数量
    },
    hide: function () {
      // 页面被隐藏
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  },

  attached: function () {
    var that = this;
    this.getGlobalData('themeColor'); //获取主题配色
    that.initData();
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getGlobalData,
    initData: function (e) {
      var that = this;
      that.setData({
        fontsize_status: that.properties.fontsizeStatus,
        vipShowOpen: app.globalData.vipShowOpen,
        extAppid: app.globalData.appid
      })
      var queryData = that.data.queryData;
      console.log("页面参数", queryData)
      if (queryData && queryData.oneid) {
        that.setData({
          oneid: queryData.oneid
        })
      }
      if (queryData && queryData.secondid) {
        that.setData({
          secondid: queryData.secondid
        })
      }
      if (queryData && queryData.title) {
        that.setData({
          title: queryData.title
        })
        app.setNavtitle(queryData.title);
      }
      setNavColor.call(this);
      that.requestAllGoods();
    },
    requestAllGoods: function () {
      var that = this;
      var data = {},
        sortType = that.data.sortType,
        page = that.data.page;
      data.map = 'applet_mall_category_goodsInfoList_new';
      var pricesort = that.data.pricesort;
      data.priceSort = pricesort;
      data.sortType = sortType;
      data.title = that.data.title;
      if(sortType == 2) {
        data.sortType = 1;
      } else if (sortType == 5) {
        data.sortType = 4;
      } else if (sortType == '1' && pricesort == 'down') {
        data.sortType = 3;
      } else if (sortType == '1' && pricesort == 'up') {
        data.sortType = 2;
      }

      if (that.data.oneid) {
        data.kind1 = that.data.oneid ? that.data.oneid : '';
      }
      data.secondCate = that.data.secondid ? that.data.secondid : '';
      data.page = page;
      wx.$get(data, {
        pageList: true
      }).then(res => {
        // that.setData({
        //   flLink: res.data.link
        // })
        var curArr = res.data.goods;
        var lastPageLength = curArr.length;
        if (page == 0) {
          that.setData({
            shopGoodsArr: null
          })
        }
        var shopGoodsArr = that.data.shopGoodsArr ? that.data.shopGoodsArr : [];
        var shopGoodsLen = shopGoodsArr.length;
        if (curArr.length > 0) {
          that.setData({
            [`shopGoodsArr[${shopGoodsLen}]`]: curArr
          })
        } else if (page == 0) {
          that.setData({
            shopGoodsArr: []
          })
        }
        if (lastPageLength < 10) {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
        var noMoretip = that.data.noMoretip
        if (sortType == 1) {
          if (pricesort == 'up') {
            that.setData({
              [`allGoodsList[${sortType}].goodsListUp`]: that.data.shopGoodsArr,
              [`allGoodsList[${sortType}].noMoretipUp`]: noMoretip,
            })
          } else {
            that.setData({
              [`allGoodsList[${sortType}].goodsListDown`]: that.data.shopGoodsArr,
              [`allGoodsList[${sortType}].noMoretipDown`]: noMoretip,
            })
          }
        } else {
          that.setData({
            [`allGoodsList[${sortType}].goodsList`]: that.data.shopGoodsArr,
            [`allGoodsList[${sortType}].noMoretip`]: noMoretip,
          })
        }
      }).catch(err => {
        if (page <= 0) {
          that.setData({
            shopGoodsArr: []
          })
        } else {
          that.setData({
            [`allGoodsList[${sortType}].noMoretip`]: true,
            showLoading: false,
            noMoretip: true
          });
        }
      })
    },
    requestFltype: function () {
      var that = this;
      wx.$get({
        map: 'applet_goods_style'
      }).then(res => {
        console.log(res)
        var datainfo = res.data;
        that.setData({
          fontsize_status: datainfo.fontsize_status,
        })
        console.log(that.data.fontsize_status)
      }).catch(err => {
        console.log(err)
      })
    },
    onPullDownRefresh: function () {
      var that = this;
      console.log('999')
      that.setData({
        page: 0,
        noMoretip: false,
        showLoading: true
      });
      that.requestFltype();
      that.requestAllGoods();
    },
    onReachBottom: function () {
      var that = this,
        isMore = that.data.noMoretip,
        page = that.data.page,
        sortType = that.data.sortType,
        pricesort = that.data.pricesort;
      page++;
      if (sortType == 1) {
        if (pricesort == 'up') {
          that.setData({
            [`allGoodsList[${sortType}].pageUp`]: page
          })
        } else {
          that.setData({
            [`allGoodsList[${sortType}].pageDown`]: page
          })
        }
      } else {
        that.setData({
          [`allGoodsList[${sortType}].page`]: page
        })
      }
      that.setData({
        page: page
      });
      if (!isMore) {
        that.requestAllGoods();
      }
    },
    goodDetail: function (e) {
      var goodId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/pages/goodDetail/goodDetail?id=' + goodId
      })
    },
    changeShowType: function (e) {
      var type = e.currentTarget.dataset.type;
      this.setData({
        showType: type
      })
    },
    allflgoodPage: function (e) {
      var link = e.currentTarget.dataset.link;
      wx.navigateTo({
        url: link
      })
    },
    searchPage: function () {
      wx.navigateTo({
        url: '/pages/searchList/searchList'
      })
    },
    sortGood: function (e) {
      var that = this,
        sortType = e.currentTarget.dataset.sort,
        pricesort = that.data.pricesort,
        allGoodsList = that.data.allGoodsList;
      if (sortType == 1) {
        if (pricesort == 'up') {
          pricesort = 'down';
          var page = allGoodsList[sortType].pageDown,
            noMoretip = allGoodsList[sortType].noMoretipDown,
            shopGoodsArr = allGoodsList[sortType].goodsListDown;
        } else {
          pricesort = 'up';
          var page = allGoodsList[sortType].pageUp,
            noMoretip = allGoodsList[sortType].noMoretipUp,
            shopGoodsArr = allGoodsList[sortType].goodsListUp;
        }
      } else {
        pricesort = '';
        var page = allGoodsList[sortType].page,
          noMoretip = allGoodsList[sortType].noMoretip,
          shopGoodsArr = allGoodsList[sortType].goodsList;
      }
      this.setData({
        sortType: sortType,
        pricesort: pricesort,
        page: page,
        noMoretip: noMoretip
      })
      if (noMoretip) {
        this.setData({
          showLoading: false
        })
      }
      if (shopGoodsArr.length == 0 && !noMoretip) {
        this.onPullDownRefresh();
      } else {

        this.setData({
          shopGoodsArr: shopGoodsArr
        })
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0
        })
      }

    },
    toggleListshow: function () {
      var that = this;
      var curListType = that.data.curListType;
      if (curListType == 2) {
        curListType = 4;
      } else if (curListType == 4) {
        curListType = 2;
      }
      that.setData({
        curListType: curListType
      })
    },
    addtoCart: function (e) {
      var that = this,
        good = e.currentTarget.dataset.curgood;
      that.setData({
        isShowModal: true,
        curGoodinfo: good
      })
    },
    onShareAppMessage: function () {
      var that = this,
        title = that.data.title ? that.data.title : '商品列表',
        oneid = that.data.oneid ? that.data.oneid : '',
        secondid = that.data.secondid ? that.data.secondid : '';
      app.getPoint(that);
      return {
        title: title,
        path: '/pages/allgoods/allgoods?oneid=' + oneid + '&secondid=' + secondid + '&title=' + title
      }
    }
  }
})