/* pages/components/allGoods/allGoods.wxss */
@import "../../../app.wxss";

.page-con {
  background-color: #f3f4f5;
  /*height: 100vh;*/
}

.no-data {
  padding: 40% 0;
}

.no-data image {
  height: 160rpx;
  width: 160rpx;
  display: block;
  margin: 0 auto;
}

.no-data text {
  display: block;
  line-height: 2;
  font-size: 26rpx;
  text-align: center;
  color: #aaa;
}

.good-sort-zhanwei {
  height: 180rpx;
}

.top-part-fixed {
  background-color: #fff;
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2;
  box-shadow: 0 4rpx 10rpx #e8e8e8;
}

.fl-search .all-fl {
  width: 90rpx;
  height: 90rpx;
  padding: 24rpx;
  box-sizing: border-box;
}

.fl-search .all-fl image {
  display: block;
  height: 100%;
  width: 100%;
}

.good-sort .sort-item {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
}

.good-sort .sort-item .price-sort {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0rpx;
  width: 18rpx;
  height: 30rpx;
  position: relative;
  top: -2rpx;
}

.good-sort .sort-item.active {
  color: #ff5854;
  position: relative;
  font-weight: bold;
}

.listshow {
  width: 90rpx;
  height: 90rpx;
  padding: 26rpx;
  box-sizing: border-box;
}

.listshow image {
  display: block;
  height: 100%;
  width: 100%;
}

.search-wrap {
  padding: 8rpx 0;
}

.search-container {
  border-radius: 8rpx;
  width: 100%;
  margin: 0 auto;
  padding: 14rpx 0;
  box-sizing: border-box;
  background-color: #f8f8f8;
  text-align: center;
  font-size: 0;
}

.search-wrap image {
  height: 36rpx;
  width: 36rpx;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
  position: relative;
  top: -2rpx;
}

.search-wrap text {
  display: inline-block;
  vertical-align: middle;
  color: #999;
  font-size: 28rpx;
}

.title-name {
  text-align: center;
  padding: 20rpx;
  position: relative;
}

.good-fenlei {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.fenlei-list {
  width: 100%;
  box-sizing: border-box;
  padding: 25rpx 20rpx 15rpx;
  overflow: hidden;
}

.fenlei-list .fenlei-item {
  width: 25%;
  float: left;
}

.fenlei-list .fenlei-item image {
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  display: block;
  margin: 0 auto;
  margin-bottom: 8rpx;
}

.fenlei-list .fenlei-item text {
  display: block;
  text-align: center;
  font-size: 26rpx;
  line-height: 2;
}

.title-name text {
  padding: 0 20rpx;
  position: relative;
  font-size: 32rpx;
}

.good-list-wrap {
  /* background-color: #fff; */
  background-color: #f3f4f5;
  padding: 10rpx 0 0;
}

.good-list {
  padding: 0 1.5%;
  overflow: hidden;
}

.price-buy {
  font-size: 24rpx;
  line-height: 1.2;
  color: #ff2422;
}

.price-buy .now-price {
  font-size: 32rpx;
}

.price-buy .ori-price {
  margin-left: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.price-buy .price-box {
  position: relative;
}

.price-buy .slod-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 20rpx;
  padding: 0 10rpx;
}

.price-buy .origin-price {
  color: #999;
  display: block;
}

.buy-btn {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  height: 46rpx;
  line-height: 48rpx;
  padding: 0 10rpx;
  min-width: 60rpx;
  text-align: center;
  -webkit-border-radius: 30rpx;
  border-radius: 30rpx;
  background-color: #ff8132;
  color: #fff;
  font-size: 26rpx;
}

.good-title {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
  height: 80rpx;
  line-height: 40rpx;
}

.good-image {
  display: block;
  height: 710rpx;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  position: relative;
}

.good-image .vip-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 80rpx;
  height: 80rpx;
}

.good-image .no-good {
  width: 120rpx;
  height: 120rpx;
  line-height: 120rpx;
  font-size: 26rpx;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -60rpx;
  letter-spacing: 1rpx;
  z-index: 1;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}

.good-image .img {
  display: block;
  height: 100%;
  width: 100%;
  border-radius: 6rpx;
}

.good-intro {
  background-color: #fff;
  padding: 20rpx 0 10rpx;
  position: relative;
}

.good-intro .good-add {
  position: absolute;
  bottom: 10rpx;
  right: 0;
  height: 45rpx;
  width: 45rpx;
  border-radius: 50%;
  background-color: #F2F2F2;
  padding: 10rpx;
  box-sizing: border-box;
}

.good-intro .good-add .img {
  width: 100%;
  height: 100%;
  display: block;
}

.good-view4 .good-intro .good-add {
  width: 55rpx;
  height: 55rpx;
  padding: 12rpx;
}

.good-view1 .item-wrap {
  padding: 0 1%;
}

.good-view1 .good-item {
  margin-bottom: 20rpx;
}

.good-view2 .good-item {
  width: 50%;
  float: left;
  padding: 8rpx;
  box-sizing: border-box;
}

.good-view2 .good-intro .price-buy {
  margin-top: 16rpx;
  height: 62rpx;
}

.good-view2 .good-intro .price-buy.sold-hide {
  line-height: 85rpx;
}

.good-view2 .good-intro .price-buy .buy-btn {
  position: absolute;
  right: 0;
  bottom: 10rpx;
}

.good-view3 .good-item {
  width: 50%;
  float: left;
  margin-bottom: 14rpx;
}

.good-view3 .item-wrap {
  padding: 0 1.6%;
}

.good-view3 .good-item:nth-of-type(3n+1) {
  width: 100%;
}

.good-view3 .good-item:nth-of-type(3n+1) .item-wrap {
  padding: 0 1%;
}

.good-view1 .good-item:last-child {
  margin-bottom: 0;
}

.good-view2 .good-image {
  height: 346rpx;
  padding: 0;
}

.good-view2 .good-title {
  font-size: 30rpx;
  margin-bottom: 8rpx;
}

.good-view3 .good-item .good-image {
  height: 350rpx;
}

.good-view3 .good-item:nth-of-type(3n+1) .good-image {
  height: 710rpx;
}

.good-view4 {
  padding: 10rpx 2.5% 0;
}

.good-view4 .good-item {
  overflow: hidden;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.good-view4 .good-item .good-image {
  height: 260rpx;
  width: 260rpx;
  float: left;
}

.good-view4 .good-intro {
  background-color: #fff;
  padding: 10rpx 20rpx 15rpx;
  position: relative;
  margin-left: 270rpx;
  box-sizing: border-box;
  height: 260rpx;
}

.good-view4 .good-intro .good-title {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
  height: 68rpx;
  line-height: 34rpx;
  font-size: 30rpx;
}

.good-view4 .good-intro .price-buy {
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
}

.good-view4 .good-intro .price-buy .buy-btn {
  position: absolute;
  right: 0;
  bottom: 10rpx;
}


.end-tip {
  width: 80%;
  margin: 15rpx auto;
  text-align: center;
  position: relative;
}

.end-tip:before,
.end-tip:after {
  content: '';
  position: absolute;
  top: 50%;
  height: 2rpx;
  background-color: #e5e5e5;
  left: 0;
  margin-top: -1rpx;
  width: 47%;
}

.end-tip:before {
  left: auto;
  right: 0;
}

.end-tip text {
  height: 12rpx;
  width: 12rpx;
  display: inline-block;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  background-color: #e5e5e5;
  position: relative;
  top: -3rpx;
}

.common-style text {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}

.slod-box .ori-price {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin: 0 20rpx 0 0;
}

.slod-box .ori-price text {
  text-decoration: line-through;
}

/* 覆盖 app.wxss 样式 */
.good-view2 .good-intro .price-buy {
  margin-top: 16rpx;
  height: 90rpx;
}

.good-intro .good-add {
  position: absolute;
  top: 50%;
  right: 6px;
  height: 45rpx;
  width: 45rpx;
  border-radius: 50%;
  background-color: #F2F2F2;
  padding: 10rpx;
  box-sizing: border-box;
  margin-top: -26rpx;
}