<!--pages/components/allGoods/allGoods.wxml-->
<!-- <nav-bar class="nav-bar" page-name="{{title}}"></nav-bar> -->
<!--折叠菜单  -->
<fold-menu></fold-menu>
<view class="page-con">
  <view class="goodlist-container">
    <view class="good-sort-zhanwei">
      <view class="top-part-fixed" style="top:{{navHeight}}px">
        <view class="fl-search flex-wrap">
          <view class="all-fl" data-link="{{flLink}}" bindtap='allflgoodPage'>
            <image src="/images/icon_all_fl.png"></image>
          </view>
          <view class="search-wrap flex-con">
            <view class="search-container" bindtap="searchPage">
              <image src="/images/sousuo.png" mode="aspectFit"></image>
              <text>在店内搜索</text>
            </view>
          </view>
          <view class="listshow" bindtap="toggleListshow">
            <image src="/images/icon_shu.png" mode="aspectFit" wx:if="{{curListType==2}}"></image>
            <image src="/images/icon_heng.png" mode="aspectFit" wx:if="{{curListType==4}}"></image>
          </view>
        </view>
        <view class="good-sort flex-wrap">
          <view class="sort-item flex-con {{sortType=='6'?'active':''}}" data-sort="6" bindtap="sortGood">综合</view>
          <view class="sort-item flex-con {{sortType=='2'?'active':''}}" data-sort="2" bindtap="sortGood">销量</view>

          <view class="sort-item flex-con {{sortType=='1'?'active':''}}" data-sort="1" bindtap="sortGood">价格
            <image src="/images/icon_lower1.png" class="price-sort" wx:if="{{sortType=='1'&&pricesort=='down'}}" mode="aspectFit"></image>
            <image src="/images/icon_upper1.png" class="price-sort" wx:if="{{sortType=='1'&&pricesort=='up'}}" mode="aspectFit"></image>
            <image src="/images/icon_price.png" class="price-sort" wx:if="{{sortType!='1'&&pricesort==''}}" mode="aspectFit"></image>
          </view>
          <view class="sort-item flex-con {{sortType=='5'?'active':''}}" data-sort="5" bindtap="sortGood">新品</view>
        </view>
      </view>
    </view>
    <view class="good-list-wrap">
      <view class="good-list good-view{{curListType}}">
        <block wx:key="index" wx:for="{{shopGoodsArr}}" wx:for-item="shopGood">
          <block wx:key="index" wx:for="{{shopGood}}" wx:for-item="good">
            <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
              <view class="item-wrap">
                <view class="good-image">
                  <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                  <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                  <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
                </view>
                <view class="good-intro">
                  <!-- 加大加粗 -->
                  <view class="good-title {{fontsize_status == 1?'common-style':''}}"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text><text>{{good.name}}</text></view>
                  <view class="price-buy {{fontsize_status == 1?'common-style':''}} {{good.soldShow==1?'':'sold-hide'}}">
                    <view class="price-box">
                      <text>￥</text><text class="now-price">{{good.price}}</text>
                      <text class="ori-price" wx:if="{{good.oriPrice>0}}">￥{{good.oriPrice}}</text>
                      <!-- 购物车按钮 -->
                      <view class="good-add" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addtoCart">
                        <image src="/images/icon_gwc_black.png" mode="aspectFit" class="img"></image>
                      </view>
                    </view>
                    <view class="slod-box">
                      <!-- <view wx:if="{{good.isVipPrice==1&&good.isVip==1}}">
                        <text class="ori-price" wx:if="{{good.noVipPrice>0&&vipShowOpen=='1'}}">非会员价 <text>￥<block wx:if="{{good.maxNoVipPrice>0&&good.minNoVipPrice>0&&good.maxNoVipPrice!=good.minNoVipPrice}}">{{good.minNoVipPrice}}-{{good.maxNoVipPrice}}</block>
                            <block wx:else>{{good.noVipPrice}}</block>
                          </text></text>
                      </view> -->
                      <text class="origin-price " wx:if="{{good.soldShow==1}}">销量{{good.sold}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </block>
      </view>
    </view>
    <view class="no-data" wx:if="{{shopGoodsArr.length<=0}}">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
      <text>暂无对应商品哦~</text>
    </view>
    <!--上拉加载提示-->
    <view class="loading-tip" wx:if="{{showLoading}}">
      <view class="icon_load">
        <view id="floatingBarsG">
          <view class="blockG" id="rotateG_01"></view>
          <view class="blockG" id="rotateG_02"></view>
          <view class="blockG" id="rotateG_03"></view>
          <view class="blockG" id="rotateG_04"></view>
          <view class="blockG" id="rotateG_05"></view>
          <view class="blockG" id="rotateG_06"></view>
          <view class="blockG" id="rotateG_07"></view>
          <view class="blockG" id="rotateG_08"></view>
        </view>
      </view>
      <text>努力加载中...</text>
    </view>
    <view class="nomore-tip" wx:if="{{noMoretip&&shopGoodsArr.length>0}}">没有更多数据了</view>
  </view>
  <!-- 选择商品规格 -->
  <buy-modal cur-goodinfo="{{curGoodinfo}}"></buy-modal>
</view>