import {
  splitArrData
} from '../../../utils/util.js';
import {
  updateLiveStatus,
  couponReceive
} from "../../../api/reuseRequest.js";
import rqcfg from "../../../utils/constant"
import {
  getGlobalData
} from "../../../utils/reuseFunc"
const app = getApp();
let livePlayer;
Component({
  properties: {
    'pullRefresh': {
      type: <PERSON><PERSON><PERSON>,
      observer: 'pullRefresh'
    },
    'tempid': {
      type: String,
      observer: ''
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    isShowFormat: false
  },
  /**
   * 组件的方法列表
   */
  ready: function () {
    var that = this;
    that.requestIndex();
    that.setData({
      sessionForm: app.globalData.sessionForm ? app.globalData.sessionForm : '',
      vipShowOpen: app.globalData.vipShowOpen
    })
    // that.requestVrurl();
    this.getGlobalData('videoInfo');
  },
  methods: {
    getGlobalData,
    // 客服回复组件间传值
    getreplyData: function (e) {
      var that = this;
      that.setData(e.detail);
    },
    pullRefresh: function () {
      console.log(444444444444)
      var that = this;
      this.getGlobalData('videoInfo');
      wx.removeStorageSync('componentData');
      that.requestIndex();
    },
    requestIndex: function () {
      var that = this;
      var data = {
        map: 'applet_custom_tpl_base',
        tplId: that.data.tempid ? that.data.tempid : ''
      };
      // wx.showLoading({
      //   title: '加载中',
      // })
      wx.$get(data).then(res => {
        console.log(res)
        var indexInfo = res.data;
        //初始化分类列表
        for (let i = 0; i < indexInfo.templateData.length; i++) {
          if (indexInfo.templateData[i].type == "seckill") { //秒杀组件
            indexInfo.templateData[i].curSort = 0;
          }
          if (indexInfo.templateData[i].type == 'catelist') {
            indexInfo.templateData[i].modelShow = false;
            indexInfo.templateData[i].categoryId = '';
            that.setData({
              toViewCategoryId: 'category' + i + '-0'
            })
          }
        }
        that.setData({
          headerTitle: indexInfo.headerTitle,
          pagebgColor: indexInfo.pagebgColor,
          componentData: indexInfo.templateData,
          isPullDownRefresh: false
        })
        console.log(that.data.componentData)

        if (that.data.headerTitle) {
          wx.setNavigationBarTitle({
            title: that.data.headerTitle
          })
        }
        that.formatComponentdata(); //格式化模板数据
        this.getGlobalData('themeColor'); //获取主题配色
        this.getGlobalData('serviceSetting');
      }).catch(err => {

      })
    },
    requestVrurl: function () {
      var that = this;
      this.videoContext = wx.createVideoContext('myVideo');
      wx.$get({
        map: 'applet_promotional_video'
      }, {
        showError: false,
      }).then(res => {
        let responseData = res.data;
        var showPlaybtn = false;
        if (responseData.url != '') {
          showPlaybtn = true;
        }
        that.setData({
          videoInfo: responseData,
          showPlaybtn: showPlaybtn,
          vrurl: responseData.vrurl,
          vrShareTitle: responseData.vrShareTitle,
          vrShareCover: responseData.vrShareCover
        })
      }).catch(err => {
        that.setData({
          showPlaybtn: false
        })
      })

    },
    // 打开搜索
    toSearchpage: function () {
      wx.navigateTo({
        url: '/pages/searchList/searchList',
      })
    },
    toInformation: function () {
      wx.navigateTo({
        url: '/pages/informationPage/informationPage',
      })
    },
    seckillIndex: function () {
      wx.navigateTo({
        url: '/pages/seckillPageShow/seckillPageShow',
      })
    },
    //秒杀tab选择
    chooseSkillTab: function (e) {
      var that = this,
        componentData = that.data.componentData,
        tabIndex = e.currentTarget.dataset.tabindex,
        componIndex = e.currentTarget.dataset.componindex;
      that.setData({
        [`componentData[${componIndex}].curSort`]: tabIndex
      })
    },
    goodDetail: function (e) {
      var goodId = e.currentTarget.dataset.id;
      var limitId = e.currentTarget.dataset.laid
      if (goodId) {
        wx.navigateTo({
          url: '/pages/goodDetail/goodDetail?id=' + goodId + '&laid=' + limitId
        })
      }
    },
    groupDetail: function (e) {
      var goodId = e.currentTarget.dataset.id;
      var status = e.currentTarget.dataset.status;
      // if (status==3){return;}
      if (goodId) {
        wx.navigateTo({
          url: '/pages/groupGoodDetail/groupGoodDetail?goodid=' + goodId
        })
      }
    },
    bargainDetail: function (e) { //砍价商品详情
      var that = this,
        id = e.currentTarget.dataset.id,
        status = e.currentTarget.dataset.status;
      // if (status == 2) { return; }
      if (id) {
        wx.navigateTo({
          url: '/subpages/bargainGoodDetail/bargainGoodDetail?id=' + id
        })
      }

    },
    tojfgoodDetail: function (e) {
      var id = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/subpages/jfGoodDetail/jfGoodDetail?id=' + id
      })
    },
    toMoreGoods: function (e) {
      var id = e.target.dataset.id;
      var title = e.target.dataset.title;
      wx.navigateTo({
        url: '/pages/allgoodsPage/allgoodsPage?oneid=""&secondid=' + id + '&title=' + title
      })
    },
    groupIndex: function () {
      wx.navigateTo({
        url: '/pages/groupIndexPage/groupIndexPage',
      })
    },
    bargainIndex: function () {
      wx.navigateTo({
        url: '/subpages/bargainIndexPage/bargainIndexPage',
      })
    },
    seckillIndex: function () {
      wx.navigateTo({
        url: '/pages/seckillPageShow/seckillPageShow',
      })
    },
    myjfshop: function () { //积分商城
      wx.navigateTo({
        url: '/subpages/integralShop/integralShop'
      })
    },
    // 打开地图
    openMap: function (e) {
      var latitude = e.currentTarget.dataset.lat,
        longitude = e.currentTarget.dataset.lng,
        address = e.currentTarget.dataset.address,
        name = e.currentTarget.dataset.name;
      wx.openLocation({
        latitude: Number(latitude),
        longitude: Number(longitude),
        name: name,
        address: address,
        scale: 18
      })
    },
    toInfoDetail: function (e) {
      var id = e.currentTarget.dataset.id;
      if (id) {
        wx.navigateTo({
          url: '/pages/informationDetail/informationDetail?id=' + id
        })
      }
    },
    // VR视图
    toWebview: function (e) {
      var that = this,
        vrurl = e.currentTarget.dataset.vrurl,
        cover = e.currentTarget.dataset.cover,
        title = e.currentTarget.dataset.title,
        vrInfo = {
          vrurl: vrurl,
          cover: cover ? cover : '',
          title: title ? title : ''
        }
      wx.setStorage({
        key: 'webviewUrl',
        data: vrInfo,
        success: function () {
          wx.navigateTo({
            url: '/pages/commonView/commonView',
          })
        }
      })
    },
    //快捷导航跳转
    openFenleiLink(e) {
      console.log(e)
      wx.setStorageSync('menuJump', e.currentTarget.dataset.menuJump)
      if (e.currentTarget.dataset.menuJump == 1) {
        let index = e.currentTarget.dataset.url.indexOf('?')
        let url = e.currentTarget.dataset.url.substring(0, index)
        console.log(url)
        wx.switchTab({
          url: url,
          fail(res){
            console.log(res)
          }
        })
        return
      }
      // if (e.currentTarget.dataset.type == '201') {
      //   wx.switchTab({
      //     url: e.currentTarget.dataset.url,
      //   })
      //   return
      // }
      var that = this;
      var type = e.currentTarget.dataset.type;
      if (type == 'index') {
        wx.reLaunch({
          url: app.globalData.indexUrl
        })
      } else if (type == '102') {
        var telphone = app.globalData.telphone ? app.globalData.telphone : '';
        var mobile = e.currentTarget.dataset.mobile;
        mobile = mobile ? mobile : telphone;
        if (mobile) {
          app.makeCall(mobile);
        } else {
          wx.$showToast("暂未获取到电话");
        }
      } else if (type == '105') {
        that._requestSign();
      } else if (type == '103') {
        wx.navigateTo({
          url: '/pages/sharepage/sharepage'
        })
      } else if (type == '3') {
        var url = e.currentTarget.dataset.url;
        var vrInfo = {
          vrurl: url
        }
        wx.setStorage({
          key: 'webviewUrl',
          data: vrInfo,
          success: function () {
            wx.navigateTo({
              url: '/pages/commonView/commonView',
            })
          }
        })
      } else if (type != '106' && type != '101') {
        var url = e.currentTarget.dataset.url;
        if (url) {
          console.log(url);
          wx.navigateTo({
            url: url
          })
        }
      }
    },
    _requestSign: function () {
      var that = this;
      wx.$get({
        map: 'applet_community_sign_point'
      }).then(res => {
        console.log(res)
        wx.showToast({
          title: res.em,
        })
      }).catch(err => {
        console.log(err)
      })
    },
    getCoupon: function (e) {
      var that = this;
      var curId = e.currentTarget.dataset.id;
      that.setData({
        curId: curId
      })
      app.getSubId('applet_coupon_receive').then(res => {
        that.toGetCoupon()
      })
    },
    // 领取优惠券跳转
    toGetCoupon: function (e) {
      let data = {
        id: this.data.curId
      }
      couponReceive(data).then(res => {
        console.log("优惠券领取成功", res)
      }).catch(err => {
        console.log("优惠券领取失败", err)
      })
    },
    //一级分类切换
    toggleChange: function (e) {
      let that = this,
        componentData = that.data.componentData,
        categoryId = e.currentTarget.dataset.categoryid,
        componIndex = e.currentTarget.dataset.componindex,
        cateIndex = e.currentTarget.dataset.cateindex,
        name = e.currentTarget.dataset.name;
      cateIndex = cateIndex > 2 ? cateIndex - 2 : 0;
      var viewCategoryId = 'category' + componIndex + '-' + cateIndex;
      that.setData({
        [`componentData[${componIndex}].categoryId`]: categoryId,
        toViewCategoryId: viewCategoryId,
        curGoodFl: '',
      });
      if (categoryId) {
        wx.navigateTo({
          url: '/pages/allgoodsPage/allgoodsPage?secondid=' + categoryId + '&title=' + name,
        })
      }
    },
    //打开弹窗
    openModel: function (e) {
      var that = this,
        componIndex = e.currentTarget.dataset.componindex,
        componentData = that.data.componentData;
      that.setData({
        [`componentData[${componIndex}].modelShow`]: true,
      })
    },
    // 优惠券分享领取
    shareGet(e) {
      var componentData = this.data.componentData;

      var index1 = e.currentTarget.dataset.index1,
        index2 = e.currentTarget.dataset.index2;
      componentData[index1].couponData[index2].needShare = 0;
      this.setData({
        componentData: componentData
      })
    },
    //关闭弹窗
    closeModel: function (e) {
      var that = this,
        componIndex = e.currentTarget.dataset.componindex,
        componentData = that.data.componentData;
      that.setData({
        [`componentData[${componIndex}].modelShow`]: false
      })
    },
    addtoCart: function (e) {
      console.log(e);
      var that = this;
      var good = e.currentTarget.dataset.curgood;
      that.setData({
        isShowModal: true,
        curGoodinfo: good
      })
    },
    formatComponentdata: function () {
      let that = this;
      let componentData = that.data.componentData;
      console.log('自定义模板数据', componentData);
      for (let i = 0; i < componentData.length; i++) {
        let currentData = componentData[i],
          type = currentData.type;
        for (let m in currentData.style) {
          if (m == 'width' && (type == 'fenlei' || type == 'search')) {
            componentData[i].style[m] = currentData.style[m] + '%';
          }
          if (componentData[i].style[m] > 0) {
            componentData[i].style[m] = currentData.style[m] * 2 + 'rpx';
          }
        }
        let delstrategy = this.dealComponentdata();
        componentData[i] = typeof delstrategy[type] === 'function' ? delstrategy[type](currentData) : currentData;
        if (currentData.requestDataCfg) {
          this.dealExtraData(currentData, i)
        }


      }
      that.setData({
        componentData: componentData
      })
      // 彪悍五金 小程序里面刷新价格显示很慢 问题这样解决会导致其它小程序出现问题，注释掉
      // wx.setStorageSync('componentData', that.data.componentData)
      console.log('组件数据', componentData);
    },
    //组件数据处理策略
    dealComponentdata() {
      const delstrategy = {
        /**
         * 搜索组件
         */
        search: (data) => {
          for (let m in data.searchArea) {
            if (data.searchArea[m] > 0) {
              data.searchArea[m] = data.searchArea[m] * 2 + 'rpx';
            }
          }
          return data
        },
        /**
         * 轮播组件
         */
        slide: (data) => {
          if (data.borderRadius > 0) {
            data.borderRadius = data.borderRadius * 2 + 'rpx';
          }
          return data
        },
        /**
         * 分类导航组件
         */
        fenlei: data => {
          if (data.iconRadius > 0) {
            data.iconRadius = data.iconRadius * 2 + 'rpx';
          }
          let navNumber = data.navNumber * 2,
            flitems = data.flitems,
            flPageitems = splitArrData(flitems, navNumber);
          data.flPageitems = flPageitems;
          return data
        },
        /**
         * 公告组件
         */
        notice: data => {
          var noticeTxt = data.noticeTxt,
            noticeTxtPage = splitArrData(noticeTxt, 2);
          data.noticeTxtPage = noticeTxtPage;
          return data
        },
        /**
         * 图片组件
         */
        image: data => {
          for (let m in data.imageStyle) {
            if (data.imageStyle[m] > 0) {
              data.imageStyle[m] = data.imageStyle[m] * 2 + 'rpx';
            }
          }
          return data
        },
        /**
         * 视频组件
         */
        video: data => {
          data.videosrc = data.videolink;
          return data
        },
        /**
         * 按钮组件
         */
        button: data => {
          for (let m in data.buttonStyle) {
            if (data.buttonStyle[m] > 0) {
              data.buttonStyle[m] = data.buttonStyle[m] * 2 + 'rpx';
            }
          }
          return data
        },
        /**
         * 分割线组件
         */
        space: data => {
          for (let m in data.spaceStyle) {
            if (data.spaceStyle[m] > 0) {
              data.spaceStyle[m] = data.spaceStyle[m] * 2 + 'rpx';
            }
          }
          return data
        },
        /**
         * 优惠券组件
         */
        coupon: data => {
          if (data.limitStyle.fontSize > 0) {
            data.limitStyle.fontSize = data.limitStyle.fontSize * 2 + 'rpx';
          }
          if (data.valueStyle.fontSize > 0) {
            data.valueStyle.fontSize = data.valueStyle.fontSize * 2 + 'rpx';
          }
          return data
        },
        /**
         * 橱窗组件
         */
        window: data => {
          if (data.imageStyle.borderRadius > 0) {
            data.imageStyle.borderRadius = data.imageStyle.borderRadius * 2 + 'rpx';
          }
          if (data.imageStyle.padding > 0) {
            data.imageStyle.padding = data.imageStyle.padding * 2 + 'rpx';
          }
          return data
        },
        /**
         * 商品列表 拼团  秒杀 砍价 积分商品  组件
         */
        goodlist: goodsPublic,
        group: goodsPublic,
        seckill: goodsPublic,
        bargain: goodsPublic,
        points: goodsPublic,
        /**
         * 图文 推荐
         */
        pictxt: picPublic,
        recommendList: picPublic
      }

      /**
       * @function  商品类组件 公共处理策略
       * @param {object} data  组件数据
       */
      function goodsPublic(data) {
        for (let m in data.titleStyle) {
          if (data.titleStyle[m] > 0) {
            data.titleStyle[m] = data.titleStyle[m] * 2 + 'rpx';
          }
        }
        for (let n in data.priceStyle) {
          if (data.priceStyle[n] > 0) {
            data.priceStyle[n] = data.priceStyle[n] * 2 + 'rpx';
          }
        }
        return data
      }

      /**
       * @function  图文及推荐 公共处理策略
       * @param {object} data  组件数据
       */
      function picPublic(data) {
        for (let m in data.titleCss) {
          if (data.titleCss[m] > 0) {
            data.titleCss[m] = data.titleCss[m] * 2 + 'rpx';
          }
        }
        for (let n in data.imageStyle) {
          if (data.imageStyle[n] > 0) {
            data.imageStyle[n] = data.imageStyle[n] * 2 + 'rpx';
          }
        }
        return data
      }
      return delstrategy
    },
    //需要额外请求数据处理策略
    dealExtraData(currentData, i) {
      let type = currentData.type;
      wx.$get({
        map: currentData.requestDataCfg['api']
      }, {
        url: rqcfg.requestUrl + '&' + currentData.requestDataCfg['params'],
        showError: false

      }).then(res => {
        let responseData = res.data;
        let field = currentData.requestDataCfg['dataType'];
        this.setData({
          [`componentData[${i}].${field}`]: responseData
        })
        //导航商品
        if (type == 'cateGoods') {
          this.setData({
            [`componentData[${i}].goodsList[0].goods`]: responseData,
            [`componentData[${i}].goodsList[0].hasGoods`]: true
          })
        }
        // 彪悍五金 小程序里面刷新价格显示很慢 问题这样解决会导致其它小程序出现问题，注释掉
        // wx.setStorageSync('componentData', this.data.componentData)
        //直播组件
        if (type == 'live') {
          this.updateLiveMsg(responseData, i)
        }

      }).catch(err => {
        console.log(err)
      })
    },
    updateLiveMsg(newList, i) {
      //直播组件信息

      if (newList.length > 0) {
        try {
          livePlayer = requirePlugin('live-player-plugin');
          // console.log('直播列表', newList);
          let currentTimeStamp = Date.parse(new Date()) / 1000;
          for (let j = 0; j < newList.length; j++) {
            //查询直播间状态
            let nowLiveStatus = newList[j].live_status,
              lastUpdateTime = parseInt(newList[j].update_time);
            // console.log('最后一次更新时间', nowLiveStatus, currentTimeStamp, lastUpdateTime, (currentTimeStamp - lastUpdateTime))
            //当直播状态为 未开始和直播中，并且直播状态更新时间是五分钟之前
            if ((nowLiveStatus == 101 || nowLiveStatus == 102) && (currentTimeStamp * 1 - lastUpdateTime) > 300) {
              // console.log('符合更新条件', newList[j].room_id)
              livePlayer.getLiveStatus({
                room_id: newList[j].room_id
              }).then(res1 => {
                // console.log('查询直播状态', res.liveStatus, newList[j].room_id)
                this.setData({
                  [`componentData[${i}].liveList[${j}].live_status`]: res1.liveStatus
                })
                updateLiveStatus(newList[j].room_id, res1.liveStatus)

              })
            }
          }
        } catch (err) {
          console.log(err)
        }
      }
    }

  }
})