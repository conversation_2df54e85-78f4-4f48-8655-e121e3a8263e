<!-- 客服回复提示弹窗 -->
<kefu-replytip is-showkfreply="{{isShowkfreply}}" reply-tips="{{replyTips}}" customer-mobile="{{customerMobile}}" contact-phone="{{contactPhone}}"></kefu-replytip>
<page-skeleton wx:if="{{!componentData}}"></page-skeleton>
<view class="custom-index-wrap" style="background-color:{{pagebgColor}};">
  <!-- 数据渲染提示 -->
  <!-- <view class="no-data-loading" wx:if="{{!componentData}}">
    <image class="load-img" src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/loading-home.gif"></image>
  </view> -->

  <block wx:key="index" wx:for="{{componentData}}" wx:for-item="compon" wx:for-index="componIndex">
    <!-- 轮播组件 -->
    <view class="slide-component" style="height:{{compon.style.height}};margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};padding-left:{{compon.style.paddingLeft}};padding-right:{{compon.style.paddingRight}};box-sizing:border-box;" wx:if="{{compon.type=='slide'&&compon.slideimgs.length>0}}">
      <!-- VR全景图 -->
      <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
        <image class="img" src="/images/icon_vr.png" mode="aspecFit"></image>
        <text class="text">全景图</text>
      </view>
      <swiper class="swiper" indicator-dots="{{true}}" autoplay="{{compon.autoplay}}" interval="{{compon.interval}}" duration="{{compon.duration}}" indicatorColor="{{compon.indicatorColor}}" indicatorActiveColor="{{compon.indicatorActiveColor}}" circular="true">
        <block wx:key="index" wx:for="{{compon.slideimgs}}" wx:for-item="slideimg">
          <swiper-item class="swiper-item" data-type="{{slideimg.type}}" data-url="{{slideimg.link}}" bindtap="openFenleiLink">
            <image src="{{slideimg.img}}" class="slide-image" style="border-radius:{{compon.borderRadius}};" />
            <block wx:if="{{slideimg.type=='106'}}">
              <navigator class="applet-jump" path="{{slideimg.path}}" target="miniProgram" app-id="{{slideimg.link}}" open-type="navigate" />
            </block>
            <block wx:if="{{slideimg.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
    <!-- 视频组件 -->
    <view class="video-component" style="height:{{compon.style.height}};margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};padding-left:{{compon.style.paddingLeft}};padding-right:{{compon.style.paddingRight}};" wx:if="{{compon.type=='video'&&compon.videosrc}}">
      <video class="video" src="{{compon.videosrc}}" style="height:{{compon.style.height}};" autoplay="{{compon.autoplay}}" objectFit="contain" poster="{{compon.videocover}}" wx:if="{{compon.videosrc}}"></video>
    </view>
    <!-- 分类导航组件 -->
    <view class="fenlei-component" style="width:{{compon.style.width}};background-color:{{compon.style.backgroundColor}};color:{{compon.style.color}};font-size:{{compon.style.fontSize}};border-radius:{{compon.style.borderRadius}};margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.type=='fenlei'}}">
      <view class="style1 singleNum{{compon.navNumber}}" wx:if="{{compon.styleType==1}}">
        <!-- <scroll-view scroll-x class="scroll-view {{compon.flitems.length<=compon.navNumber?'styleAver':''}}"> -->
        <scroll-view scroll-x class="scroll-view {{compon.flitems.length<=compon.navNumber?'styleAver':''}}">
          <block wx:key="index" wx:for="{{compon.flitems}}">
            <view class="fl-item" data-type="{{item.link.type}}" data-menu-jump="{{item.bottom_menu_jump}}" data-url="{{item.link.url}}" bindtap="openFenleiLink">
              <image class="img" src="{{item.icon}}" mode="aspectFill" style="border-radius:{{compon.iconRadius}}"></image>
              <text class="text" style="font-size:{{compon.style.fontSize}};color:{{compon.style.color}};">{{item.name}}</text>
              <block wx:if="{{item.link.type=='106'}}">
                <navigator class="kefu-btn" path="{{item.link.path}}" target="miniProgram" app-id="{{item.link.url}}" open-type="navigate" />
              </block>
              <block wx:if="{{item.link.type=='101'}}">
                <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
              </block>
            </view>
          </block>
        </scroll-view>
      </view>
      <view class="style2 singleNum{{compon.navNumber}}" wx:if="{{compon.styleType==2}}">
        <swiper class="swiper {{compon.flPageitems[0].length<=compon.navNumber?'singleH':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{compon.flPageitems.length>1}}" indicatorColor="{{compon.indicatorColor}}" indicatorActiveColor="{{compon.indicatorActiveColor}}">
          <block wx:key="index" wx:for="{{compon.flPageitems}}" wx:for-item="categoryitem">
            <swiper-item>
              <view class="fl-nav-wrap">
                <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="category">
                  <view class="fl-nav-item" data-type="{{category.link.type}}" data-menu-jump="{{category.bottom_menu_jump}}" data-url="{{category.link.url}}" bindtap="openFenleiLink">
                    <image class="img" src="{{category.icon}}" mode="aspectFill" style="border-radius:{{compon.iconRadius}}"></image>
                    <text class="text" style="font-size:{{compon.style.fontSize}};color:{{compon.style.color}};">{{category.name}}</text>
                    <block wx:if="{{category.link.type=='106'}}">
                      <navigator class="kefu-btn" target="miniProgram" app-id="{{category.link.url}}" path="{{category.link.path}}" open-type="navigate" />
                    </block>
                    <block wx:if="{{category.link.type=='101'}}">
                      <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
                    </block>
                  </view>
                </block>
              </view>
            </swiper-item>
          </block>
        </swiper>
      </view>
      <view class="style3" wx:if="{{compon.styleType==3}}">
        <block wx:key="index" wx:for="{{compon.flitems}}">
          <view class="fl-item flex-wrap" data-type="{{item.link.type}}" data-menu-jump="{{item.bottom_menu_jump}}" data-url="{{item.link.url}}" bindtap="openFenleiLink">
            <block wx:if="{{item.link.type=='106'}}">
              <navigator class="kefu-btn" path="{{item.link.path}}" target="miniProgram" app-id="{{item.link.url}}" open-type="navigate" />
            </block>
            <block wx:if="{{item.link.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
            <image class="img" src="{{item.icon}}" mode="aspectFill" style="border-radius:{{compon.iconRadius}}"></image>
            <view class="item-intro flex-con">
              <text class="text name" style="font-size:{{compon.style.fontSize}};color:{{compon.style.color}};">{{item.name}}</text>
              <text class="text brief" style="color:{{compon.style.briefColor}};">{{item.brief}}</text>
            </view>
          </view>
        </block>
      </view>
    </view>
    <!-- 搜索组件 -->
    <view class="search-component" wx:if="{{compon.type=='search'}}">
      <view class="search-wrap" style="background-color:{{compon.searchArea.backgroundColor}};padding-top:{{compon.searchArea.paddingTop}};padding-bottom:{{compon.searchArea.paddingBottom}};margin-top:{{compon.searchArea.marginTop}};margin-bottom:{{compon.searchArea.marginBottom}};">
        <view class="search-con" style="height:{{compon.style.height}};line-height:{{compon.style.height}};width:{{compon.style.width}};font-size:{{compon.style.fontSize}};background-color:{{compon.style.backgroundColor}};border-radius:{{compon.style.borderRadius}};color:{{compon.style.color}};border-color:{{compon.style.borderColor}};" bindtap="toSearchpage">
          <text>{{compon.placeHolder}}</text>
          <image src="/images/customIcon/icon_search_black.png" class="icon-search" mode="aspectFit" wx:if="{{compon.searchiconColor=='black'}}"></image>
          <image src="/images/customIcon/icon_search_white.png" class="icon-search" mode="aspectFit" wx:if="{{compon.searchiconColor=='white'}}"></image>
        </view>
      </view>
    </view>
    <!-- 地址组件 -->
    <view class="address-component" wx:if="{{compon.type=='address'}}">
      <view class="style1" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};color:{{compon.style.color}};font-size:{{compon.style.fontSize}};" wx:if="{{compon.addressStyle==1}}">
        <view class="address-wrap flex-wrap" style="background-color:{{compon.style.backgroundColor?compon.style.backgroundColor:'#fff'}};" data-lat="{{compon.address.latitude}}" data-lng="{{compon.address.longitude}}" data-address="{{compon.address.addr}}" data-name="{{compon.companyName}}" bindtap="openMap">
          <image wx:if="{{compon.addressiconColor=='grey'}}" src="/images/customIcon/icon_position.png" class="icon_addr" mode="aspectFit"></image>
          <image wx:if="{{compon.addressiconColor=='white'}}" src="/images/customIcon/icon_position_white.png" class="icon_addr" mode="aspectFit"></image>
          <view class="flex-con">
            <view class="address-detail">{{compon.address.addr}}</view>
          </view>
          <image src="/images/customIcon/icon_addr_jt.png" class="icon_enter" mode="aspectFit"></image>
        </view>
      </view>
      <view class="style2" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};color:{{compon.style.color}};font-size:{{compon.style.fontSize}};" wx:if="{{compon.addressStyle==2}}">
        <view class="item-box flex-wrap">
          <image src="/images/customIcon/icon_time.png" class="icon_addr" mode="aspectFit"></image>
          <view class="flex-con">
            <view class="address-detail">{{compon.businessTime}}</view>
          </view>
        </view>
        <view class="item-box flex-wrap" data-type="102" data-mobile="{{compon.mobile}}" bindtap="openFenleiLink">
          <image src="/images/customIcon/icon_tel1.png" class="icon_addr" mode="aspectFit"></image>
          <view class="flex-con">
            <view class="address-detail">{{compon.mobile}}</view>
          </view>
          <image src="/images/customIcon/icon_addr_jt.png" class="icon_enter" mode="aspectFit"></image>
        </view>
        <view class="item-box flex-wrap" data-lat="{{compon.address.latitude}}" data-lng="{{compon.address.longitude}}" data-address="{{compon.address.addr}}" data-name="{{compon.companyName}}" bindtap="openMap">
          <image src="/images/customIcon/icon_position1.png" class="icon_addr" mode="aspectFit"></image>
          <view class="flex-con">
            <view class="address-detail">{{compon.address.addr}}</view>
          </view>
          <image src="/images/customIcon/icon_addr_jt.png" class="icon_enter" mode="aspectFit"></image>
        </view>
      </view>
      <view class="style3" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.addressStyle==3}}">
        <view class="shop-address-wrap">
          <view class="shop-info flex-wrap">
            <image src="{{compon.companyLogo}}" class="shop-logo" mode="aspectFill"></image>
            <view class="flex-con">
              <view class="shop-name">
                <text class="name">{{compon.companyName}}</text>
                <view class="tel-kefu">
                  <image src="/images/customIcon/icon_dh.png" class="icon_tel" mode="aspectFit" data-lat="{{compon.address.latitude}}" data-lng="{{compon.address.longitude}}" data-address="{{compon.address.addr}}" data-name="{{compon.companyName}}" bindtap="openMap"></image>
                  <image src="/images/customIcon/icon_tel.png" class="icon_tel" mode="aspectFit" data-type="102" data-mobile="{{compon.mobile}}" bindtap="openFenleiLink"></image>
                  <view class="kefu">
                    <image src="/images/customIcon/icon_wechat.png" class="icon_tel" mode="aspectFit"></image>
                    <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
                  </view>
                </view>
              </view>
              <view class="shop-address flex-wrap" data-lat="{{compon.address.latitude}}" data-lng="{{compon.address.longitude}}" data-address="{{compon.address.addr}}" data-name="{{compon.companyName}}" bindtap="openMap">
                <image src="/images/customIcon/icon_position.png" class="icon_addr" mode="aspectFit"></image>
                <view class="flex-con">
                  <view class="address-detail">{{compon.address.addr}}</view>
                </view>
                <image src="/images/customIcon/icon_addr_jt.png" class="icon_enter" mode="aspectFit"></image>
              </view>
            </view>
          </view>
          <view class="shop-brief" data-id="{{compon.companyLink}}" bindtap='toInfoDetail'>
            {{compon.companyBrief}}
            <text class="text">查看全部</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 通知公告组件 -->
    <view class="notice-component" style="color:{{compon.style.color}};background-color:{{compon.style.backgroundColor}};font-size:{{compon.style.fontSize}};margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.type=='notice'}}">
      <view class="notice-wrap flex-wrap">
        <!-- <view class="tt-title" style="color:{{compon.titleColor}}" bindtap='toInformation'>{{compon.titleTxt}}</view>
        <view class="beauty-toutiao flex-con">
          <swiper class="swiper" vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
            <block wx:key="index" wx:for="{{compon.noticeTxtPage}}" wx:for-item="noticeitem">
              <swiper-item>
                <block wx:key="index" wx:for="{{noticeitem}}">
                  <view class="toutiao-item {{noticeitem.length==1?'style1':''}}" style="font-size:{{compon.style.fontSize}};" data-id="{{item.link}}" bindtap='toInfoDetail'>{{item.text}}</view>
                </block>
              </swiper-item>
            </block>
          </swiper>
        </view> -->
        <view class="notice-icon" bindtap='toInformation'>
          <image src="/images/icon_xiaoxi.png" mode="aspecFit"></image>
        </view>
        <view class="beauty-toutiao flex-con">
          <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600" style="height:60rpx;">
            <block wx:key="index" wx:for="{{compon.noticeTxt}}" wx:for-item="noticeitem">
              <swiper-item>
                <view class="toutiao-item" style="font-size:{{compon.style.fontSize}};" data-id="{{noticeitem.link}}" bindtap='toInfoDetail'>{{noticeitem.text}}</view>
              </swiper-item>
            </block>
          </swiper>
        </view>
      </view>
    </view>
    <!-- 标题组件 -->
    <view class="title-component" style="color:{{compon.style.color}};background-color:{{compon.style.backgroundColor}};text-align:{{compon.style.textAlign}};padding-top:{{compon.style.paddingTop}};padding-bottom:{{compon.style.paddingBottom}};margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.type=='title'}}">
      <image src="{{compon.titleBg}}" class="title-bg" wx:if="{{compon.titleStyle==5}}"></image>
      <view class="title-label style{{compon.titleStyle}}">
        <view class="title-txt {{compon.isBold?'bold':''}}">
          <text class="name" style="font-size:{{compon.style.fontSize}};">{{compon.titleTxt}}</text>
          <text class="line" style="background-color:{{compon.lineColor}};" wx:if="{{compon.titleStyle!=5&&compon.titleStyle!=1}}"></text>
        </view>
      </view>
    </view>
    <!-- 图片组件 -->
    <view class="image-component" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};padding-top:{{compon.style.paddingTop}};padding-bottom:{{compon.style.paddingBottom}};padding-left:{{compon.style.paddingLeft}};padding-right:{{compon.style.paddingRight}};background-color:{{compon.style.backgroundColor}};text-align:{{compon.style.textAlign}};" wx:if="{{compon.type=='image'}}">
      <view class="img-box {{compon.imageLocation}}" style="width:{{compon.imageStyle.width}};height:{{compon.imageStyle.height}};" data-type="{{compon.link.type}}" data-url="{{compon.link.url}}" bindtap="openFenleiLink">
        <image class="img" src="{{compon.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}};"></image>
        <block wx:if="{{compon.link.type=='106'}}">
          <navigator class="applet-jump" target="miniProgram" path="{{compon.link.path}}" app-id="{{compon.link.url}}" open-type="navigate" />
        </block>
        <block wx:if="{{compon.link.type=='101'}}">
          <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
        </block>
      </view>
    </view>
    <!-- 按钮组件 -->
    <view class="button-component" style="padding-top:{{compon.style.paddingTop}};padding-bottom:{{compon.style.paddingBottom}};text-align:{{compon.style.textAlign}}" wx:if="{{compon.type=='button'}}">
      <view class="cus-btn" hover-class="btn-hover" style="width:{{compon.buttonStyle.width}};height:{{compon.buttonStyle.height}};line-height:{{compon.buttonStyle.lineHeight}};color:{{compon.buttonStyle.color}};background-color:{{compon.buttonStyle.backgroundColor}};font-size:{{compon.buttonStyle.fontSize}};border-color:{{compon.buttonStyle.borderColor}};border-radius:{{compon.buttonStyle.borderRadius}};" data-type="{{compon.link.type}}" data-url="{{compon.link.url}}" bindtap="openFenleiLink">{{compon.btntxt}}
        <block wx:if="{{compon.link.type=='106'}}">
          <navigator class="applet-jump" target="miniProgram" path="{{compon.link.path}}" app-id="{{compon.link.url}}" open-type="navigate" />
        </block>
        <block wx:if="{{compon.link.type=='101'}}">
          <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
        </block>
      </view>
    </view>
    <!-- 分割线组件 -->
    <view class="space-component" style="margin-top:{{compon.style.marginTop}};padding-left:{{compon.style.paddingLeft}};" wx:if="{{compon.type=='space'}}">
      <text class="text space {{compon.style.textAlign}}" style="border-top-width:{{compon.spaceStyle.borderTopWidth}};border-top-style:{{compon.spaceStyle.borderTopStyle}};border-top-color:{{compon.spaceStyle.borderTopColor}};width:{{compon.spaceStyle.width}};" wx:if="{{compon.type=='space'}}"></text>
    </view>
    <!-- 广告组件 -->
    <view class="ad-component" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.type=='advertisement'}}">
      <ad unit-id="{{compon.unitId}}" wx:if="{{compon.unitId}}"></ad>
    </view>
    <!-- 商品列表组件 -->
    <view class="goods-component" wx:if="{{compon.type=='goodlist'&&compon.goodsData.length>0}}">
      <scroll-view scroll-x="{{compon.goodStyle==4}}" class="goods-list style{{compon.goodStyle}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.goodStyle!=6}}">
        <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good">
          <view class="good-item border-b" data-id="{{good.id}}" bindtap='goodDetail'>
            <view class="good-item-con">
              <view class="good-image style{{compon.bigStyle}}">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <view class="good-title" stylesheet style="color:{{compon.titleStyle.color}};font-size:{{compon.titleStyle.fontSize}};">{{good.name}}</view>
                <view class="good-brief" wx:if="{{compon.goodStyle==2}}">{{good.brief}}</view>
                <view class="good-price">
                  <view class="price-box">
                    <view class="now-price {{compon.priceBold?'bold':''}}" style="color:{{compon.priceStyle.color}};">￥
                      <text class="text" style="font-size:{{compon.priceStyle.fontSize}};">{{good.price}}</text> 
                      <text class='good-oriPrice'>{{good.oriPrice>0?'￥'+good.oriPrice:''}}</text>
                    </view>
                    <view class="good-add" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addtoCart">
                      <image src="/images/icon_gwc_black.png" mode="aspectFit" class="img"></image>
                    </view>
                  </view>
                  <view class="slod-box">
                    <!-- <view wx:if="{{good.isVipPrice==1&&good.isVip==1}}">
                      <text class="ori-price" wx:if="{{good.noVipPrice>0&&vipShowOpen=='1'}}">非会员价 <text>￥<block wx:if="{{good.maxNoVipPrice>0&&good.minNoVipPrice>0&&good.maxNoVipPrice!=good.minNoVipPrice}}">{{good.minNoVipPrice}}-{{good.maxNoVipPrice}}</block>
                          <block wx:else>{{good.noVipPrice}}</block>
                        </text></text>
                    </view> -->
                    <block wx:if="{{compon.goodStyle==1}}">
                      <text class="sold-num" wx:if="{{good.soldShow == 1}}">销量：{{good.sold}}</text>
                      <text class="sold-num-active" wx:if="{{good.soldShow != 1}}"></text>
                    </block>
                    <!-- 新修改的 -->
                    <block wx:if="{{compon.goodStyle==3}}">
                      <view class="sold-num-new" wx:if="{{good.soldShow == 1}}">销量：{{good.sold}}</view>
                      <view class="sold-num-active" wx:if="{{good.soldShow != 1}}"></view>
                    </block>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </block>
        <view class="good-item border-b" wx:if="{{compon.goodStyle==4&&compon.isShowmore}}" style="width:auto;">
          <view class="good-item-con">
            <view class="single-more" style="height:320rpx;background-color:#fff;" data-type="" data-url="{{compon.goodsLink}}" bindtap="openFenleiLink">
              <text class="text">查看更多</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="goods-list" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.goodStyle==6}}">
        <common-goodstyle goods-list="{{compon.goodsData}}" compon-item="{{compon}}"></common-goodstyle>
      </view>
      <view wx:if="{{compon.goodStyle!=4&&compon.isShowmore}}" style="padding-bottom:18rpx;">
        <view class="see-more {{compon.goodStyle==1?'morestyle1':''}}" data-id="{{compon.goodSource}}" data-title="{{compon.sourceName}}" data-type="" data-url="{{compon.goodsLink}}" bindtap="openFenleiLink">查看全部商品</view>
      </view>


    </view>
    <!-- 图文列表组件 -->
    <view class="pictxt-component" wx:if="{{compon.type=='pictxt'}}">
      <scroll-view scroll-x="{{compon.picStyle==1}}" class="pic-list style{{compon.picStyle}} imgnum{{compon.singleImgNum}}" style="margin-top:{{compon.style.marginTop}};text-align:{{compon.style.textAlign}};margin-bottom:{{compon.style.marginBottom}};">
        <block wx:key="index" wx:for="{{compon.picData}}" wx:for-item="pic">
          <view class="pic-item border-b" data-type="{{pic.link.type}}" data-url="{{pic.link.url}}" bindtap="openFenleiLink">
            <view class="pic-item-con">
              <view class="img-box">
                <image class="img" src="{{pic.cover}}" style="height:{{compon.imageStyle.height}};border-radius:{{compon.imageStyle.borderRadius}}" mode="aspectFill"></image>
              </view>
              <view class="pic-intro style{{compon.titleStyle}}" wx:if="{{compon.titleStyle!=3}}">
                <view class="pic-title" style="color:{{compon.titleCss.color}};font-size:{{compon.titleCss.fontSize}};line-height:{{compon.titleCss.lineHeight}};">{{pic.title}}</view>
                <view class="pic-brief" style="color:{{compon.briefFontcolor}};" wx:if="{{(compon.picStyle==1&&compon.titleStyle==2&&compon.isShowbrief)||(compon.picStyle==2&&compon.isShowbrief)}}">{{pic.brief}}</view>
              </view>
            </view>
            <block wx:if="{{pic.link.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" path="{{pic.link.path}}" app-id="{{pic.link.url}}" open-type="navigate" />
            </block>
            <block wx:if="{{pic.link.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
        </block>
      </scroll-view>
    </view>
    <!-- 推荐组件 -->
    <view class="pictxt-component" wx:if="{{compon.type=='recommendList'&&compon.picData.length>0}}">
      <scroll-view scroll-x="{{compon.picStyle==1}}" class="pic-list style{{compon.picStyle}} imgnum{{compon.singleImgNum}}" style="margin-top:{{compon.style.marginTop}};text-align:{{compon.style.textAlign}};margin-bottom:{{compon.style.marginBottom}};">
        <block wx:key="index" wx:for="{{compon.picData}}" wx:for-item="pic">
          <view class="pic-item border-b" data-type="{{pic.link.type}}" data-url="{{pic.link.url}}" bindtap="openFenleiLink">
            <view class="pic-item-con">
              <view class="img-box">
                <image class="img" src="{{pic.cover}}" style="height:{{compon.imageStyle.height}};border-radius:{{compon.imageStyle.borderRadius}}" mode="aspectFill"></image>
              </view>
              <view class="pic-intro style{{compon.titleStyle}}" wx:if="{{compon.titleStyle!=3}}">
                <view class="pic-title" style="color:{{compon.titleCss.color}};font-size:{{compon.titleCss.fontSize}};line-height:{{compon.titleCss.lineHeight}};">{{pic.title}}</view>
                <view class="pic-brief" style="color:{{compon.briefFontcolor}};" wx:if="{{(compon.picStyle==1&&compon.titleStyle==2&&compon.isShowbrief)||(compon.picStyle==2&&compon.isShowbrief)}}">{{pic.brief}}</view>
              </view>
            </view>
            <block wx:if="{{pic.link.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" path="{{pic.link.path}}" app-id="{{pic.link.url}}" open-type="navigate" />
            </block>
          </view>
        </block>
        <view class="pic-item border-b" style="width:auto;" wx:if="{{compon.isShowmore&&compon.picStyle==1}}">
          <view class="pic-item-con">
            <view class="single-more" style="height:{{compon.imageStyle.height}};border-radius:{{compon.imageStyle.borderRadius}};background-color:#f0f0f0;" data-type="1" data-url="{{compon.listUrl}}" bindtap="openFenleiLink">
              <text class="text">查看更多</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <view wx:if="{{compon.isShowmore&&compon.picStyle==2}}">
        <view class="see-more border-t" data-type="1" data-url="{{compon.listUrl}}" bindtap="openFenleiLink">查看更多</view>
      </view>
    </view>
    <!-- 新优惠券-->
    <view class="new-coupon-wrap" wx:if="{{compon.couponData.length>0}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};padding-top:{{compon.style.paddingTop}};padding-bottom:{{compon.style.paddingBottom}};">
      <scroll-view scroll-x class="scroll-view {{compon.couponData.length==1?'style1':''}} {{compon.couponData.length==2?'style2':''}} {{compon.couponData.length>2?'style3':''}}">
        <!-- <scroll-view scroll-x class="scroll-view style2"> -->
        <block wx:key="index" wx:for="{{compon.couponData}}" wx:for-item="coupon">
          <view class="coupon-item">
            <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/couponimg/image_youhuijuan.png" class="coupon_bg fade_in" wx:if="{{compon.couponData.length>1}}"></image>
            <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/couponimg/image_bj.png" class="coupon_bg fade_in" wx:if="{{compon.couponData.length==1}}"></image>
            <view class="coupon-info" wx:if="{{compon.couponData.length>1}}">
              <view class="coupon-title">
                <view class="title"><text>{{coupon.name}}</text></view>
              </view>
              <view class="info-wrap flex-wrap">
                <view class="flex-con flex-wrap">
                  <view class="left-info flex-con">
                    <view class="money">￥<text>{{coupon.value}}</text></view>
                    <text class="limit-use">{{coupon.limit==0?'无使用限制':'满'+coupon.limit+'减'+coupon.value}}</text>
                  </view>
                  <view class="right-info">
                    <view class="desc">优惠券</view>
                    <block wx:if="{{coupon.received==1}}">
                      <view class="coupon-btn">已领取</view>
                    </block>
                    <block wx:if="{{coupon.startLeft==0&&coupon.received==0}}">
                      <view class="coupon-btn" wx:if="{{coupon.needShare==0&&!coupon.level}}" bindtap="getCoupon" data-id="{{coupon.id}}">{{coupon.hadReceive==1?'已':'立即'}}领取</view>
                      <view class="coupon-btn" wx:if="{{coupon.needShare==0&&coupon.level}}" bindtap="getCoupon" data-id="{{coupon.id}}">会员领取</view>
                      <view class="coupon-btn" wx:if="{{coupon.needShare==1}}">分享领<button open-type="share" data-index1="{{componIndex}}" data-index2='{{index}}' data-id="{{coupon.id}}" bindtap='shareGet'></button></view>
                    </block>
                    <block wx:if="{{coupon.startLeft>0}}">
                      <view class="coupon-btn">暂未开抢</view>
                    </block>
                  </view>
                </view>
              </view>
            </view>
            <view class="coupon-info" wx:if="{{compon.couponData.length==1}}">
              <view class="info-wrap flex-wrap">
                <view class="flex-con flex-wrap">
                  <view class="left-info flex-wrap">
                    <view class="money">￥<text>{{coupon.value}}</text></view>
                  </view>
                  <view class="right-info flex-con">
                    <view class="coupon-title">
                      <view class="title"><text>{{coupon.name}}</text></view>
                    </view>
                    <view class="con-wrap flex-wrap">
                      <view class="desc-con flex-con">
                        <view class="desc">优惠券</view>
                        <text class="limit-use">{{coupon.limit==0?'无使用限制':'满'+coupon.limit+'减'+coupon.value}}</text>
                      </view>

                      <block wx:if="{{coupon.received==1}}">
                        <view class="coupon-btn">已经领取</view>
                      </block>
                      <block wx:if="{{coupon.startLeft==0&&coupon.received==0}}">
                        <view class="coupon-btn" wx:if="{{coupon.needShare==0}}" bindtap="getCoupon" data-id="{{coupon.id}}">{{coupon.hadReceive==1?'已经':'立即'}}领取</view>
                        <view class="coupon-btn" wx:if="{{coupon.needShare==1}}">分享领取<button open-type="share" data-index1="{{componIndex}}" data-index2='{{index}}' data-id="{{coupon.id}}" bindtap='shareGet'></button></view>
                      </block>
                      <block wx:if="{{coupon.startLeft>0}}">
                        <view class="coupon-btn">暂未开抢</view>
                      </block>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
    <!-- 橱窗 -->
    <view class="window-component" wx:if="{{compon.type=='window'}}">
      <view class="window-wrap style{{compon.windowStyle}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};padding-top:{{compon.style.paddingTop}};padding-bottom:{{compon.style.paddingBottom}};padding-left:{{compon.style.paddingLeft}};padding-right:{{compon.style.paddingRight}};height:{{compon.style.height}};background-color:{{compon.style.backgroundColor}};">
        <!-- 两列样式1 -->
        <block wx:if="{{compon.windowStyle==1}}">
          <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
            <image class="img" src="{{compon.link1.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link1.type}}" data-url="{{compon.link1.url}}" bindtap="openFenleiLink"></image>
            <block wx:if="{{compon.link1.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" path="{{compon.link1.path}}" app-id="{{compon.link1.url}}" open-type="navigate" />
            </block>
            <block wx:if="{{compon.link1.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
          <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
            <image class="img" src="{{compon.link2.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link2.type}}" data-url="{{compon.link2.url}}" bindtap="openFenleiLink"></image>
            <block wx:if="{{compon.link2.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link2.url}}" path="{{compon.link2.path}}" open-type="navigate" />
            </block>
            <block wx:if="{{compon.link2.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
        </block>
        <!-- 左一右二样式2 -->
        <block wx:if="{{compon.windowStyle==2}}">
          <view class="left-window">
            <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
              <image class="img" src="{{compon.link1.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link1.type}}" data-url="{{compon.link1.url}}" bindtap="openFenleiLink"></image>
              <block wx:if="{{compon.link1.type=='106'}}">
                <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link1.url}}" path="{{compon.link1.path}}" open-type="navigate" />
              </block>
              <block wx:if="{{compon.link1.type=='101'}}">
                <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
              </block>
            </view>
          </view>
          <view class="right-window">
            <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
              <image class="img" src="{{compon.link2.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link2.type}}" data-url="{{compon.link2.url}}" bindtap="openFenleiLink"></image>
              <block wx:if="{{compon.link2.type=='106'}}">
                <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link2.url}}" path="{{compon.link2.path}}" open-type="navigate" />
              </block>
              <block wx:if="{{compon.link2.type=='101'}}">
                <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
              </block>
            </view>
            <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
              <image class="img" src="{{compon.link3.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link3.type}}" data-url="{{compon.link3.url}}" bindtap="openFenleiLink"></image>
              <block wx:if="{{compon.link3.type=='106'}}">
                <navigator class="applet-jump" target="miniProgram" path="{{compon.link3.path}}" app-id="{{compon.link3.url}}" open-type="navigate" />
              </block>
              <block wx:if="{{compon.link3.type=='101'}}">
                <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
              </block>
            </view>
          </view>
        </block>
        <!-- 左二右一样式3 -->
        <block wx:if="{{compon.windowStyle==3}}">
          <view class="left-window">
            <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
              <image class="img" src="{{compon.link1.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link1.type}}" data-url="{{compon.link1.url}}" bindtap="openFenleiLink"></image>
              <block wx:if="{{compon.link1.type=='106'}}">
                <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link1.url}}" path="{{compon.link1.path}}" open-type="navigate" />
              </block>
            </view>
            <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
              <image class="img" src="{{compon.link3.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link3.type}}" data-url="{{compon.link3.url}}" bindtap="openFenleiLink"></image>
              <block wx:if="{{compon.link3.type=='106'}}">
                <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link3.url}}" path="{{compon.link3.path}}" open-type="navigate" />
              </block>
            </view>
          </view>
          <view class="right-window">
            <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
              <image class="img" src="{{compon.link2.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link2.type}}" data-url="{{compon.link2.url}}" bindtap="openFenleiLink"></image>
              <block wx:if="{{compon.link2.type=='106'}}">
                <navigator class="applet-jump" target="miniProgram" path="{{compon.link2.path}}" app-id="{{compon.link2.url}}" open-type="navigate" />
              </block>
            </view>
          </view>
        </block>
        <!-- 一行3个或4个样式 -->
        <block wx:if="{{compon.windowStyle==4||compon.windowStyle==5}}">
          <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
            <image class="img" src="{{compon.link1.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link1.type}}" data-url="{{compon.link1.url}}" bindtap="openFenleiLink" mode="aspectFill"></image>
            <block wx:if="{{compon.link1.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link1.url}}" path="{{compon.link1.path}}" open-type="navigate" />
            </block>
            <block wx:if="{{compon.link1.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
          <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
            <image class="img" src="{{compon.link2.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link2.type}}" data-url="{{compon.link2.url}}" bindtap="openFenleiLink" mode="aspectFill"></image>
            <block wx:if="{{compon.link2.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" path="{{compon.link2.path}}" app-id="{{compon.link2.url}}" open-type="navigate" />
            </block>
            <block wx:if="{{compon.link2.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
          <view class="window-item" style="padding:{{compon.imageStyle.padding}}">
            <image class="img" src="{{compon.link3.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link3.type}}" data-url="{{compon.link3.url}}" bindtap="openFenleiLink" mode="aspectFill"></image>
            <block wx:if="{{compon.link3.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link3.url}}" path="{{compon.link3.path}}" open-type="navigate" />
            </block>
            <block wx:if="{{compon.link3.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
          <view class="window-item" wx:if="{{compon.windowStyle==5}}" style="padding:{{compon.imageStyle.padding}}">
            <image class="img" src="{{compon.link4.imageUrl}}" style="border-radius:{{compon.imageStyle.borderRadius}}" data-type="{{compon.link4.type}}" data-url="{{compon.link4.url}}" bindtap="openFenleiLink" mode="aspectFill"></image>
            <block wx:if="{{compon.link4.type=='106'}}">
              <navigator class="applet-jump" target="miniProgram" app-id="{{compon.link4.url}}" path="{{compon.link4.path}}" open-type="navigate" />
            </block>
            <block wx:if="{{compon.link4.type=='101'}}">
              <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
            </block>
          </view>
        </block>
      </view>
    </view>
    <!-- 拼团商品组件 -->
    <view class="goods-component group-component" wx:if="{{compon.type=='group'&&compon.goodsData.length>0}}">
      <view class="group-goods-wrap" wx:if="{{compon.goodStyle==2}}">
        <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good">
          <view class="group-good flex-wrap border-b" data-id="{{good.id}}" bindtap='groupDetail'>
            <view class="good-cover">
              <image class="img" src="{{good.gcover}}" mode="aspectFill"></image>
              <view class="icon-wrap">
                <image class="icon" src="/images/group/icon_biaoqian.png"></image>
                <view class="desc" wx:if="{{good.gbtype!=4}}">{{good.total}}人团</view>
                <view class="desc" wx:if="{{good.gbtype==4}}">阶梯团</view>
              </view>
            </view>
            <view class="good-info flex-con">
              <view class="title">{{good.name}}</view>
              <view class="group-price-img flex-wrap1">
                <view class="ori-price">
                  <view class="desc">原价</view>
                  <view class="desc">￥{{good.oriprice}}</view>
                </view>
                <view class="price-img flex-con">
                  <image class="line" src="/images/group/line.png" mode="aspectFit"></image>
                  <view class="new-price">
                    拼团价:￥{{good.gprice}}<view class="triangle_border_down"><text></text></view>
                  </view>
                </view>
              </view>
              <view class="people-price flex-wrap">
                <view class="people">已拼{{good.hadTotal+good.sold}}人</view>
                <view class="price-wrap flex-con flex-wrap">
                  <view class="label">拼</view>
                  <view class="price"><text>￥</text>{{good.gprice}}</view>
                </view>
                <view class="group-btn">去拼团</view>
              </view>
            </view>
          </view>
        </block>
      </view>
      <scroll-view scroll-x="{{compon.goodStyle==4}}" wx:if="{{compon.goodStyle!=2}}" class="goods-list style{{compon.goodStyle}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};">
        <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good">
          <view class="good-item border-b" data-id="{{good.id}}" data-status="{{good.status}}" bindtap='groupDetail'>
            <view class="good-item-con">
              <view class="good-image">
                <image src="{{good.gcover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
              </view>
              <view class="good-intro">
                <view class="good-title" stylesheet style="color:{{compon.titleStyle.color}};font-size:{{compon.titleStyle.fontSize}};">{{good.name}}</view>
                <view class="good-brief" wx:if="{{compon.goodStyle==2}}">{{good.hadTotal}}人已团</view>
                <view class="good-price">
                  <view class="now-price {{compon.priceBold?'bold':''}}" style="color:{{compon.priceStyle.color}};">￥
                    <text class="text" style="font-size:{{compon.priceStyle.fontSize}};">{{good.gprice}}</text>
                    <!-- <text class="sold-num" wx:if="{{compon.goodStyle==2}}">{{good.hadTotal}}人已团</text>  -->
                    <text class='common-oriPrice'>{{good.oriPrice>0?'原价:'+good.oriPrice:''}}</text>
                  </view>
                  <view></view>
                </view>
                <view class="add-cart end" wx:if="{{good.status==3}}">已结束</view>
                <view class="add-cart" wx:else style="background-color:{{compon.openBgcolor}}">去开团</view>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
      <view wx:if="{{compon.goodStyle!=4&&compon.isShowmore}}" style="padding-bottom:18rpx;">
        <view class="see-more {{compon.goodStyle==1?'morestyle1':''}}" data-id="{{compon.goodSource}}" data-title="{{compon.sourceName}}" bindtap="groupIndex">查看全部拼团</view>
      </view>
    </view>
    <!-- 秒杀商品组件 -->
    <view class="goods-component seckill-component" wx:if="{{compon.type=='seckill'&&compon.goodsData.length>0}}">
      <block wx:if="{{compon.goodStyle!=5}}">
        <scroll-view scroll-x="{{compon.goodStyle==4}}" class="goods-list style{{compon.goodStyle}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};">
          <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good">
            <view class="good-item border-b" data-id="{{good.id}}" data-laid="{{good.limit.id}}" bindtap='goodDetail'>
              <view class="good-item-con">
                <view class="good-image">
                  <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                  <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                </view>
                <view class="good-intro">
                  <view class="good-title" stylesheet style="color:{{compon.titleStyle.color}};font-size:{{compon.titleStyle.fontSize}};">{{good.name}}</view>
                  <!-- <view class="good-brief" wx:if="{{compon.goodStyle==2}}">{{good.brief}}</view> -->
                  <view class="sold-percent flex-wrap">
                    <view class="flex-con">
                      <view class="start-time" wx:if="{{good.seckill==2}}">{{good.limitStartTime}}开始</view>
                      <view class="percent-show" wx:else>
                        <text class="percent-progress" style="width:{{good.limitHadSale}}%"></text>
                        <text class="percent-number">{{good.limitHadSale}}</text>
                      </view>
                    </view>
                  </view>
                  <view class="good-price">
                    <view class="now-price {{compon.priceBold?'bold':''}}" style="color:{{compon.priceStyle.color}};">￥
                      <text class="text" style="font-size:{{compon.priceStyle.fontSize}};">{{good.limitPrice}}</text>
                      <text wx:if="{{good.oriPrice>0&&compon.goodStyle==1}}" class="ori-price">{{good.oriPrice}}</text>
                      <text wx:if="{{good.oriPrice>0&&(compon.goodStyle==2||compon.goodStyle==3)}}" class="ori-price">￥{{good.oriPrice}}</text>
                    </view>
                  </view>
                  <view class="add-cart" wx:if="{{good.seckill==2}}" style="background-color:#4bb036;">即将开始</view>
                  <view class="add-cart" wx:if="{{good.seckill==1}}" style="background-color:{{compon.openBgcolor}}">马上抢</view>
                  <view class="add-cart" wx:if="{{good.seckill==0}}" style="background-color:#ffcccc;">已结束</view>
                </view>
              </view>
            </view>
          </block>
        </scroll-view>
        <view wx:if="{{compon.goodStyle!=4&&compon.isShowmore}}" style="padding-bottom:18rpx;">
          <view class="see-more {{compon.goodStyle==1?'morestyle1':''}}" data-id="{{compon.goodSource}}" data-title="{{compon.sourceName}}" bindtap="seckillIndex">查看全部秒杀</view>
        </view>
      </block>
      <!-- 秒杀样式5 -->
      <view class="skillComponent5" style="border:1px solid {{compon.activeBgcolor}};" wx:else>
        <view class="title-wrap flex-wrap">
          <view class="title-icon">
            <image src="/images/miaosha.png" mode="aspectFit"></image>
          </view>
          <view class="skill-tab flex-con">
            <block wx:key="id" wx:for="{{compon.goodsData}}" wx:for-item="tab">
              <view class="tabItem {{compon.curSort==index?'active':''}}" data-componindex="{{componIndex}}" data-tabindex="{{index}}" bindtap="chooseSkillTab" style="background-color:{{compon.curSort==index?compon.activeBgcolor:''}}">
                <view class="title">{{tab.name}}</view>
                <view class="desc">{{tab.statusNote}}</view>
              </view>
            </block>
          </view>
          <view class="more-btn" bindtap="seckillIndex">
            <text>更多</text>
            <image src="/images/icon_right_more.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="skill-wrap">
          <scroll-view class="skill-good-wrap" scroll-x wx:if="{{compon.goodsData[compon.curSort].goodsList.length>0}}">
            <block wx:key="id" wx:for="{{compon.goodsData[compon.curSort].goodsList}}" wx:for-item="good">
              <view class="skill-good" data-id="{{good.id}}" data-laid="{{good.limit.id}}" bindtap="goodDetail">
                <view class="cover">
                  <image src="{{good.cover}}" mode="aspectFill"></image>
                  <view class="good-label" wx:if="{{compon.goodsData[compon.curSort].status==1}}" style="background-color:{{compon.activeBgcolor}}">必抢</view>
                </view>
                <view class="title" style="color:{{compon.titleStyle.color}};">{{good.name}}</view>
                <view class="process-wrap" wx:if="{{compon.goodsData[compon.curSort].status==1}}">
                  <view class="process-slide" style="width:{{good.limitHadSale}}%;background-color:{{compon.activeBgcolor}}"></view>
                  <view class="process-num">已抢{{good.limitHadSale}}</view>
                </view>
                <view class="price-wrap flex-wrap">
                  <view class="price flex-con {{compon.priceBold?'bold':''}}" wx:if="{{compon.goodsData[compon.curSort].status!=2}}" style="color:{{compon.priceStyle.color}};">￥{{good.limitPrice}}</view>
                  <view class="price flex-con" wx:if="{{compon.goodsData[compon.curSort].status==2}}" style="color:{{compon.priceStyle.color}};">￥{{good.price}}</view>
                  <view class="ori-price">{{good.oriPrice}}元</view>
                </view>
              </view>
            </block>
          </scroll-view>
          <view class="no-data" wx:if="{{compon.goodsData[compon.curSort].goodsList.length<=0}}">
            <image src="/images/empty_img.png" mode="aspectFit"></image>
            <text>暂无相关商品哦~</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 砍价商品组件 -->
    <view class="goods-component bargain-component" wx:if="{{compon.type=='bargain'&&compon.goodsData.length>0}}">
      <scroll-view scroll-x="{{compon.goodStyle==4}}" class="goods-list style{{compon.goodStyle}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};">
        <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap='bargainDetail'>
            <view class="good-item-con">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
              </view>
              <view class="good-intro">
                <view class="good-title" stylesheet style="color:{{compon.titleStyle.color}};font-size:{{compon.titleStyle.fontSize}};">{{good.name}}</view>
                <!-- <view class="good-brief" wx:if="{{compon.goodStyle==2}}">{{good.brief}}</view>
								<view class="good-brief" wx:if="{{compon.goodStyle==2}}">{{good.sold}}人已砍</view> -->
                <block wx:if="{{compon.goodStyle==2}}">
                  <view class="ori-num flex-wrap">
                    <view class="ori-price flex-con" wx:if="{{good.oriPrice}}">￥{{good.oriPrice}}</view>
                    <view class="sold-num">{{good.sold}}人已砍</view>
                  </view>
                  <view class="new-price flex-wrap">
                    <view class="desc">最低<text>砍</text>至</view>
                    <view class="price {{compon.priceBold?'bold':''}}" style="color:{{compon.priceStyle.color}};font-size:{{compon.priceStyle.fontSize}};"><text>￥</text>{{good.minPrice}}</view>
                  </view>
                </block>
                <view class="good-price" wx:if="{{compon.goodStyle!=2}}">
                  <view class="now-price {{compon.priceBold?'bold':''}}" style="color:{{compon.priceStyle.color}};">￥
                    <text class="text" style="font-size:{{compon.priceStyle.fontSize}};">{{good.minPrice}}</text>
                    <!-- <text class="sold-num" wx:if="{{compon.goodStyle==2}}">{{good.hadTotal}}人已团</text>  -->
                  </view>
                </view>
                <view class="add-cart end" wx:if="{{good.status==2}}">已结束</view>
                <view class="add-cart" wx:else style="background-color:{{compon.openBgcolor}}">去砍价</view>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
      <view wx:if="{{compon.goodStyle!=4&&compon.isShowmore}}" style="padding-bottom:18rpx;">
        <view class="see-more {{compon.goodStyle==1?'morestyle1':''}}" data-id="{{compon.goodSource}}" data-title="{{compon.sourceName}}" bindtap="bargainIndex">查看全部砍价</view>
      </view>
    </view>
    <!-- 积分商品组件 -->
    <view class="jf-goods-list" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.type=='points'&&compon.goodsData.length>0}}">
      <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good" wx:if="{{compon.goodStyle==2}}">
        <view class="jf-good-item flex-wrap border-b" data-id="{{good.id}}" bindtap='tojfgoodDetail'>
          <image src="{{good.cover}}" class="img-ele" mode="aspectFill"></image>
          <view class="good-intro flex-con">
            <view class="good-name">
              <view class="name" style="max-width:520rpx;color:{{compon.titleStyle.color}};font-size:{{compon.titleStyle.fontSize}};">{{good.name}}</view>
            </view>
            <view class="good-desc flex-wrap">
              <view class="price flex-con">会员价
                <view class="integral">
                  <image src="/images/integralImg/jifen_2.png" class="icon_jf" mode="aspectFit"></image>{{good.points}}
                </view>
                <view class="add-icon">+</view>
                <view class="now-price">
                  <text class="text">￥{{good.price}}元</text>
                </view>
              </view>
              <view class="dh-btn">兑换</view>
            </view>
            <view class="price-sold flex-wrap">
              <view class="ori-price">
                <block wx:if="{{good.oriPrice>0}}">门市价：￥{{good.oriPrice}}　</block>
                <block wx:if="{{good.stockShow==1}}">库存 {{good.stock}}</block>
              </view>
              <view class="sold-num flex-con" wx:if="{{good.soldShow==1}}">已兑换{{good.sold}}份</view>
            </view>
          </view>
        </view>
      </block>
      <block wx:key="index" wx:for="{{compon.goodsData}}" wx:for-item="good" wx:if="{{compon.goodStyle==1}}">
        <view class="jf-good-item2 {{index!=compon.goodsData.length-1&&index!=compon.goodsData.length-2?'border-b':''}}" data-id="{{good.id}}" bindtap='tojfgoodDetail'>
          <view class="cover">
            <image class="img" src="{{good.cover}}" mode="aspectFill"></image>
          </view>
          <view class="title" style="color:{{compon.titleStyle.color}};font-size:{{compon.titleStyle.fontSize}};">{{good.name}}</view>
          <view class="price-wrap flex-wrap">
            <view class="price flex-con">
              <view class="integral">
                <image src="/images/integralImg/jifen_2.png" class="icon_jf" mode="aspectFit"></image>{{good.points}}
              </view>
              <view class="add-icon">+</view>
              <view class="now-price">
                <text class="text">￥{{good.price}}元</text>
              </view>
            </view>
          </view>
          <view class="sold-wrap flex-wrap">
            <view class="old-price flex-con" wx:if="{{good.oriPrice>0}}">
              门市价:￥{{good.oriPrice}}
            </view>
            <view class="sold-num" wx:if="{{good.soldShow==1}}">已兑换{{good.sold}}份</view>
          </view>
        </view>
      </block>
      <view wx:if="{{compon.isShowmore}}">
        <view class="see-more {{compon.goodStyle==1?'border-t':''}}" bindtap="myjfshop">查看全部积分商品</view>
      </view>
    </view>
    <!-- 分类列表组件 -->
    <view class="catelist-component" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};" wx:if="{{compon.type=='catelist'}}">
      <view class="scroll-wrap" wx:if="{{!compon.modelShow}}">
        <scroll-view scroll-x scroll-with-animation scroll-into-view="{{toViewCategoryId}}">
          <block wx:key="index" wx:for="{{compon.cateList}}">
            <view class="tab-item {{compon.categoryId==item.id?'active':''}}" id="category{{componIndex}}-{{index}}" style="color:{{compon.style.fontColor}};font-size:{{compon.style.fontSize}};" data-categoryid="{{item.id}}" data-componindex="{{componIndex}}" data-cateindex="{{index}}" data-name="{{item.name}}" bindtap="toggleChange">
              <text>{{item.name}}</text>
            </view>
          </block>
        </scroll-view>
        <view class="more-wrap border-l" data-componindex="{{componIndex}}" bindtap="openModel">
          <image src="/images/icon_all_fl.png" class="icon_more" mode="aspectFit"></image>
        </view>
      </view>
      <view class="model-con" wx:if="{{compon.modelShow}}">
        <view class="model-title flex-wrap">
          <view class="tishi-text flex-con">全部分类</view>
          <view class="close-btn" data-componindex="{{componIndex}}" bindtap="closeModel">
            <image src="/images/food-jt.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="type-labels">
          <block wx:key="index" wx:for="{{compon.cateList}}">
            <view class="label-box {{compon.categoryId==item.id?'active':''}}" data-model="model" style="color:{{compon.style.fontColor}};font-size:{{compon.style.fontSize}};" data-categoryid="{{item.id}}" data-componindex="{{componIndex}}" data-name="{{item.name}}" bindtap="toggleChange">
              <text>{{item.name}}</text>
            </view>
          </block>
        </view>
      </view>
    </view>
    <!-- 直播组件 -->
    <view class="liver-component" wx:if="{{compon.type=='live'}}" style="margin-top:{{compon.style.marginTop}};margin-bottom:{{compon.style.marginBottom}};">
      <live-components component-info="{{compon}}"></live-components>
    </view>
    <!-- 多多客商品组件 -->
    <view class="pdd-component" wx:if="{{compon.type=='duoduoke'&&compon.goodsData.length>0}}" style="background-color:#fff;">
      <pdd-component component-info="{{compon}}"></pdd-component>
    </view>
  </block>
</view>
<!-- 选择商品规格 -->
<buy-modal cur-goodinfo="{{curGoodinfo}}"></buy-modal>
<!--错误提示-->
<view class="error-tip fade_in" wx:if="{{errorTip.isShow}}">
  {{errorTip.text}}
</view>