.border-t,
.border-b,
.border-l,
.border-r {
  position: relative;
}

.border-t:before,
.border-b:after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  background-color: #e2e2e2;
  right: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  z-index: 1;
}

.border-l::before,
.border-r::after {
  content: '';
  position: absolute;
  bottom: 0;
  top: 0;
  background-color: #e2e2e2;
  left: 0;
  width: 1px;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  z-index: 1;
}

.border-b:after {
  top: auto;
  bottom: 0;
}

.border-r::after {
  right: 0;
  left: auto;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.thin-border {
  position: relative;
  -webkit-border-radius: 5px;
  border-radius: 5px;
}

.thin-border:after {
  content: '';
  position: absolute;
  top: -50%;
  bottom: -50%;
  left: -50%;
  right: -50%;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  border: 1px solid #bbb;
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

.flex-wrap {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -ms-box;
  display: box;
  display: flex;
  align-items: center;
}

.flex-wrap1 {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -ms-box;
  display: box;
  display: flex;
}

.flex-con {
  -webkit-box-flex: 1;
  -ms-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  box-flex: 1;
  flex: 1;
}

.flex-vertical {
  -webkit-box-orient: vertical;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  -webkit-flex-flow: column;
  flex-direction: column;
  flex-flow: column;
}

/* 数据渲染提示 */
.no-data-loading {
  padding: 50% 0;
}

.no-data-loading .load-img {
  width: 60rpx;
  height: 32rpx;
  display: block;
  margin: 0 auto;
}

.no-data-loading .loading-tip {
  font-size: 28rpx;
  color: #aaa;
  text-align: center;
}

/* vr图标分享 */
.icon-vr {
  position: absolute;
  left: 0;
  top: 20rpx;
  padding: 12rpx 20rpx 12rpx 10rpx;
  background-color: rgba(0, 0, 0, .5);
  border-radius: 0 40rpx 40rpx 0;
  z-index: 2;
  font-size: 0;
}

.icon-vr.bottom-right {
  left: auto;
  bottom: 25rpx;
  top: auto;
  right: 0;
  border-radius: 40rpx 0 0 40rpx;
}

.icon-vr .img {
  width: 40rpx !important;
  height: 40rpx !important;
  display: inline-block !important;
  vertical-align: middle;
  margin-right: 8rpx;
}

.icon-vr .text {
  font-size: 26rpx !important;
  display: inline-block !important;
  vertical-align: middle;
  line-height: 1.5 !important;
  color: #fff;
}

/* 轮播组件 */
.slide-component {
  width: 100%;
  height: 360rpx;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.slide-component .swiper {
  width: 100%;
  height: 100%;
}

.slide-component .swiper-item {
  position: relative;
}

.slide-component .swiper-item .applet-jump {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.slide-component .swiper-item .slide-image {
  display: block;
  width: 100%;
  height: 100%;
}

.slide-component .swiper .wx-swiper-dot {
  height: 12rpx !important;
  width: 12rpx !important;
  border-radius: 50% !important;
  margin: 0 8rpx;
}

/* 视频组件 */
.video-component {
  width: 100%;
  box-sizing: border-box;
  display: block;
}

.video-component .video {
  width: 100%;
  height: auto;
  display: block;
}

/* 分类导航组件 */
.fenlei-component {
  margin: 0 auto;
  background-color: #fff;
}

.fenlei-component .fl-nav-item,
.fenlei-component .fl-item {
  position: relative;
}

.fenlei-component .fl-nav-item .kefu-btn,
.fenlei-component .fl-item .kefu-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
}

.fenlei-component .fl-nav-item .text {
  white-space: nowrap;
  /* overflow-x: auto; */
}

.fenlei-component .style1 .scroll-view {
  padding: 15rpx 10rpx;
  box-sizing: border-box;
  white-space: nowrap;
  width: 100%;
  text-align: center;
}

/* .fenlei-component .style1 .scroll-view.styleAver { display: table; } */
.fenlei-component .style1 .fl-item {
  display: inline-block;
  vertical-align: middle;
  text-align: center;
}

.fenlei-component .style1 .scroll-view.styleAver .fl-item {
  display: table-cell;
  width: 1000rpx;
}

.fenlei-component .style1 .fl-item .img {
  display: block;
  margin: 0 auto;
  border-radius: 50%;
}

.fenlei-component .style1 .fl-item .text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  font-size: 28rpx;
  padding: 0 15rpx;
  margin-top: 8rpx;
}

.fenlei-component .style1.singleNum3 .fl-item {
  width: 30%;
}

.fenlei-component .style1.singleNum3 .fl-item .img {
  width: 115rpx;
  height: 115rpx;
}

.fenlei-component .style1.singleNum4 .fl-item {
  width: 23%;
}

.fenlei-component .style1.singleNum4 .fl-item .img {
  width: 105rpx;
  height: 105rpx;
}

.fenlei-component .style1.singleNum5 .fl-item {
  width: 19%;
}

.fenlei-component .style1.singleNum5 .fl-item .img {
  width: 95rpx;
  height: 95rpx;
}

.fenlei-component .style2.singleNum3 .swiper {
  height: 380rpx;
}

.fenlei-component .style2.singleNum4 .swiper {
  height: 360rpx;
}

.fenlei-component .style2.singleNum5 .swiper {
  height: 340rpx;
}

.fenlei-component .style2.singleNum3 .swiper.singleH {
  height: 190rpx;
}

.fenlei-component .style2.singleNum4 .swiper.singleH {
  height: 180rpx;
}

.fenlei-component .style2.singleNum5 .swiper.singleH {
  height: 170rpx;
}

.fenlei-component .style2 .wx-swiper-dot {
  position: relative;
  top: 6rpx;
  width: 6px !important;
  height: 6px !important;
}

.fenlei-component .style2 .fl-nav-wrap {
  padding: 25rpx 10rpx 8rpx;
  font-size: 0;
}

.fenlei-component .style2 .fl-nav-item {
  margin: 0 auto;
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 8rpx;
}

.fenlei-component .style2 .fl-nav-item .img {
  width: 84rpx;
  height: 84rpx;
  display: block;
  margin: 0 auto;
  border-radius: 35rpx;
}

.fenlei-component .style2 .fl-nav-item .text {
  text-align: center;
  font-size: 26rpx;
  line-height: 2;
  display: block;
}

.fenlei-component .style2.singleNum3 .fl-nav-item {
  width: 33.33%;
}

.fenlei-component .style2.singleNum3 .fl-nav-item .img {
  width: 110rpx;
  height: 110rpx;
}

.fenlei-component .style2.singleNum4 .fl-nav-item {
  width: 25%;
}

.fenlei-component .style2.singleNum4 .fl-nav-item .img {
  width: 100rpx;
  height: 100rpx;
}

.fenlei-component .style2.singleNum5 .fl-nav-item {
  width: 20%;
}

.fenlei-component .style2.singleNum5 .fl-nav-item .img {
  width: 90rpx;
  height: 90rpx;
}

.fenlei-component .style3 {
  overflow: hidden;
  padding: 10rpx;
  box-sizing: border-box;
}

.fenlei-component .style3 .fl-item {
  width: 50%;
  float: left;
  padding: 12rpx 8rpx;
  box-sizing: border-box;
}

.fenlei-component .style3 .fl-item .img {
  display: block;
  width: 90rpx;
  height: 90rpx;
  margin-right: 16rpx;
  border-radius: 15rpx;
}

.fenlei-component .style3 .fl-item .text {
  display: block;
  max-width: 226rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.fenlei-component .style3 .fl-item .name {
  font-size: 30rpx;
}

.fenlei-component .style3 .fl-item .brief {
  color: #999;
  font-size: 26rpx;
}

/* 搜索组件 */
.search-component .search-wrap {
  background-color: #fff;
  padding: 20rpx 0;
}

.search-component .search-wrap .search-con {
  border: 1px solid #eee;
  width: 94%;
  margin: 0 auto;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  background-color: #eee;
  padding: 0 26rpx;
  box-sizing: border-box;
  position: relative;
  color: #333;
}

.search-component .search-wrap .icon-search {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  top: 50%;
  right: 26rpx;
  margin-top: -20rpx;
}

/* 地址组件 */
.address-component {
  font-size: 28rpx;
}

.address-component .icon_addr {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

.address-component .address-detail {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 610rpx;
}

.address-component .icon_enter {
  width: 28rpx;
  height: 28rpx;
}

.address-component .style1 .address-wrap {
  padding: 25rpx 20rpx;
  background-color: #fff;
}

.address-component .style1 .address-detail {
  line-height: 1.1;
}

.address-component .style2 {
  padding: 12rpx 0;
  background-color: #fff;
}

.address-component .style2 .item-box {
  padding: 10rpx 20rpx;
}

.address-component .style2 .address-detail {
  line-height: 1.1;
}

.address-component .style3 {
  padding: 20rpx;
  background-color: #fff;
}

.address-component .style3 .shop-info .shop-logo {
  width: 95rpx;
  height: 95rpx;
  display: block;
  border-radius: 10rpx;
  margin-right: 15rpx;
}

.address-component .style3 .shop-name {
  position: relative;
  margin-bottom: 10rpx;
}

.address-component .style3 .shop-name .name {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 370rpx;
}

.address-component .style3 .tel-kefu {
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -40rpx;
}

.address-component .style3 .icon_tel,
.address-component .style3 .kefu {
  width: 66rpx;
  height: 66rpx;
  padding: 2rpx;
  box-sizing: border-box;
  margin-left: 15rpx;
  position: relative;
  display: inline-block;
}

.address-component .style3 .kefu .icon_tel {
  margin-left: 0;
}

.address-component .style3 .kefu .button {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 1;
  margin: 0;
}

.address-component .style3 .address-detail {
  max-width: 530rpx;
  color: #999;
}

.address-component .style3 .shop-brief {
  margin-top: 8rpx;
  color: #999;
  font-size: 28rpx;
}

.address-component .style3 .shop-brief .text {
  color: #38f;
}

/* 通知公告组件 */
.notice-component .notice-wrap {
  padding: 15rpx 25rpx;
}

.notice-component .tt-title {
  color: #FE6668;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.2;
  letter-spacing: 0.5px;
  width: 80rpx;
  text-align: center;
  margin-right: 15rpx;
}

.notice-component .notice-wrap .img {
  width: 140rpx;
  height: 35rpx;
  margin-right: 20rpx;
  position: relative;
  top: -2rpx;
}

.notice-component .notice-wrap .beauty-toutiao {
  box-sizing: border-box;
  overflow: hidden;
}

.notice-component .notice-wrap .beauty-toutiao.border-l:before {
  top: 10rpx;
  bottom: 10rpx;
  background-color: #E1E2E2;
}

.notice-component .notice-wrap .beauty-toutiao .swiper {
  width: 100%;
  height: 80rpx;
}

.notice-component .beauty-toutiao .toutiao-item {
  line-height: 60rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 28rpx;
  max-width: 600rpx;
}

.notice-component .beauty-toutiao .toutiao-item.style1 {
  line-height: 80rpx;
}

/* 新版样式 */
.notice-wrap .notice-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 20rpx;
}

.notice-wrap .notice-icon image {
  display: block;
  width: 100%;
  height: 100%;
}

/* 标题组件 */
.title-component {
  position: relative;
  background-color: #fff;
}

.title-component .title-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.title-component .title-label {
  position: relative;
  z-index: 1;
  padding: 0 20rpx;
  font-size: 32rpx;
}

.title-component .title-txt {
  line-height: 1.1;
  position: relative;
}

.title-component .title-txt.bold {
  font-weight: bold;
}

.title-component .title-label.style2 .title-txt {
  padding-left: 20rpx;
  text-align: left !important;
}

.title-component .title-label.style2 .title-txt .line {
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -6rpx;
  height: 12rpx;
  width: 12rpx;
  border-radius: 50%;
  background-color: #0898fa;
}

.title-component .title-label.style3 .title-txt {
  padding-left: 16rpx;
  text-align: left !important;
}

.title-component .title-label.style3 .title-txt .line {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 2rpx;
  width: 8rpx;
  border-radius: 8rpx;
  background-color: #0898fa;
}

.title-component .title-label.style4 {
  text-align: center !important;
}

.title-component .title-label.style4 .title-txt {
  padding-bottom: 6rpx;
}

.title-component .title-label.style4 .line {
  position: absolute;
  left: 50%;
  bottom: -6rpx;
  height: 6rpx;
  width: 120rpx;
  background-color: #0898fa;
  margin-left: -60rpx;
}

/* 图片组件 */
.image-component {
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
}

.image-component .img-box {
  margin: 0 auto;
  position: relative;
  width: 100%;
  height: 150rpx;
}

.image-component .img-box .applet-jump {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.image-component .img-box.left {
  margin-left: 0;
  margin-right: auto;
}

.image-component .img-box.center {
  margin-left: auto;
  margin-right: auto;
}

.image-component .img-box.right {
  margin-left: auto;
  margin-right: 0;
}

.image-component .img {
  display: block;
  width: 100%;
  height: 100%;
}

/* 按钮组件 */
.button-component {
  text-align: center;
  padding: 20rpx 0;
}

.button-component .cus-btn {
  display: inline-block;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 10rpx;
  background-color: #38f;
  padding: 0 20rpx;
  color: #fff;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  position: relative;
}

.button-component .cus-btn .applet-jump {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.button-component .btn-hover {
  opacity: .9;
}

/* 分割线组件 */
.space-component .space {
  display: block;
  border-top: 4rpx dashed #FE6668;
}

.space-component .space.left {
  margin-left: 0;
  margin-right: auto;
}

.space-component .space.center {
  margin-left: auto;
  margin-right: auto;
}

.space-component .space.right {
  margin-left: auto;
  margin-right: 0;
}

.space-component .text {
  display: block;
}

/*商品列表组件*/
.goods-component .good-intro {
  position: relative;
}

.goods-component .good-intro .good-title {
  font-size: 32rpx;
  line-height: 1.5;
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.goods-component .style1 .good-intro .good-title,
.goods-component .style4 .good-intro .good-title {
  font-size: 30rpx;
  height: 84rpx;
  line-height: 42rpx;
}

.goods-component .good-intro .good-brief {
  font-size: 26rpx;
  color: #999;
  height: 32rpx;
  line-height: 32rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-component .good-intro .good-price {
  color: #FF093C;
  font-size: 24rpx;
  line-height: 1;
  /* overflow: hidden; */
  width: 100%;
  margin-top: 10rpx;
}

.goods-component .good-price .text {
  font-size: 30rpx;
}

.goods-component .good-price .sold-num-new {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.goods-component .good-price .sold-num {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  vertical-align: top;
  font-weight: normal;
  float: left;
  height: 40rpx;
}
.sold-num-active {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  vertical-align: top;
  font-weight: normal;
  float: left;
  height: 40rpx;
}

.goods-component .good-intro .add-cart {
  position: absolute;
  bottom: 14rpx;
  right: 14rpx;
  height: 48rpx;
  width: 48rpx;
  border-radius: 50%;
  background-color: red;
  padding: 8rpx;
  box-sizing: border-box;
}

.goods-component .good-intro .add-cart image {
  width: 100%;
  height: 100%;
  display: block;
}

.goods-component .good-oriPrice {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999;
  line-height: 30rpx;
  text-decoration: line-through;
  font-weight: normal;
}

.goods-component .good-intro .good-add {
  position: absolute;
  top: 50%;
  right: 14rpx;
  height: 55rpx;
  width: 55rpx;
  border-radius: 50%;
  background-color: #F2F2F2;
  padding: 12rpx;
  box-sizing: border-box;
  margin-top: -26rpx;
}

.goods-component .good-intro .good-add .img {
  width: 100%;
  height: 100%;
  display: block;
}

.goods-component .goods-list.style1 .good-add,
.goods-component .goods-list.style4 .good-add,
.goods-component .goods-list.style5 .good-add {
  height: 44rpx;
  width: 44rpx;
  padding: 10rpx;
  margin-top: -22rpx;
}

.goods-component .goods-list.style1 {
  padding: 10rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.goods-list.style1 .good-item {
  width: 50%;
  float: left;
  padding: 10rpx;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  /* padding-bottom: 0rpx; */
}

.goods-component .goods-list.style1 .good-item.border-b:after {
  height: 0;
}

.goods-component .goods-list.style1 .good-item .good-item-con {
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}

.goods-component .good-item .good-image {
  position: relative;
}

.goods-component .good-item .good-image .no-good {
  width: 120rpx;
  height: 120rpx;
  line-height: 120rpx;
  font-size: 26rpx;
  text-align: center;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -60rpx;
  letter-spacing: 1rpx;
  z-index: 10;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}

.goods-component .goods-list.style1 .good-item .good-image {
  width: 100%;
  display: block;
  height: 346rpx;
  position: relative;
}

/* .goods-component .goods-list.style1 .good-image .img { width: 100%; height: 100%; display: block; } */
.goods-component .goods-list .good-image .vip-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 80rpx;
  height: 80rpx;
}

.goods-component .goods-list.style1 .good-image .img {
  display: block;
  height: 100%;
  width: 100%;
}

.goods-component .goods-list.style1 .good-intro {
  padding: 10rpx 16rpx 15rpx;
}

.goods-component .goods-list.style1 .good-intro .good-price {
  /* margin-top: 20rpx; */
}

.goods-component .good-price .ori-price {
  margin-left: 15rpx;
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.goods-component .goods-list.style2 .good-item {
  padding: 20rpx;
  padding-right: 0;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 0 10rpx #ddd;
  box-sizing: border-box;
  margin: 16rpx 20rpx 0;
}

.goods-component .goods-list.style2 .good-item-con {
  overflow: hidden;
}

.goods-component .goods-list.style2 .good-title {
  height: 84rpx;
  line-height: 42rpx;
  font-weight: 700;
}

.goods-component .goods-list.style2 .good-item .good-image {
  width: 220rpx;
  height: 220rpx;
  float: left;
  position: relative;
}

.goods-component .goods-list.style2 .good-item .good-image .img {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 8rpx;
}

.goods-component .goods-list.style2 .good-item .ori-num {
  padding: 10rpx 20rpx 10rpx 0;
  color: #999;
  font-size: 26rpx;
}

.goods-component .goods-list.style2 .good-item .ori-num .ori-price {
  text-decoration: line-through;
}

.goods-component .goods-list.style2 .good-item .new-price {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 10rpx;
  padding-left: 20rpx;
  box-sizing: border-box;
  line-height: 1;
  font-size: 0;
}

.goods-component .goods-list.style2 .good-item .new-price .desc {
  color: #333;
  font-size: 26rpx;
}

.goods-component .goods-list.style2 .good-item .new-price .desc text {
  display: inline-block;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  width: 34rpx;
  height: 34rpx;
  line-height: 36rpx;
  text-align: center;
  border-radius: 50%;
  background-color: #FF0015;
}

.goods-component .goods-list.style2 .good-item .new-price .price {
  color: #FF0015;
  font-size: 34rpx;
  margin-left: 10rpx;
}

.goods-component .goods-list.style2 .good-item .new-price .price.bold {
  font-weight: bold;
}

.goods-component .goods-list.style2 .good-item .new-price .price text {
  font-size: 24rpx;
}

.goods-component .goods-list.style2 .good-image .vip-tag {
  left: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
}

.goods-component .goods-list.style2 .good-intro {
  padding-left: 20rpx;
  float: left;
  width: 452rpx;
  position: relative;
  box-sizing: border-box;
  height: 220rpx;
}

.goods-component .goods-list.style2 .good-price {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  padding-left: 20rpx;
  box-sizing: border-box;
  line-height: 1;
}

.goods-component .goods-list.style2 .good-intro .add-cart {
  bottom: 0;
  right: 4rpx;
  border-radius: 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  width: 130rpx;
}

.goods-component .goods-list.style3 .good-item {
  padding: 20rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.goods-component .goods-list.style3 .good-item .good-image .img {
  width: 100%;
  height: 100%;
  display: block;
}

.goods-component .goods-list.style3 .good-image .vip-tag {
  left: 20rpx;
  top: 20rpx;
  width: 90rpx;
  height: 90rpx;
}

.goods-component .goods-list.style3 .good-intro {
  margin-top: 20rpx;
  width: 100%;
}

.goods-component .goods-list.style3 .good-intro .good-price {
  margin-top: 8rpx;
}

.goods-component .goods-list.style3 .good-intro .add-cart {
  bottom: 0;
  right: 4rpx;
}

.goods-component .goods-list.style3 .good-item .good-image {
  width: 710rpx;
  height: 710rpx;
  position: relative;
}

.goods-component .goods-list.style3 .good-item .good-image.style1_1 {
  height: 710rpx;
}

/*1:1图*/
.goods-component .goods-list.style3 .good-item .good-image.style3_2 {
  height: 473rpx;
}

/*3:2图*/
.goods-component .goods-list.style3 .good-item .good-image.style16_9 {
  height: 399rpx;
}

/*16:9图*/
.goods-component .goods-list.style4 {
  padding: 8rpx 6rpx 6rpx;
  overflow: hidden;
  font-size: 0;
  white-space: nowrap;
  box-sizing: border-box;
}

.goods-component .good-intro .add-cart.end {
  opacity: 0.2;
}

.goods-component .goods-list.style4 .good-item {
  width: 45%;
  display: inline-block;
  padding: 6rpx;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  vertical-align: top;
}

.goods-component .goods-list.style4 .good-item.border-b:after {
  height: 0;
}

.goods-component .goods-list.style4 .good-item .good-item-con {
  background-color: #fff;
}

.goods-component .goods-list.style4 .good-item .good-image {
  width: 100%;
  display: block;
  height: 320rpx;
  position: relative;
}

.goods-component .goods-list.style4 .good-image .img {
  width: 100%;
  height: 100%;
  display: block;
}

.goods-component .goods-list.style4 .good-image .vip-tag {
  left: 6rpx;
  top: 6rpx;
  width: 70rpx;
  height: 70rpx;
}

.goods-component .goods-list.style4 .good-intro {
  padding: 10rpx 16rpx 15rpx;
}

.goods-component .goods-list.style4 .good-intro .good-price {
  margin-top: 20rpx;
}

.goods-component .now-price.bold {
  font-weight: bold;
}

.see-more {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #fff;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.see-more.morestyle1 {
  width: 97%;
  margin: 0 auto;
  border-radius: 6rpx;
}

.goods-list.style4 .see-more {
  display: none;
}

.goods-component .goods-list.style5 {
  padding: 8rpx 6rpx 6rpx;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #fff;
}

.goods-component .goods-list.style5 .good-title {
  height: 90rpx;
  line-height: 1.5;
}

.goods-list.style5 .good-item {
  width: 33.33%;
  float: left;
  padding: 6rpx;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}

.goods-component .goods-list.style5 .good-item.border-b:after {
  height: 0;
}

.goods-component .goods-list.style5 .good-item .good-item-con {
  background-color: #fff;
}

.goods-component .goods-list.style5 .good-item .good-image {
  width: 100%;
  display: block;
  height: 232rpx;
  position: relative;
}

.goods-component .goods-list.style5 .good-image .vip-tag {
  width: 60rpx;
  height: 60rpx;
}

.goods-component .goods-list.style5 .good-image .img {
  display: block;
  height: 100%;
  width: 100%;
}

.goods-component .goods-list.style5 .good-intro {
  padding: 10rpx 12rpx 15rpx;
}

.goods-component .goods-list.style5 .good-intro .good-price {
  margin-top: 8rpx;
}

/* .goods-component .goods-list.style5 .sold-num {
  display: none;
} */

.goods-component .goods-list.style5 .good-oriPrice {
  display: block;
  margin-left: 0;
  min-height: 10rpx;
  color: #d1d1d1;
  font-size: 22rpx;
  height: 22rpx;
}

/*图文列表样式*/
.pictxt-component .pic-list {
  box-sizing: border-box;
}

.pictxt-component .pic-list .pic-item {
  position: relative;
}

.pictxt-component .pic-item .applet-jump {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.pictxt-component .pic-list .pic-item-con {
  position: relative;
  border-radius: 4rpx;
  overflow: hidden;
  background-color: #fff;
  box-sizing: border-box;
}

.pictxt-component .pic-list .img-box {
  width: 100%;
  display: block;
}

.pictxt-component .pic-list .img-box .img {
  width: 100%;
  display: block;
}

.pic-item .pic-intro {
  padding: 14rpx 16rpx;
  box-sizing: border-box;
}

.pic-item .pic-brief {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 32rpx;
  height: 32rpx;
}

.pic-item .pic-title {
  font-size: 26rpx;
  line-height: 1.4;
  box-sizing: border-box;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.pictxt-component .pic-list.style1 {
  white-space: nowrap;
  font-size: 0;
  padding: 10rpx 8rpx;
  box-sizing: border-box;
  background-color: #fff;
  text-align: center;
}

.pictxt-component .pic-list.style1 .pic-item {
  display: inline-block;
  vertical-align: top;
  padding: 8rpx;
  box-sizing: border-box;
}

.pictxt-component .pic-list.style1.imgnum1 .pic-item {
  width: 100%;
}

.pictxt-component .pic-list.style1.imgnum2 .pic-item {
  width: 60%;
}

.pictxt-component .pic-list.style1.imgnum3 .pic-item {
  width: 40%;
}

.pictxt-component .pic-list.style1.imgnum4 .pic-item {
  width: 30%;
}

.pictxt-component .pic-list.style1 .pic-item.border-b:after {
  height: 0;
}

.pictxt-component .pic-list.style1 .pic-intro.style1 {
  background-color: rgba(0, 0, 0, .5);
  color: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}

.pictxt-component .pic-list.style2 {
  overflow: hidden;
  padding: 10rpx;
  box-sizing: border-box;
}

.pictxt-component .pic-list.style2 .pic-item {
  float: left;
  padding: 6rpx;
  box-sizing: border-box;
  overflow: hidden;
}

.pictxt-component .pic-list.style2 .pic-item:after {
  height: 0;
}

.pictxt-component .pic-list.style2.imgnum1 .pic-item {
  width: 100%;
}

.pictxt-component .pic-list.style2.imgnum2 .pic-item {
  width: 50%;
}

.pictxt-component .pic-list.style2.imgnum3 .pic-item {
  width: 33.33%;
}

.pictxt-component .pic-list.style2.imgnum4 .pic-item {
  width: 25%;
}

/* 优惠券 */
.new-coupon-wrap {
  padding: 22rpx 0 22rpx 22rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.new-coupon-wrap .scroll-view {
  width: 100%;
  white-space: nowrap;
  font-size: 0;
}

.new-coupon-wrap .coupon-item {
  display: inline-block;
  position: relative;
  line-height: 1.35;
  overflow: hidden;
  margin-right: 22rpx;
}

.new-coupon-wrap .style1 .coupon-item {
  display: block;
  width: 640rpx;
  height: 202rpx;
  margin: 0 33rpx;
}

.new-coupon-wrap .style1 .left-info {
  text-align: center;
  margin-top: 40rpx;
  height: 140rpx;
}

.new-coupon-wrap .style1 .left-info .money {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  padding: 10rpx 50rpx 10rpx 30rpx;
  border-right: 1rpx solid rgba(255, 255, 255, 0.6);
}

.new-coupon-wrap .style1 .money text {
  font-size: 60rpx;
}

.new-coupon-wrap .style1 .coupon-title {
  padding-top: 10rpx;
}

.new-coupon-wrap .style1 .right-info {
  height: 180rpx;
  text-align: left;
  padding: 0 40rpx;
}

.new-coupon-wrap .style1 .con-wrap {
  margin-top: 10rpx;
}

.new-coupon-wrap .style1 .right-info .desc {
  margin-bottom: 20rpx;
}

.new-coupon-wrap .style1 .right-info .coupon-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #fff;
  color: #F85C4E;
  margin-top: 0;
  font-size: 30rpx;
  box-sizing: border-box;
  padding: 12rpx;
  line-height: 38rpx;
  text-align: center;
}

.new-coupon-wrap .style2 .coupon-item,
.new-coupon-wrap .style3 .coupon-item {
  width: 342rpx;
  height: 186rpx;
  /* height:175rpx; */
}

.new-coupon-wrap .style3 .coupon-item {
  width: 320rpx;
  margin-right: 0;
}

.new-coupon-wrap .style3 .coupon-item:last-child {
  margin-right: 20rpx;
}

.new-coupon-wrap .coupon_bg {
  display: block;
  width: 100%;
  height: 100%;
}

.new-coupon-wrap .coupon-info {
  position: absolute;
  top: 10rpx;
  left: 35rpx;
  right: 30rpx;
  bottom: 10rpx;
  z-index: 1;
}

.new-coupon-wrap .info-wrap {
  height: 118rpx;
}

.new-coupon-wrap .style1 .info-wrap {
  height: 180rpx;
}

.new-coupon-wrap .coupon-title {
  text-align: center;
}

.new-coupon-wrap .coupon-title .title {
  position: relative;
  display: inline-block;
  padding: 10rpx 10rpx 0;
}

.new-coupon-wrap .coupon-title text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 22rpx;
  display: inline-block;
  max-width: 150rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.new-coupon-wrap .coupon-title .title:after,
.new-coupon-wrap .coupon-title .title:before {
  width: 30rpx;
  height: 2rpx;
  background-color: rgba(255, 255, 255, 0.8);
  content: '';
  position: absolute;
  top: 50%;
  margin-top: 2rpx;
}

.new-coupon-wrap .coupon-title .title:after {
  right: -30rpx;
}

.new-coupon-wrap .coupon-title .title:before {
  left: -30rpx;
}

.new-coupon-wrap .left-info .money {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 6rpx;
}

.new-coupon-wrap .money text {
  font-size: 42rpx;
  display: inline-block;
  margin-top: -10rpx;
}

.new-coupon-wrap .limit-use {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.new-coupon-wrap .right-info {
  text-align: center;
}

.new-coupon-wrap .right-info .desc {
  color: #fff;
  font-size: 32rpx;
}

.new-coupon-wrap .right-info .coupon-btn {
  color: rgba(255, 255, 255, 0.8);
  font-size: 22rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  padding: 6rpx 10rpx;
  margin-top: 10rpx;
  position: relative;
}

.new-coupon-wrap .right-info .coupon-btn button {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 1;
  margin: 0;
}

/* 橱窗 */
.window-wrap {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.window-wrap .window-item .img {
  display: block;
  height: 100%;
  width: 100%;
}

.window-wrap .window-item {
  box-sizing: border-box;
  position: relative;
}

.window-wrap .window-item .applet-jump {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.window-wrap.style1 .window-item {
  height: 100%;
  width: 50%;
  float: left;
}

.window-wrap.style2 .left-window,
.window-wrap.style3 .left-window,
.window-wrap.style2 .right-window,
.window-wrap.style3 .right-window {
  width: 50%;
  height: 100%;
  float: left;
}

.window-wrap.style2 .left-window .window-item {
  height: 100%;
  width: 100%;
}

.window-wrap.style2 .right-window .window-item {
  height: 50%;
  width: 100%;
}

.window-wrap.style3 .left-window .window-item {
  height: 50%;
  width: 100%;
}

.window-wrap.style3 .right-window .window-item {
  height: 100%;
  width: 100%;
}

.window-wrap.style4 .window-item {
  width: 33.33%;
  height: 100%;
  float: left;
}

.window-wrap.style5 .window-item {
  width: 25%;
  height: 100%;
  float: left;
}

/* 拼团不同于商品列表样式 */
.group-component .good-intro .add-cart {
  width: 110rpx;
  text-align: center;
  font-size: 26rpx;
  color: #fff;
  border-radius: 8rpx;
  line-height: 50rpx;
  padding: 0;
}

.group-component .group-goods-wrap {
  background-color: #fff;
}

.group-goods-wrap .group-good {
  padding: 26rpx 20rpx;
}

.group-goods-wrap .group-good:last-child .border-b:after,
.group-goods-wrap .group-good:last-child.border-b:after {
  height: 0;
}

.group-goods-wrap .good-cover {
  width: 230rpx;
  height: 230rpx;
  position: relative;
  margin-right: 20rpx;
}

.group-goods-wrap .good-cover .img {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.group-goods-wrap .good-cover .icon-wrap {
  width: 120rpx;
  height: 116rpx;
  position: absolute;
  top: -6rpx;
  left: -6rpx;
}

.group-goods-wrap .good-cover .icon-wrap .icon {
  display: block;
  width: 100%;
  height: 100%;
}

.group-goods-wrap .good-cover .icon-wrap .desc {
  color: #fff;
  font-size: 20rpx;
  width: 120rpx;
  position: absolute;
  top: 0;
  left: -12rpx;
  transform: rotate(-44deg);
  padding-left: 8rpx;
}

.group-goods-wrap .good-info .title {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  font-size: 30rpx;
  color: #333;
  height: 80rpx;
  line-height: 40rpx;
  font-weight: 700;
}

.group-goods-wrap .good-info .group-price-img {
  position: relative;
  padding: 16rpx 0 16rpx 40rpx;
}

.group-goods-wrap .good-info .group-price-img .price-img {
  position: relative;
}

.group-goods-wrap .good-info .group-price-img .price-img .line {
  display: block;
  width: 198rpx;
  height: 64rpx;
}

.group-goods-wrap .good-info .group-price-img .ori-price {
  text-align: center;
  font-size: 20rpx;
  color: #666;
}

.group-goods-wrap .good-info .group-price-img .new-price {
  position: absolute;
  top: 0;
  left: 80rpx;
  background-color: #FFE7E9;
  color: #FF0619;
  font-size: 22rpx;
  padding: 2rpx 14rpx;
  border-radius: 20rpx;
}

.group-goods-wrap .people-price .people {
  color: #333;
  font-size: 24rpx;
  margin-right: 4rpx;
}

.triangle_border_down {
  width: 0;
  height: 0;
  border-width: 10rpx 10rpx 0;
  border-style: solid;
  border-color: #FFE7E9 transparent transparent;
  position: absolute;
  bottom: -10rpx;
  left: 38rpx;
}

.triangle_border_down span {
  display: block;
  width: 0;
  height: 0;
  border-width: 8rpx 8rpx 0;
  border-style: solid;
  border-color: #fc0 transparent transparent;
  position: absolute;
  top: -29rpx;
  left: -28rpx;
}

.group-goods-wrap .people-price .price-wrap .label {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  background-color: #FF0315;
  color: #fff;
  text-align: center;
  font-weight: bold;
  border-radius: 50%;
  margin-right: 2rpx;
  font-size: 28rpx;
}

.group-goods-wrap .people-price .price-wrap .price {
  color: #333;
  font-weight: bold;
  font-size: 44rpx;
}

.group-goods-wrap .people-price .price-wrap .price text {
  font-size: 24rpx;
}

.group-goods-wrap .group-btn {
  background-color: #FF0315;
  color: #fff;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
}

/* 秒杀不同于商品列表样式 */
.seckill-component .good-intro .good-brief {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  word-break: normal;
  max-width: 480rpx;
  height: 32rpx;
  margin-top: 10rpx;
}

.seckill-component .good-intro .add-cart {
  min-width: 110rpx;
  text-align: center;
  font-size: 26rpx;
  color: #fff;
  border-radius: 8rpx;
  line-height: 50rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  white-space: nowrap;
  width: auto;
}

.seckill-component .start-time {
  font-size: 26rpx;
  color: #ff3641;
  line-height: 24rpx;
}

.seckill-component .goods-list.style2 .start-time {
  color: #48AE38;
  border: 2rpx solid #48AE38;
  border-radius: 6rpx;
  display: inline-block;
  padding: 4rpx 10rpx;
  font-size: 24rpx;
}

.seckill-component .sold-percent {
  margin-top: 6rpx;
  margin-bottom: 35rpx;
}

.seckill-component .goods-list.style2 .sold-percent {
  margin-top: 30rpx;
  margin-bottom: 0;
}

.seckill-component .goods-list.style2 .good-price {
  bottom: 10rpx;
}

.seckill-component .sold-percent .percent-num {
  font-size: 24rpx;
  color: #ff3641;
  margin-right: 10rpx;
}

.seckill-component .sold-percent .percent-show {
  height: 24rpx;
  width: 75%;
  background-color: #fefefe;
  position: relative;
  box-sizing: border-box;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  overflow: hidden;
}

.seckill-component .sold-percent .percent-show .percent-progress {
  position: absolute;
  left: 0;
  top: -1rpx;
  height: 24rpx;
  background-color: #ff373f;
  border-radius: 12rpx;
  background: -webkit-linear-gradient(left, #ffd9da, #ff7483);
  background: linear-gradient(left, #ffd9da, #ff7483);
  transition: all 0.5s;
}

.seckill-component .sold-percent .percent-show .percent-number {
  position: absolute;
  right: 8rpx;
  top: 1rpx;
  height: 22rpx;
  line-height: 22rpx;
  color: #ff3641;
  font-size: 20rpx;
  left: auto;
}

.seckill-component .sold-percent .progress {
  border-radius: 10rpx;
}

/* 秒杀样式5 */
.skillComponent5 {
  margin: 10rpx 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  border: 1px solid #F16745;
  /* box-shadow: 0 0 10rpx #eee; */
}

.skillComponent5 .title-wrap {
  padding: 0 20rpx;
  border-bottom: 1px solid #eee;
}

.skillComponent5 .title-icon {
  width: 150rpx;
  height: 37rpx;
  margin-right: 10rpx;
}

.skillComponent5 .title-icon image {
  display: block;
  width: 100%;
  height: 100%;
}

.skillComponent5 .title-wrap .tabItem {
  display: inline-block;
  padding: 15rpx 10rpx;
  width: 130rpx;
  box-sizing: border-box;
  text-align: center;
  color: #111;
  font-size: 32rpx;
}

.skillComponent5 .title-wrap .tabItem.active {
  background-color: #F1522B;
  color: #fff;
}

.skillComponent5 .title-wrap .tabItem.active .desc {
  color: #fff;
}

.skillComponent5 .title-wrap .tabItem .title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.skillComponent5 .title-wrap .tabItem .desc {
  color: #999;
  font-size: 24rpx;
}

.skillComponent5 .more-btn text {
  font-size: 26rpx;
  color: #999;
  vertical-align: middle;
}

.skillComponent5 .more-btn image {
  /* width:14rpx;
  height:23.33rpx; */
  width: 26rpx;
  height: 26rpx;
  vertical-align: middle;
  margin-left: 5rpx;
  margin-top: -2rpx;
}

.skillComponent5 .skill-wrap {
  padding: 15rpx 10rpx;
}

.skillComponent5 .skill-good-wrap {
  white-space: nowrap;
}

.skillComponent5 .skill-good-wrap::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.skillComponent5 .skill-good {
  display: inline-block;
  width: 210rpx;
  margin: 0 10rpx;
}

.skillComponent5 .skill-good .cover {
  width: 210rpx;
  height: 210rpx;
  position: relative;
  margin-bottom: 12rpx;
}

.skillComponent5 .skill-good .cover .good-label {
  background-color: #F1522B;
  color: #fff;
  border-radius: 0 10rpx 10rpx 6rpx;
  font-size: 22rpx;
  padding: 0 16rpx;
  position: absolute;
  left: 0;
  bottom: 0;
}

.skillComponent5 .skill-good .cover image {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}

.skillComponent5 .skill-good .title {
  color: #333;
  font-size: 30rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.skillComponent5 .skill-good .process-wrap {
  width: 100%;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #FCDCD5;
  position: relative;
  margin-top: 12rpx;
  overflow: hidden;
}

.skillComponent5 .process-wrap .process-slide {
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #F1522B;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.skillComponent5 .process-wrap .process-num {
  color: #fff;
  height: 40rpx;
  line-height: 40rpx;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 8rpx;
  font-size: 20rpx;
  z-index: 2;
}

.skillComponent5 .price-wrap {
  margin-top: 12rpx;
  color: #F1522B;
  font-size: 28rpx;
}

.skillComponent5 .price-wrap .price.bold {
  font-weight: bold;
}

.skillComponent5 .price-wrap .ori-price {
  color: #999;
  font-size: 22rpx;
  text-decoration: line-through;
}

/* 砍价不同于商品列表样式 */
.bargain-component .good-intro .add-cart {
  width: 110rpx;
  text-align: center;
  font-size: 26rpx;
  color: #fff;
  border-radius: 8rpx;
  line-height: 50rpx;
  padding: 0;
}

.error-tip {
  padding: 20rpx 25rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  position: fixed;
  top: 50%;
  left: 50%;
  border-radius: 4px;
  transform: translate(-50%, -50%);
  z-index: 10000009;
  max-width: 80%;
  display: inline-block;
  text-align: center;
}

/* 单行滑动查看更多 */
.single-more {
  width: 150rpx;
  position: relative;
  height: 100%;
}

.single-more .text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 28rpx;
  width: 60rpx;
  letter-spacing: 2rpx;
  line-height: 1.4;
  text-align: center;
}

/* 商品图片标签 */
.good-img-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #ff6912;
  height: 36rpx;
  line-height: 36rpx;
  padding: 0 16rpx 0 10rpx;
  border-bottom-right-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}

/* 积分商品样式 */
.jf-goods-list {
  background-color: #fff;
  padding-left: 20rpx;
}

.jf-good-item {
  padding: 20rpx 25rpx 20rpx 0;
}

.jf-good-item.border-b:last-child::after {
  height: 0;
}

.jf-good-item .img-ele {
  height: 140rpx;
  width: 140rpx;
  display: block;
  margin-right: 25rpx;
  background-color: #f0f0f0;
}

.jf-good-item .good-intro {
  min-height: 140rpx;
  position: relative;
}

.jf-good-item .good-name {
  font-size: 32rpx;
  line-height: 44rpx;
}

.jf-good-item .good-name .name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 430rpx;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.jf-good-item .good-name .text {
  font-size: 24rpx;
  color: #999;
  display: block;
  text-align: right;
}

.jf-good-item .good-intro .good-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 36rpx;
}

.jf-good-item .good-desc .brief {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 390rpx;
}

.jf-good-item .good-desc .text {
  font-size: 24rpx;
  color: #999;
  display: block;
  text-align: right;
}

.jf-good-item .good-intro .tag-box {
  margin-top: 8rpx;
}

.jf-good-item .tag-box .text {
  padding: 6rpx 8rpx;
  border: 1px solid #f9b66d;
  color: #f9b66d;
  border-radius: 6rpx;
  font-size: 24rpx;
  display: inline-block;
  margin-right: 6rpx;
  line-height: 1;
  margin-bottom: 6rpx;
}

.jf-good-item .good-intro .good-price {
  position: absolute;
  left: 0;
  bottom: 0;
  line-height: 35rpx;
}

.jf-good-item .good-intro .price {
  margin-top: 6rpx;
  font-size: 26rpx;
  color: #333;
}

.jf-good-item .good-intro .dh-btn {
  height: 48rpx;
  line-height: 50rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #fff;
  border-radius: 30rpx;
  background-color: #ff604f;
}

.jf-good-item .good-intro .dh-btn.gray {
  color: #999;
  padding: 0 2rpx;
  background-color: #fff;
}

.jf-good-item .good-intro .now-price {
  font-size: 26rpx;
  color: #ff333d;
  margin-right: 15rpx;
  display: inline-block;
  vertical-align: middle;
}

.jf-good-item .good-intro .integral {
  font-size: 26rpx;
  color: #333;
  display: inline-block;
}

.jf-good-item .integral .icon_jf {
  height: 20rpx;
  width: 20rpx;
  margin-right: 6rpx;
  position: relative;
  top: 2rpx;
}

.jf-good-item .now-price .text {
  font-size: 32rpx;
  display: inline;
  color: #ff333d;
}

.jf-good-item .good-intro .ori-price {
  font-size: 24rpx;
  color: #999;
  display: inline-block;
}

.jf-good-item .good-intro .cus-btn {
  height: 52rpx;
  line-height: 50rpx;
  border-radius: 30rpx;
  background-color: #ff5f56;
  border: 1px solid #ff4331;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: inline-block;
  color: #fff;
  width: 120rpx;
  box-sizing: border-box;
  text-align: center;
  margin-bottom: -8rpx;
}

.jf-good-item .price-sold .vip-price {
  height: 40rpx;
  line-height: 44rpx;
  border-radius: 22rpx;
  background-color: #ff5f56;
  border: 1px solid #ff4331;
  color: #fff;
  padding: 0 15rpx;
  font-size: 26rpx;
}

.jf-good-item .price-sold .sold-num {
  font-size: 24rpx;
  text-align: right;
  color: #999;
}

/* 商品排版2 */
.jf-good-item2 {
  width: 340rpx;
  display: inline-block;
  padding: 18rpx 0;
  margin-right: 30rpx;
}

.jf-good-item2:nth-of-type(2n) {
  margin-right: 0;
}

.jf-good-item2 .cover {
  width: 340rpx;
  height: 340rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.jf-good-item2 .cover .img {
  width: 100%;
  height: 100%;
}

.jf-good-item2 .title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 430rpx;
  margin-bottom: 4rpx;
  font-size: 30rpx;
}

.jf-good-item2 .now-price {
  font-size: 26rpx;
  color: #ff333d;
  margin-right: 15rpx;
  display: inline-block;
  vertical-align: middle;
}

.jf-good-item2 .integral {
  font-size: 26rpx;
  color: #333;
  display: inline-block;
}

.jf-good-item2 .integral .icon_jf {
  height: 20rpx;
  width: 20rpx;
  margin-right: 6rpx;
  position: relative;
  top: 2rpx;
}

.jf-good-item2 .now-price .text {
  font-size: 32rpx;
  display: inline;
  color: #ff333d;
}

.jf-good-item2 .old-price {
  font-size: 24rpx;
  color: #999;
  display: inline-block;
}

.jf-good-item2 .sold-num {
  font-size: 24rpx;
  color: #999;
}

/* 分类列表组件 */
.catelist-component {
  background-color: #fff;
}

.catelist-component .scroll-wrap {
  height: 90rpx;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 4rpx 10rpx #e8e8e8;
  position: relative;
  padding-left: 10rpx;
  box-sizing: border-box;
}

.catelist-component .scroll-wrap scroll-view {
  width: 660rpx;
  white-space: nowrap;
}

.catelist-component .scroll-wrap scroll-view .tab-item {
  display: inline-block;
  min-width: 18%;
  text-align: center;
  font-size: 30rpx;
  color: #5d5d5d;
  padding: 0 16rpx;
  box-sizing: border-box;
  vertical-align: middle;
}

.catelist-component .scroll-wrap scroll-view .tab-item text {
  display: inline-block;
  height: 90rpx;
  line-height: 90rpx;
  vertical-align: middle;
}

.catelist-component .scroll-wrap scroll-view .tab-item.active text {
  box-sizing: border-box;
  color: #000;
  font-weight: bold;
  font-size: 32rpx;
}

.catelist-component .scroll-wrap .more-wrap {
  background-color: #fff;
  height: 90rpx;
  text-align: center;
  width: 90rpx;
  position: absolute;
  top: 0rpx;
  right: 0;
}

.catelist-component .scroll-wrap .more-wrap image.icon_more {
  display: block;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 24rpx;
}

.catelist-component .scroll-wrap .more-type {
  padding: 10rpx 0;
}

.catelist-component .scroll-wrap .more-type text {
  font-size: 24rpx;
  color: #60AFFE;
}

.catelist-component .scroll-wrap .more-type image {
  display: block;
  width: 31rpx;
  height: 12rpx;
  margin: 0 auto;
}

.catelist-component .model-con .model-title {
  padding: 0;
}

.catelist-component .model-con .model-title .tishi-text {
  color: #333333;
  font-size: 30rpx;
  padding-left: 20rpx;
}

.catelist-component .model-con .model-title .close-btn {
  width: 80rpx;
  height: 90rpx;
}

.catelist-component .model-con .model-title .close-btn image {
  display: block;
  width: 100%;
  height: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  transform: rotate(-90deg);
}

.catelist-component .model-con .type-labels {
  padding: 0 12rpx 15rpx;
  max-height: 660rpx;
  overflow: auto;
  font-size: 0;
}

.catelist-component .model-con .type-labels .label-box {
  display: inline-block;
  width: 25%;
  text-align: center;
  padding: 12rpx;
  box-sizing: border-box;
}

.catelist-component .model-con .type-labels .label-box text {
  height: 64rpx;
  line-height: 64rpx;
  display: block;
  box-sizing: border-box;
  margin: 0 auto;
  overflow: hidden;
  white-space: nowrap;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
}

.catelist-component .model-con .type-labels .label-box.active text {
  border-color: #ff8132;
  color: #ff8132;
}

/* 商品组件价格展示新样式 */
.price-box {
  position: relative;
}

.slod-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10rpx;
}

.slod-box .ori-price {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin: 0 20rpx 0 0 !important;
}

.slod-box .ori-price text {
  text-decoration: line-through;
}

.add-icon {
  display: inline-block;
  font-size: 26rpx;
  margin: 0 10rpx;
}