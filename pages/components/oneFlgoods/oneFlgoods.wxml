<!--pages/components/oneFlgoods/oneFlgoods.wxml-->
<!--折叠菜单  -->
<fold-menu></fold-menu>
<view class="page-con">
  <view class="no-data" wx:if="{{!showLoading&&oneFl.length<=0}}">
    <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
    <text>暂无对应商品哦~</text>
  </view>
  <view class="search-wrap" id="searchWrap">
    <view class="search-container" bindtap="searchPage">
      <image src="/images/icon_fl_sousuo.png" mode="aspectFit"></image>
      <text>在店内搜索</text>
    </view>
  </view>
  <view class="goods-wrap" wx:if="{{oneFl.length>0}}">
    <scroll-view class="one-fl border-r" scroll-y scroll-into-view="{{goIntoView}}" scroll-with-animation="true">
      <block wx:key="index" wx:for="{{oneFl}}">
        <view wx:if="{{fontsize_status == 0}}" class="fl-item {{item.id==curId?'active':''}}" id="onefl{{item.id}}" data-id="{{item.id}}" bindtap="toggleOnefl"><text style="background:{{item.id==curId?themeColor1:'#fff'}};">{{item.name}}</text></view>
        <view wx:if="{{fontsize_status == 1}}" class="fl-item-two {{item.id==curId?'active':''}}" id="onefl{{item.id}}" data-id="{{item.id}}" bindtap="toggleOnefl"><text style="background:{{item.id==curId?themeColor1:'#fff'}};">{{item.name}}</text></view>
      </block>
    </scroll-view>
    <view class="goods-list-wrap">
      <pull-up-load wrap-height="100%" show-tip="{{(showLoading||shopGoods.length<=0)?false:true}}" can-move="{{noMoretip}}" bindpullUpEnd="pullUpEnd">
        <view class="goods-list" wx:if="{{shopGoods.length>0}}">
          <block wx:key="index" wx:for="{{shopGoods}}" wx:for-item="good">
            <view class="good-item border-b flex-wrap" data-id="{{good.id}}" bindtap="goodDetail">
              <!-- <image class="good-img fade_in" src="{{good.cover}}" mode="aspectFill"></image> -->
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="sold-tag" wx:if="{{good.stock==0}}">已售罄</view>
              </view>
              <view class="good-intro flex-con">
                <view class="good-title {{fontsize_status == 1 ?'common-style':''}}"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text><text>{{good.name}}</text></view>
                <view class="good-price {{fontsize_status == 1 ?'common-style':''}}"><text>￥{{good.price}}</text> </view>
                <view class="num-change">
                  <!-- <image src="/images/icon_jia.png" wx:if="{{good.stock>0}}" mode="aspectFit" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addminusCart"></image> -->
                  <!-- 购物车按钮 -->
                  <view class="good-add" wx:if="{{good.stock>0}}" mode="aspectFit" data-curgood="{{good}}" data-isformat="{{good.hasFormat}}" data-gid="{{good.id}}" catchtap="addtoCart">
                    <image src="/images/icon_gwc_black.png" mode="aspectFit" class="img"></image>
                  </view>
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="no-data" wx:if="{{!showLoading&&shopGoods.length<=0}}">
          <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
          <text>暂无对应商品哦~</text>
        </view>
        <!--上拉加载提示-->
        <view class="loading-tip" wx:if="{{showLoading}}">
          <view class="icon_load">
            <view id="floatingBarsG">
              <view class="blockG" id="rotateG_01"></view>
              <view class="blockG" id="rotateG_02"></view>
              <view class="blockG" id="rotateG_03"></view>
              <view class="blockG" id="rotateG_04"></view>
              <view class="blockG" id="rotateG_05"></view>
              <view class="blockG" id="rotateG_06"></view>
              <view class="blockG" id="rotateG_07"></view>
              <view class="blockG" id="rotateG_08"></view>
            </view>
          </view>
          <text>努力加载中...</text>
        </view>
        <!-- <view class="nomore-tip" wx:if="{{noMoretip&&shopGoods.length>0}}">没有更多数据了</view> -->
      </pull-up-load>
    </view>
  </view>
  <!-- 返回首页 -->
  <view class="cart-enter" wx:if="{{showSy}}" style="background-color:{{themeColor1?themeColor1:'#ff8132'}};bottom:120rpx;padding:16rpx;" bindtap="backsy">
    <image src="/images/icon_backIndex.png" mode="aspectFit"></image>
  </view>
  <!-- 购物车入口 -->
  <view wx:if="{{appidStatus!='wxce5035eb4c3cd8fe'}}" class="cart-enter" style="background-color:{{themeColor1?themeColor1:'#ff8132'}};" bindtap="toCartPage">
    <text class="num" wx:if="{{cartNum>0}}">{{cartNum}}</text>
    <image src="/images/icon_add_cart.png" mode="aspectFit"></image>
  </view>
  <!-- 选择商品规格 -->
  <buy-modal cur-goodinfo="{{curGoodinfo}}"></buy-modal>
</view>
<!--错误提示-->
<view class="error-tip fade_in" wx:if="{{errorTip.isShow}}">
  {{errorTip.text}}
</view>