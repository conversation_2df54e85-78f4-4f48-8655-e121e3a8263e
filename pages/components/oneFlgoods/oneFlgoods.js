// pages/components/oneFlgoods/oneFlgoods.js
import {
  getGlobalData,
  setNavColor
} from "../../../utils/reuseFunc"
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isRefresh: {
      type: Boolean,
      value: false,
      observer: 'onPullDownRefresh'
    },
    fontsizeStatus: {
      type: String,
      value: ""
    },
    isBottom: {
      type: Boolean,
      value: false,
      observer: 'onReachBottom'
    },
    queryData: {
      type: Object,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showLoading: true,
    noMoretip: false,
    page: 0,
    curId: '',
    goIntoView: '',
    buyNumber: 1
  },
  lifetimes: {
    attached: function () {
      var that = this;
      that.initData();
      that.setData({
        appidStatus: app.globalData.appid
      })
      console.log(that.data.appidStatus, '8520')
    },

  },
  pageLifetimes: {

    show: function () {
      var that = this;
      app.setCartnum(this); //更新购物车数量
    },
    hide: function () {
      // 页面被隐藏
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  },
  attached: function () {
    var that = this;
    this.getGlobalData('themeColor'); //获取主题配色
    that.initData();
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getGlobalData,
    initData: function () {
      var that = this;
      that.setData({
        fontsize_status: that.properties.fontsizeStatus
      })
      that.isTagpage();
      app.setNavtitle('分类商品');
      setNavColor.call(this);
      var queryData = that.data.queryData;
      if (queryData && queryData.id) {
        that.setData({
          curId: queryData.id
        })
      }
      that.requestAllGoods('one');
      app.setCartnum(); //更新购物车数量
    },
    toggleOnefl: function (e) {
      var that = this,
        id = e.currentTarget.dataset.id,
        queryData = that.data.queryData;
      queryData.id = id;
      that.triggerEvent('changeQuery', queryData)
      that.setData({
        page: 0,
        noMoretip: false,
        showLoading: true,
        curId: id,
        goIntoView: 'onefl' + id,
        // shopGoods: null
      })
      that.requestAllGoods();
    },
    requestAllGoods: function (type) {
      var that = this,
        page = that.data.page,
        data = {
          map: 'applet_mall_category_goodsInfoList_new',
          page: page,
          cate: that.data.curId
        };
      // console.log(data);
      let listConfig = {
        showLoading: true,
        stopPull: true,
        showError: false
      }
      // if (type == 'one') {
      //   listConfig.showLoading = true
      // }
      wx.$get(data, listConfig).then(res => {
        let responseData = res.data;
        var allArr = [];
        var initArr = that.data.shopGoods ? that.data.shopGoods : '[]';
        var curArr = responseData.goods;
        var lastPageLength = curArr.length;
        if (page > 0) {
          allArr = initArr.concat(curArr);
        } else {
          allArr = responseData.goods
        }
        var hasCurid = false;
        var category = responseData.category;
        var curId = responseData.category && responseData.category.length > 0 ? responseData.category[0].id : '';
        var goIntoView = responseData.category && responseData.category.length > 0 ? 'onefl' + responseData.category[0].id : '';
        if (type == 'one') {
          that.setData({
            oneFl: category
          })
          if (that.data.curId) {
            // console.log("已确定分类"+that.data.curId)
            for (let i = 0; i < category.length; i++) {
              if (category[i].id == that.data.curId) {
                hasCurid = true;
              }
            }
            if (hasCurid) {
              curId = that.data.curId;
              goIntoView = 'onefl' + that.data.curId;
            }
          }
          that.setData({
            curId: curId,
            goIntoView: goIntoView
          })
        }
        that.setData({
          shopGoods: allArr.length > 0 ? allArr : [],
          cartNum: responseData.cartNum
        })
        if (lastPageLength < 10) {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
        // console.log(that.data.shopGoods);
        if (page == 0) {
          wx.pageScrollTo({
            scrollTop: 0
          })
        }
        wx.hideLoading()
      }).catch(err => {
        if (page <= 0) {
          that.setData({
            shopGoods: []
          })
        } else {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      })
    },
    requestFltype: function () {
      var that = this;
      wx.$get({
        map: 'applet_goods_style'
      }).then(res => {
        console.log(res)
        var datainfo = res.data;
        that.setData({
          fontsize_status: datainfo.fontsize_status,
        })
        console.log(that.data.fontsize_status)
      }).catch(err => {
        console.log(err)
      })
    },
    onPullDownRefresh: function () {
      var that = this;
      that.setData({
        page: 0,
        noMoretip: false,
        showLoading: true,
        curId: ''
      });
      that.requestFltype()
      that.requestAllGoods('one');

    },
    onReachBottom: function () {
      var that = this;

      var isMore = that.data.noMoretip;
      var page = that.data.page;
      page++;
      that.setData({
        page: page
      });
      if (!isMore) {
        that.requestAllGoods();
      }
    },
    goodDetail: function (e) {
      var goodId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/pages/goodDetail/goodDetail?id=' + goodId
      })
    },
    changeShowType: function (e) {
      var type = e.target.dataset.type;
      this.setData({
        showType: type
      })
    },
    searchPage: function () {
      wx.navigateTo({
        url: '/pages/searchList/searchList'
      })
    },
    sortGood: function (e) {
      var type = e.target.dataset.sort;
      this.setData({
        sortType: type
      })
      this.onPullDownRefresh();
    },
    toCartPage: function () {
      wx.navigateTo({
        url: '/subpages/mycart/mycart'
      })
    },
    isTagpage: function () {
      var that = this;
      var menu = app.globalData.menuTitle;
      var tabbarPage = [];
      for (var i in menu) {
        tabbarPage.push(i);
      }
      var path = getCurrentPages()[getCurrentPages().length - 1].__route__;
      var isTabPage = false;
      for (var i = 0; i < tabbarPage.length; i++) {
        if (tabbarPage[i] == path) {
          isTabPage = true;
        }
      }
      that.setData({
        isTabPage: isTabPage
      })
      var enterFrom = app.globalData.enterFrom;
      if (!isTabPage) {
        that.setData({
          showSy: true
        })
      }
    },
    backsy: function () {
      app.backHome();
    },
    addtoCart: function (e) {
      var that = this;
      var good = e.currentTarget.dataset.curgood;
      var isformat = e.currentTarget.dataset.isformat;
      if (isformat) {
        app.errorTip(that, '多规格商品请在商品详情页添加', 2000);
        return;
      } else {
        that.confirmAddcart(good);
      }
    },
    confirmAddcart: function (curGoods) {
      var that = this,
        esid = curGoods.esid,
        gid = curGoods.id,
        gfid = curGoods && curGoods.id ? curGoods.id : '',
        num = 1;
      var data = {
        map: 'applet_add_cart',
        esid: esid,
        gid: gid,
        num: num,
        add: 1
      }
      wx.$get(data)
        .then(res => {
          app.errorTip(that, '已加入购物车', 1000);
          that.setData({
            cartNum: res.data.cartNum
          })
          app.globalData.requestCartNum = res.data.cartNum;
          app.setCartnum();
        })
    },
    pullUpEnd(e) {
      var that = this,
        curId = that.data.curId,
        oneFl = that.data.oneFl,
        nextIndex = 0;
      for (let i = 0; i < oneFl.length; i++) {
        if (curId == oneFl[i].id) {
          if (i < oneFl.length - 1) {
            nextIndex = i + 1;
          }
        }
      }
      let event = {
        currentTarget: {
          dataset: {
            id: oneFl[nextIndex].id
          }
        }
      }
      this.toggleOnefl(event)
    },
    onShareAppMessage: function () {
      var that = this;
      var title = '分类商品';
      var shareInfo = app.globalData.shareInfo;
      title = shareInfo.shareTitle ? shareInfo.shareTitle : title;
      app.getPoint(that);
      var curId = that.data.curId ? that.data.curId : '';
      return {
        title: title,
        path: '/pages/oneFlgoods/oneFlgoods?id=' + curId
      }
    },
  }
})