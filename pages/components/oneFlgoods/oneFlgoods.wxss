/* pages/components/oneFlgoods/oneFlgoods.wxss */
@import "../../../app.wxss";

page {
  background-color: #fff;
}

.page-con {
  min-height: 100vh;
  background-color: #fff;
}

.no-data {
  padding: 40% 0;
}

.no-data image {
  height: 160rpx;
  width: 160rpx;
  display: block;
  margin: 0 auto;
}

.no-data text {
  display: block;
  line-height: 2;
  font-size: 26rpx;
  text-align: center;
  color: #aaa;
}

.search-wrap {
  padding: 15rpx 0;
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  box-sizing: border-box;
  z-index: 10;
  background-color: #fff;
}

.search-container {
  width: 95%;
  margin: 0 auto;
  height: 80rpx;
  line-height: 80rpx;
  box-sizing: border-box;
  background-color: #f2f2f2;
  text-align: left;
  padding: 0 20rpx;
  border-radius: 40rpx;
}

.search-wrap image {
  height: 36rpx;
  width: 36rpx;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.search-wrap text {
  display: inline-block;
  vertical-align: middle;
  color: #999;
  font-size: 28rpx;
}

.goods-wrap {
  width: 100%;
  height: 100vh;
  padding-top: 110rpx;
  box-sizing: border-box;
}

.one-fl {
  position: fixed;
  left: 0;
  top: 110rpx;
  width: 28%;
  height: 100%;
  box-sizing: border-box;
  padding: 10rpx 0;
  z-index: 1;
  padding-bottom: 110rpx;
  background-color: #fff;
}

.one-fl .fl-item,
.one-fl .fl-item-two {
  padding: 14rpx;
}

.one-fl .fl-item text,
.one-fl .fl-item-two text {
  display: block;
  height: 57rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  border-radius: 30rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background 0.3s;
}

.one-fl .fl-item-two text {
  font-size: 32rpx;
}

.one-fl .fl-item.active text,
.one-fl .fl-item-two.active text {
  background: -webkit-linear-gradient(left, #fea26d, #fe813a);
  background: linear-gradient(left, #fea26d, #fe813a);
  color: #fff;
}

.one-fl .fl-item-two.active text {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}

.goods-list-wrap {
  box-sizing: border-box;
  padding-left: 28%;
  height: 100%;
}

.good-list {
  background-color: #fff;
}

.good-item {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.good-item:last-child:after {
  height: 0;
}

.good-enter {
  background-color: #f7f8f9;
}

.good-item .good-image {
  width: 150rpx;
  height: 150rpx;
  display: block;
  margin-right: 20rpx;
  position: relative;
}

.good-item .good-image .vip-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 60rpx;
  height: 60rpx;
}

.good-item .good-image .sold-tag {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  height: 48rpx;
  line-height: 48rpx;
  width: 100%;
  text-align: center;
  color: #fff;
  font-size: 26rpx;
  background-color: rgba(0, 0, 0, 0.4);
}

.good-item .good-image .img {
  display: block;
  height: 100%;
  width: 100%;
}

.good-item .good-intro {
  height: 150rpx;
  position: relative;
}

.good-item .good-title {
  font-size: 30rpx;
  line-height: 1.45;
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.good-item .good-price {
  position: absolute;
  bottom: 0;
  left: 0;
  font-size: 34rpx;
  color: #fa3951;
}

.num-change {
  overflow: hidden;
  position: absolute;
  bottom: 0;
  right: 0;
}

.num-change image,
.num-change text {
  float: left;
  height: 50rpx;
  width: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 30rpx;
}

.num-change text {
  padding: 0 5rpx;
  width: auto;
  min-width: 30rpx;
  color: #666;
}

.num-change .choose-guige {
  border-radius: 25rpx;
  background-color: #ff883f;
  font-size: 24rpx;
  width: 90rpx;
  color: #fff;
}

.num-change .good-add .img {
  width: 100%;
  height: 100%;
  display: block;
}

.num-change .good-add {
  width: 48rpx;
  height: 48rpx;
  padding: 12rpx;
  border-radius: 50%;
  background-color: #F2F2F2;
  box-sizing: border-box;
}

/*规格弹出层  */
.format-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

.format-modal .modal-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.format-modal .format-content {
  position: absolute;
  left: 9%;
  top: 50%;
  width: 82%;
  z-index: 2;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 6rpx;
  overflow: hidden;
  padding-top: 30rpx;
}

.format-content .close {
  position: absolute;
  top: 0;
  right: 0;
  width: 70rpx;
  height: 70rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

.format-content .close image {
  width: 100%;
  height: 100%;
}

.format-content .dish-name {
  font-size: 36rpx;
  width: 85%;
  margin: 0 auto;
  text-align: center;
  font-weight: bold;
  line-height: 1.6;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.format-box {
  padding: 0 25rpx;
}

.format-box .label-name {
  display: block;
  font-size: 26rpx;
  color: #999;
}

.format-box .format-tag text {
  margin: 20rpx 0;
  margin-right: 20rpx;
  display: inline-block;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  border-radius: 26rpx;
  padding: 0 15rpx;
  border: 1rpx solid #9B9B9B;
  color: #9B9B9B;
  font-size: 26rpx;
}

.format-box .format-tag text.active {
  border: 1rpx solid #ff8833;
  color: #ff8833;
}

.price-opera {
  background-color: #f9f9f9;
  padding: 20rpx 25rpx;
  margin-top: 20rpx;
}

.price-opera .price {
  color: #ff2532;
  font-size: 26rpx;
}

.price-opera .price text {
  font-size: 38rpx;
}

.price-opera .addcart {
  height: 64rpx;
  line-height: 64rpx;
  display: inline-block;
  text-align: center;
  padding: 0 15rpx;
  color: #fff;
  background-color: #ff8833;
  border-radius: 6rpx;
  min-width: 140rpx;
  box-sizing: border-box;
}

/* 购物车入口 */
.cart-enter {
  position: fixed;
  left: 20rpx;
  bottom: 20rpx;
  z-index: 10;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  padding: 18rpx;
  box-sizing: border-box;
}

.cart-enter image {
  display: block;
  height: 100%;
  width: 100%;
}

.cart-enter .num {
  font-size: 20rpx;
  background-color: #e6231f;
  color: #fff;
  min-width: 32rpx;
  padding: 0 5rpx;
  height: 32rpx;
  line-height: 30rpx;
  border-radius: 32rpx;
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  text-align: center;
  box-sizing: border-box;
}

.contact-wrap {
  right: 20rpx;
  left: auto;
  bottom: 120rpx;
}

/* 加大加粗样式 */
.common-style text {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}