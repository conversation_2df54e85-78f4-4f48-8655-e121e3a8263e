// components/navbar/index.js
const app = getApp();
var showMesstimeout;
Component({
  properties: {
    isShow: {
      type: Boolean,
      value: true,
      observer:'getOrdertip'
    },
    isDetail: {
      type: Boolean,
      value: ''
    }
  },
  data: {
    
  },
  attached: function () {
    var that = this;
    // that.getOrdertip();
  },
  methods: {
    getOrdertip: function () {
      var that = this;

      wx.$get({
        map: 'applet_order_index_list'
      },{
        pageList:true
      }).then(res=>{
        that.setData({
          messageList: res.data
        })
        if (that.data.messageList.length > 0) {
          clearTimeout(showMesstimeout);
          that.showMessage(0);
        }else{
          clearTimeout(showMesstimeout);
          that.setData({
            messageShow: '',
            isShowmess: false
          })
        }
      }).catch(err=>{
        clearTimeout(showMesstimeout);
            that.setData({
              messageShow: '',
              isShowmess: false
            })
      })
    },
    showMessage: function (index) {
      var that = this;
      var message = that.data.messageList;
      if (message.length > 0) {
        if (index >= message.length) {
          index = 0;
        }
        that.setData({
          messageShow: message[index],
          isShowmess: true
        })
        showMesstimeout = setTimeout(function () {
          index++;
          that.setData({
            isShowmess: false
          })
          that.showMessage(index);
        }, 6000)
      }
    },
  }
})