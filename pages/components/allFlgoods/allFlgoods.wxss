/* pages/components/allFlgoods/allFlgoods.wxss */
@import "../../../app.wxss";

.page-con {
  height: 100vh;
  background-color: #fff;
}

.search-wrap {
  padding: 15rpx 0;
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  box-sizing: border-box;
  z-index: 10;
  background-color: #fff;
}

.search-container {
  width: 95%;
  margin: 0 auto;
  height: 80rpx;
  line-height: 80rpx;
  box-sizing: border-box;
  background-color: #f2f2f2;
  text-align: left;
  padding: 0 20rpx;
  border-radius: 40rpx;
  font-size: 0;
}

.search-wrap image {
  height: 36rpx;
  width: 36rpx;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
}

.search-wrap text {
  display: inline-block;
  vertical-align: middle;
  color: #999;
  font-size: 28rpx;
}

/* 分类商品样式 */
.flgoods-wrap {
  height: 100%;
  padding-top: 110rpx;
  box-sizing: border-box;
}

.flgoods-wrap scroll-view {
  height: 100%;
  box-sizing: border-box;
  float: left;
}

.flgoods-wrap .left-scroll-view {
  width: 25%;
  background-color: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  padding-top: 110rpx;
  z-index: 1;
  box-sizing: border-box;
}

.flgoods-wrap .right-scroll-view {
  width: 100%;
  background-color: #fff;
  min-height: 100%;
  box-sizing: border-box;
  padding-left: 25%;
}

.left-good-fl .good-flname,
/* 加大加粗 */
.left-good-fl .good-flname-two {
  text-align: center;
  color: #333;
  font-size: 28rpx;
  line-height: 1.3;
  padding: 30rpx 15rpx;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
}

.left-good-fl .good-flname-two {
  font-size: 32rpx;
}

.left-good-fl .good-flname.active,
.left-good-fl .good-flname-two.active {
  background-color: #fff;
  color: #FF8132;
}

.left-good-fl .good-flname-two.active {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}

.left-good-fl .good-flname.border-b:after {
  background-color: #dfdfdf;
  bottom: -1rpx;
}

.left-good-fl .good-flname .chooseNum {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  height: 32rpx;
  line-height: 32rpx;
  min-width: 32rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #fff;
  padding: 0 4rpx;
  background-color: #FF2A37;
  box-sizing: border-box;
}

.fl-img {
  width: 92%;
  margin: 0 auto;
  height: 158rpx;
  display: block;
  border-radius: 6rpx;
}

.flname-title {
  padding: 30rpx 20rpx;
  font-size: 32rpx;
  color: #333;
  text-align: center;
}

.flname-title text {
  display: inline-block;
  text-align: center;
  position: relative;
}

.flname-title text:before,
.flname-title text:after {
  content: '';
  position: absolute;
  top: 49%;
  height: 1px;
  width: 45rpx;
  background-color: #ccc;
  transform: scaleY(0.5);
  left: -60rpx;
}

.flname-title text:after {
  right: -60rpx;
  left: auto;
}

.good-list-item {
  margin-bottom: 20rpx;
}

.second-goodfl {
  overflow: hidden;
  padding: 0 2%;
}

.second-item {
  width: 33.33%;
  box-sizing: border-box;
  float: left;
  margin-bottom: 10rpx;
}

.second-item image {
  height: 100rpx;
  width: 100rpx;
  display: block;
  margin: 0 auto;
}

.second-item text {
  line-height: 1.8;
  text-align: center;
  display: block;
  font-size: 27rpx;
  margin-top: 12rpx;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 购物车入口 */
.cart-enter {
  position: fixed;
  left: 20rpx;
  bottom: 20rpx;
  z-index: 10;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  padding: 18rpx;
  box-sizing: border-box;
}

.cart-enter image {
  display: block;
  height: 100%;
  width: 100%;
}

.cart-enter .num {
  font-size: 20rpx;
  background-color: #e6231f;
  color: #fff;
  min-width: 32rpx;
  padding: 0 5rpx;
  height: 32rpx;
  line-height: 30rpx;
  border-radius: 32rpx;
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  text-align: center;
  box-sizing: border-box;
}

.common-style text {
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'PingFang SC Regular';
}