// pages/components/allFlgoods/allFlgoods.js
import {getGlobalData,setNavColor} from "../../../utils/reuseFunc"
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isRefresh:{
      type:Boolean,
      value:false,
      observer:'onPullDownRefresh'
    },
    fontsizeStatus:{
      type:String,
      value:""
    },
    isBottom:{
      type:Boolean,
      value:false,
      observer:'onReachBottom'
    },
    queryData:{
      type:Object,
      value:{}
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    isShowall: true
  },
  pageLifetimes: {
    attached: function () {
      var that = this;
      that.initData();
    },
    show: function () {
      var that = this;
      if (that.data.title){
        app.setNavtitle(that.data.title);
      }
      app.setCartnum();//更新购物车数量
    },
    hide: function () {
      // 页面被隐藏
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  },
  attached: function () {
    var that = this;
    this.getGlobalData('themeColor');//获取主题配色
    that.initData();
  },
  methods: {
    getGlobalData,
    initData: function (e) {
      var that = this;
      console.log(that)
      that.setData({
        fontsize_status:that.properties.fontsizeStatus
      })
      setNavColor.call(this);
      that.isTagpage();
      var queryData = that.data.queryData;
      if (queryData && queryData.title) {
        that.setData({
          title: queryData.title
        })
        app.setNavtitle(queryData.title);
      } else {
        app.setNavtitle('商品分类');
      }
      that.requestGoodFl();
      that.setData({
        isShowall: true,
        curGoodFl: null
      })
    },
    requestGoodFl: function () {

      var that = this;
      var data = {
        map : 'applet_goods_category_list'
      };
      
      wx.$get(data,{
        stopPull:true
      }).then(res=>{
        that.setData({
          goodsFlList: res.data,
        })
      }).catch(err=>{
        console.log(err)
      })
    },
    searchPage: function () {
      wx.navigateTo({
        url: '/pages/searchList/searchList'
      })
    },
    requestFltype: function () {
      var that = this;
      wx.$get({
        map: 'applet_goods_style'
      }).then(res => {
        console.log(res)
        var datainfo = res.data;
        that.setData({
          fontsize_status: datainfo.fontsize_status,
        })
        console.log(that.data.fontsize_status)
      }).catch(err => {
        console.log(err)
      })
    },
    onPullDownRefresh: function () {
      var that = this;
      that.setData({
        isShowall: true,
        curGoodFl: null
      })
      that.requestFltype()
      that.requestGoodFl();
    },
    flGoods: function (e) {
      var dataset = e.currentTarget.dataset,
          oneid = dataset.oneid,
          secondid = dataset.secondid,
          title = dataset.title;
      wx.navigateTo({
        url: '/pages/allgoodsPage/allgoodsPage?oneid=' + oneid + '&secondid=' + secondid + '&title=' + title
      })
    },
    // 切换一级分类
    toggleGoodfl: function (e) {
      var that = this,
          dataset = e.currentTarget.dataset,
          id = dataset.id,
          onefl = dataset.onefl;
      that.setData({
        curGoodFl: id,
        curGooddata: onefl,
        isShowall: false,
        flintoView: 'leftfl' + id
      })
    },
    makeCall: function () {
      app.makeCall();
    },
    isTagpage:function(){
      var that = this,
          menu = app.globalData.menuTitle,
          tabbarPage = [];
      for (var i in menu) {
        tabbarPage.push(i);
      }

      var path = getCurrentPages()[getCurrentPages().length - 1].__route__,isTabPage = false;
      for (var i = 0; i < tabbarPage.length; i++) {
        if (tabbarPage[i] == path) {
          isTabPage = true;
        }
      }
      that.setData({
        isTabPage: isTabPage
      })
      var enterFrom = app.globalData.enterFrom;
      if (!isTabPage) {
        that.setData({
          showSy: true
        })
      }
    },
    backsy: function () {
      app.backHome();
    },
    onShareAppMessage: function (e) {
      wx.showShareMenu({
        withShareTicket: true
      })
      var that = this,
          shareInfo = app.globalData.shareInfo,
          title = shareInfo.shareTitle ? shareInfo.shareTitle : '商品分类',
          cover = shareInfo.shareCover ? shareInfo.shareCover : '';
      app.getPoint(that);
      return {
        title: title,
        imageUrl: cover,
        path: '/pages/allFlGoods/allFlGoods'
      }
    },
    
  }
})
