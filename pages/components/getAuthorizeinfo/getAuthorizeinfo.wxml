<view class="get-authorize-info" wx:if="{{settingInfo}}">
  <block wx:if="{{settingInfo.loginImg}}">
    <image class="bg" src="{{settingInfo.loginImg}}" mode="aspectFill"></image>
  </block>
  <block wx:else>
    <image class="bg" src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/getauthorizeimg/new_bg.png" mode="aspectFill"></image>
    <view class="applet-name-logo">
      <text class="name">{{settingInfo.appletName}}</text>
      <image class="logo" src="{{settingInfo.appletAvatar}}"></image>
    </view>
  </block>
  <button class="get-userinfo-btn" catchtap='authorizeUserInfo'></button>
</view>
 
<view class="error-tip-show fade_in" wx:if="{{errorTip.isShow}}">
  {{errorTip.text}}
</view>