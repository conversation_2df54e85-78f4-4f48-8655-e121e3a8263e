const app = getApp();
import  {authorizeUserInfo} from "../../../api/reuseRequest"
Component({
  properties: {
   
  },
  data: {
    
  },
  attached: function () {
    var that = this;
    that.requestSetting();
  },
  methods: {
    requestSetting: function () {
      var that = this;
      //发起请求，获取列表列表
      
      wx.$get({
        map: 'applet_index_cfg'
      },{
        stopPull:true
      }).then(res=>{
        var dataInfo = res.data;
            that.setData({
              settingInfo: dataInfo
            })
      }).catch(err=>{

      })
    },
    hideGetuser: function () {
      var that = this;
      that.setData({
        isShowgetuser: false
      })
    },
    initPage:function(){
      this.triggerEvent('initPage');
    },
    // 获取用户信息
    authorizeUserInfo
  }
})