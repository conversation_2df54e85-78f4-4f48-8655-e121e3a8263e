// pages/components/onekeyShare/onekeyShare.js

const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isRefresh:{
      type:Boolean,
      value:false,
      observer:'onPullDownRefresh'
    },
    isBottom:{
      type:Boolean,
      value:false,
      observer:'onReachBottom'
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    page: 0,
    noMoretip: false,
    showLoading: true
  },
  attached: function () {
    var that = this;

    that.requestMaterialist();
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 获取直播商品选择列表
    requestMaterialist: function () {
      var that = this,page = that.data.page;
      var data = {
        map: 'applet_moments_material_list',
        page: page
      };

      wx.$get(data,{
        pageList:true
      }).then(res => {
        console.log("获取素材列表",res);
        var allArr = [];
        var initArr = that.data.materialist ? that.data.materialist : [];
        var curArr = res.data;
        var lastPageLength = curArr.length;
        if (page > 0) {
          allArr = initArr.concat(curArr);
        } else {
          allArr = res.data;
        }
        that.setData({
          materialist: allArr,
        })
        if (lastPageLength < 10) {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      }).catch(err => {
        if (page <= 0) {
          that.setData({
            materialist: [],
            noMoretip: false,
            showLoading: false
          })
        } else {
          that.setData({
            noMoretip: true,
            showLoading: false
          });
        }
      })
    },
    onPullDownRefresh: function () {
      var that = this;
      that.setData({
        page: 0,
        noMoretip: false,
        showLoading: true
      });
      that.requestMaterialist();
      
    },
    onReachBottom: function () {
      var that = this;
       
      var isMore = that.data.noMoretip;
      var page = that.data.page;
      page++;
      that.setData({
        page: page
      });
      if (!isMore) {
      
        that.requestMaterialist();
      }
    },
    peiviewImg: function (e) {
      var curimg = e.currentTarget.dataset.curimg,
          imgs = e.currentTarget.dataset.imgs;
      wx.previewImage({
        current: curimg, // 当前显示图片的http链接
        urls: imgs // 需要预览的图片http链接列表
      })
    },
    // 保存下载
    saveFile:function(e){
      var that = this;
      if(that.data.hasAuth){
        that.tosaveFile(e);
      }else{
        that.getSetting(e).then(res=>{
          that.tosaveFile(e);
        },err=>{
          console.log("失败",err)
        })
      }
    },
    tosaveFile:function(e){
      var that = this;
      var dataset = e.currentTarget.dataset,
          type = dataset.type,
          info = dataset.info;
      console.log('保存信息',dataset);
      if(type=='all'){
        that.setData({
          isSaveAll:true
        })
        wx.setClipboardData({
          data: info.content,
          success (res) {
            console.log("内容复制成功",res)
          }
        })
      }
      var data = {};
      if(info.type==1){
        data.path = info.imgs;
      }
      if(info.type==2){
        data.path = [info.videoUrl];
      }
      that.setData({
        downFilearr:[]
      })
      that.downFile(data,info.type,info.id);
    },
    // 获取当前相册权限
    getSetting:function(e){
      var that = this;
      return new Promise(function (resolve, reject) {
        wx.showLoading({
          title: '加载中',
          mask: true
        })
        wx.getSetting({
          success(res) {
            if (res.authSetting['scope.writePhotosAlbum']) {
              resolve(res);
              that.setData({
                hasAuth:true
              })
              wx.hideLoading()
            }else{
              wx.authorize({
                scope: 'scope.writePhotosAlbum',
                success (res) {
                  resolve(res);
                  that.setData({
                    hasAuth:true
                  })
                },
                fail (res) {
                  console.log("授权接口调起失败",res);
                  wx.showModal({
                    title: '提示',
                    content: '需要获取保存到相册权限,请开启保存到相册权限哦~',
                    success(res) {
                      if (res.confirm) {
               
                        wx.openSetting({
                          success(res1) {
                        
                            if(res1.authSetting['scope.writePhotosAlbum']){
                              that.setData({
                                hasAuth:true
                              })
                              that.tosaveFile(e);
                            }
                          }
                        })
                      }
                    }
                  })
                  wx.hideLoading()
                  reject(res);
                },
              })
              
            }
          },
          fail(res){
            reject(res)
            wx.hideLoading()
          }
        })
      })
    },
    setting:function(e){
      var that = this;
      if(e.detail.authSetting['scope.writePhotosAlbum']){
        that.setData({
          isOpenSetting:false,
          hasAuth:true
        })
        that.tosaveFile(e);
      }
    },
    // 下载要保存的图片或者视频到本地
    downFile:function(data,type,id){
      var that = this,
          i = data.i ? data.i : 0,
          success = data.success ? data.success : 0,
          fail = data.fail ? data.fail : 0;
      wx.showLoading({
        title: '正在下载',
        mask: true
      })
      wx.downloadFile({
        url: data.path[i],
        success: (res) => {
          success++;
          if (res.statusCode === 200) {
            var tempFilePath = res.tempFilePath,
                downFilearr = that.data.downFilearr?that.data.downFilearr:[];
            downFilearr.push(tempFilePath);
            that.setData({
              downFilearr:downFilearr
            })
          }
        },
        fail: (res) => {
          fail++;
        },
        complete: (res)=>{
          i++;
          if (i == data.path.length) {   //当图片传完时，停止调用        
            // console.log('成功：' + success + " 失败：" + fail);
            console.log(that.data.downFilearr)
            that.saveImage({path:that.data.downFilearr},type,id)
            wx.hideLoading()
          } else {//若图片还没有传完，则继续调用函数
            data.i = i;
            data.success = success;
            data.fail = fail;
            that.downFile(data,type,id);
          }
        }
      })
    },
    // 保存下载完的视频或者图片
    saveImage:function(data,type,id){
      var that = this,
          i = data.i ? data.i : 0,
          success = data.success ? data.success : 0,
          fail = data.fail ? data.fail : 0;
      var saveFunc = 'saveImageToPhotosAlbum';
      if(type==2){
        saveFunc = 'saveVideoToPhotosAlbum';
      }
      wx.showLoading({
        title: '正在保存',
        mask: true
      })
      wx[saveFunc]({
        filePath:  data.path[i],
        success(res) {
         success++;
        },
        fail: (res) => {
          fail++;
        },
        complete: (res)=>{
          i++;
          if (i == data.path.length) {        
            console.log('保存成功：' + success + " 失败：" + fail);
            
            if(that.data.isSaveAll){
              wx.hideLoading()
              that.setData({
                isShowSharetip:true,
                isSaveAll:false
              })
            }else{
              wx.showToast({
                icon:'success',
                title: '保存成功',
                mask: true,
                duration:2000
              })
              setTimeout(function(){
                wx.hideLoading()
              },2000)
            }
            that.getDownnum(id);
          } else {//若图片还没有保存完，则继续调用函数
            data.i = i;
            data.success = success;
            data.fail = fail;
            that.saveImage(data,type,id);
          }
        }
      })
    },
    // 统计下载次数
    getDownnum:function(id){
      let materialist = this.data.materialist;
      var that = this;
      wx.$get({
          map:'applet_moments_material_download',
          id,   
      },{
        pageList:true
      }).then(res => {
        console.log("下载次数统计成功",res)
        for(let i=0;i<materialist.length;i++){
          if(materialist[i].id==id){
            this.setData({
              [`materialist[${i}].download`]:Number(materialist[i].download)+1
            })
          }
        }
      }).catch(err=>{

      })
    },
    // 播放视频
    playVideo:function(e){
      var index = e.currentTarget.dataset.index;
      this.setData({
        [`materialist[${index}].isplay`]:true
      })
    },
    // 跳转发圈详情
    toshareDetail:function(e){
      var id = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: '/subpages0/shareDetail/shareDetail?id='+id,
      })
    },
    // 阻止冒泡
    stop:function(){

    },
    hideShareTip:function(){
      this.setData({
        isShowSharetip:false
      })
    },
    copyText:function(e){
      var text = e.currentTarget.dataset.text;
      var id = e.currentTarget.dataset.id;
      wx.setClipboardData({
        data: text,
        success:res=>{
          console.log("内容复制成功",res)
          this.getDownnum(id);
        }
      })
    }
  },
})
