<!--pages/components/onekeyShare/onekeyShare.wxml-->
<view class="list-wrap">
  <block wx:key="id" wx:for="{{materialist}}">
    <view class="list-item" data-id="{{item.id}}" bindtap="toshareDetail">
      <view class="user-info flex-wrap">
        <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="flex-con">
          <view class="user-name">{{item.name}}</view>
          <view class="time">{{item.time}}</view>
        </view>
        <view class="cus-btn">一键分享
          <button class="share-btn" open-type="share" data-cover="{{item.imgs[0]||item.videoCover}}" data-title="{{item.content}}" data-id="{{item.id}}" catchtap="stop"></button>
        </view>
      </view>
      <view class="item-con">
        <text decode="{{true}}" class="text-intro">{{item.content}}</text>
        <view class="img-list {{item.imgs.length==1?'one':''}}" wx:if="{{item.type==1}}">
          <block wx:key="index" wx:for="{{item.imgs}}" wx:for-item="img">
            <image class="img" wx:if="{{item.imgs.length==1}}" src="{{img}}" mode="widthFix" data-curimg="{{img}}" data-imgs="{{item.imgs}}" catchtap="peiviewImg"></image>
            <image class="img" wx:else src="{{img}}" mode="aspectFill" data-curimg="{{img}}" data-imgs="{{item.imgs}}" catchtap="peiviewImg"></image>
            <image class="img"  wx:if="{{item.imgs.length==4&&index==1}}"></image>
          </block>
        </view>
        <view class="video-item" wx:if="{{item.type==2}}">
          <block wx:if="{{!item.isplay}}">
            <image class="cover" src="{{item.videoCover}}" data-index="{{index}}" catchtap="playVideo"></image>
            <image class="play-tip" src="/images/onekeyshare/icon_zanting.png" data-index="{{index}}" catchtap="playVideo"></image>
          </block>
          <video wx:else autoplay="{{true}}" object-fit="contain" class="video" src="{{item.videoUrl}}"></video>
        </view>
      </view>
      <view class="opera-area flex-wrap">
        <view class="share-num flex-con"><block wx:if="{{item.download>0}}">{{item.download}}人已保存发圈</block></view>
        <block wx:if="{{item.type==2}}">
          <view class="cus-btn img-down" data-id="{{item.id}}" data-text="{{item.videoUrl}}" catchtap="copyText">复制视频连接</view>
          <view class="cus-btn save-all" data-id="{{item.id}}" data-text="{{item.content}}" catchtap="copyText">复制文案</view>
        </block>
        <block wx:else>
          <view class="cus-btn img-down" data-type="picvideo" data-info="{{item}}" catchtap="saveFile">图片下载</view>
          <view class="cus-btn save-all" data-type="all" data-info="{{item}}" catchtap="saveFile">一键保存</view>
        </block>
      </view>
    </view>
  </block>
</view>
<view class="no-data" wx:if="{{!showLoading&&materialist.length<=0}}">
  <image src="/images/empty_img.png"></image>
  <text>暂无相关内容哦~</text>
</view>
<!--上拉加载提示-->
<view class="loading-tip" wx:if="{{showLoading}}">
  <view class="icon_load">
    <view id="floatingBarsG">
      <view class="blockG" id="rotateG_01"></view>
      <view class="blockG" id="rotateG_02"></view>
      <view class="blockG" id="rotateG_03"></view>
      <view class="blockG" id="rotateG_04"></view>
      <view class="blockG" id="rotateG_05"></view>
      <view class="blockG" id="rotateG_06"></view>
      <view class="blockG" id="rotateG_07"></view>
      <view class="blockG" id="rotateG_08"></view>
    </view>
  </view>
  <text>努力加载中...</text>
</view>
<view class="nomore-tip" wx:if="{{noMoretip&&materialist.length>0}}">没有更多数据了</view>
<!-- 复制成功弹窗提醒 -->
<view class="sharetip-modal" wx:if="{{isShowSharetip}}">
  <view class="sharetip-modal-mask"></view>
  <view class="sharetip-modal-con">
    <view class="title-name">朋友圈分享攻略</view>
    <view class="tip-step">
      <image class="icon" src="/images/onekeyshare/icon_xuanze_d.png"></image>
      <text class="txt">分享文案已自动复制</text>
    </view>
    <view class="tip-step">
      <image class="icon" src="/images/onekeyshare/icon_xuanze_d.png"></image>
      <text class="txt">图片/视频已保存至手机相册</text>
    </view>
    <view class="tip-step">
      <image class="icon" src="/images/onekeyshare/icon_xuanze.png"></image>
      <text class="txt">朋友圈选择相册图片/视频，并粘贴文字</text>
    </view>
    <view class="cus-btn close-btn" bindtap="hideShareTip">我知道了</view>
  </view>
</view>