<!-- 分享弹出层 -->
<view class="share-modal-mask fade_in" wx:if="{{isShowshare}}" catchtap="hideSharemodal"></view>
<view class="share-modal fadeInUp" wx:if="{{isShowshare}}">
  <view class="share-con">
    <view class="share-style">
      <view class="style-item" bindtap="{{!iseditTitle?'':'showSharetitle'}}">
        <button open-type="share" wx:if="{{!iseditTitle}}"></button>
        <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/icon_share1.png" mode="aspectFit"></image>
        <text>发送给朋友</text>
      </view>
      <view class="style-item" wx:if="{{isGoodDetail && appid != 'wx7caac15536939070'}}" bindtap="{{slient=='1'?'':'copyLink'}}">
      <!-- <view class="style-item" wx:if="{{isGoodDetail}}" bindtap="{{slient=='1'?'':'copyLink'}}"> -->
        <button class="userinfo-btn" open-type='getUserInfo' wx:if="{{slient=='1'}}" data-callback="previewPoster" catchtap='authorizeUserInfo'></button>
        <image style="width: 100rpx;" src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/copy.png" mode="aspectFit"></image>
        <text>复制链接</text>
      </view>
      <view class="style-item" wx:if="{{posterType!='canvas'}}" bindtap="{{slient=='1'?'':'previewPoster'}}">
        <button class="userinfo-btn" open-type='getUserInfo' wx:if="{{slient=='1'}}" data-callback="previewPoster" catchtap='authorizeUserInfo'></button>
        <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/icon_share2.png" mode="aspectFit"></image>
        <text>生成海报分享</text>
      </view>
      <view class="style-item" wx:else bindtap="{{slient=='1'?'':'showPoster'}}">
        <button class="userinfo-btn" open-type='getUserInfo' wx:if="{{slient=='1'}}" data-callback="showPoster" catchtap='authorizeUserInfo'></button>
        <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/icon_share2.png" mode="aspectFit"></image>
        <text>生成海报分享</text>
      </view>
    </view>
    <view class="cancel-btn border-t" catchtap="hideSharemodal">取消分享</view>
  </view>
</view>

<!-- 直播海报显示 -->
<view class="share-modal-mask" wx:if="{{posterType=='live'&&showPoster}}" bindtap="closeLivePoster"></view>
<view class="live-poster-wrap" wx:if="{{posterType=='live'&&showPoster}}">
  <scroll-view class="poster-img-wrap" scroll-y>
    <image mode="widthFix" src="{{postImg}}" class="poster-img"></image>
  </scroll-view>
  <view class="flex-sp">
    <view class="save-btn" bindtap="savePoster">保存图片</view>
  </view>
</view>

<!-- canvas海报显示 -->
<block wx:if="{{posterType!='distri'}}">
  <view class="poster-modal-mask" hidden="{{!isShowposter}}" catchtap="hidePoster"></view>
  <view class="poster-modal fadeInUp" hidden="{{!isShowposter}}">
    <view class="close-btn" catchtap="hidePoster">
      <image src="/images/icon_close.png" mode="aspectFit"></image>
    </view>
    <view class="poster-con">
      <view class="empty_tip" wx:if="{{isDrawfinish==false}}">
        <image src="/images/empty_img.png"></image>
        <text>{{tips?tips:'暂无相关海报'}}~</text>
      </view>
      <view class="tips" wx:elif="{{!isDrawfinish}}">海报绘制中...</view>
      <canvas wx:elif="{{isDrawfinish}}" disable-scroll="{{true}}" canvas-id="myCanvas" class="pic_con" style="width:80%;height:{{canvasH}}px;background-color:#fff;margin:0 auto;"></canvas>
      <view class="opera-box">
        <view class="btn-box bg-orange" bindtap="saveImage">
          <button type="primary"></button>
          <text>保存图片</text>
        </view>
      </view>
    </view>
  </view>
</block>

<!-- canvas分销海报 -->
<view class="main-wrap" wx:if="{{posterType=='distri'}}">
  <view class="tips-txt">
    <text>成功邀请1位好友下单，最高赚{{shareInfo.profit}}元</text>
  </view>
  <view class="poster-drawing" wx:if="{{!isDrawfinish}}">海报绘制中...</view>
  <canvas wx:elif="{{isDrawfinish}}" canvas-id="myCanvas" class="pic_con" style="width:{{canvasW}}px;height:{{canvasH}}px;background-color:#fff;margin:0 auto;border-radius:10rpx;"></canvas>
  <view class="opera-box">
    <view class="btn-box bg-orange">
      <button type="primary" open-type="share"></button>
      <text>分享好友</text>
    </view>
    <view class="btn-box bg-green" bindtap="saveImage">
      <text>保存图片</text>
    </view>
  </view>
</view>

<!-- 分享标题配置 -->
<view class="sharetitle-modal-mask" wx:if="{{shareTitleShow}}" bindtap="hideSharetitle"></view>
<view class="sharetitle-modal-con {{shareTitleShow?'show':''}}">
  <view class="label-title">分享标题 <view class="cancel-btn" bindtap="hideSharetitle">取消</view>
  </view>
  <view class="title-input">
    <textarea cursor-spacing="20" confirm-type="done" fixed="true" placeholder="请输入分享标题" value="{{shareTitle}}" bindinput="shareTitleChange"></textarea>
  </view>
  <view class="share-btn-area">
    <view class="confirm-share" style="color:#fff;background-color:{{navColor}};" bindtap="hideSharetitle">立即分享<button open-type='share'></button></view>
  </view>
</view>