/* pages/components/pageShare/pageShare.wxss */
.flex-sp{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 分享弹出层 */
.share-modal-mask { position: fixed; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1004; }
.share-modal { position: fixed; left: 0; width: 100%; bottom: 0; background-color: #f2f2f2; -webkit-animation-duration: 0.5s; animation-duration: 0.5s; -webkit-animation-fill-mode: both; animation-fill-mode: both; z-index: 1005; }
.share-modal .share-style { display: table; width: 100%; padding: 20rpx; box-sizing: border-box; }
.share-modal .style-item { display: table-cell; width: 1000rpx; padding: 10rpx 0; position: relative; }
.share-modal .style-item button { position: absolute; left: 0; top: 0; width: 100%; height: 100%; margin: 0; opacity: 0; z-index: 1; }
.share-modal .style-item image { display: block; width: 115rpx; height: 115rpx; border-radius: 50%; margin: 0 auto; box-sizing: border-box; margin-bottom: 10rpx; }
.share-modal .style-item text { display: block; text-align: center; color: #666; font-size: 26rpx; }
.share-modal .cancel-btn { width: 100%; height: 96rpx; line-height: 96rpx; text-align: center; font-size: 28rpx; color: #333; background-color: #fff; }
.share-modal .cancel-btn:after { background-color: #fff; }
/* 海报弹出层 */
.poster-modal-mask{position: fixed; left: 0; top:0;width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1007;}
.poster-modal { position: fixed; left: 5%; width: 90%; top: 50%; background-color: #fff;border-radius: 6rpx;overflow: hidden; z-index: 1009;transform: translateY(-50%); padding: 40rpx 0;box-sizing: border-box;}
.poster-modal .close-btn{position: absolute;top:0;right:0;height: 64rpx;width: 64rpx;}
.poster-modal .close-btn image{display: block;width: 100%;height: 100%;box-sizing: border-box;padding: 12rpx;}
.poster-modal .tips{padding: 80rpx;text-align: center;font-size: 36rpx;color: #999;}
.poster-modal canvas{box-shadow: 0 0 20rpx #ddd;border-radius: 8rpx;}
.poster-modal .opera-box { font-size: 0; text-align: center;margin-top: 30rpx; }
.poster-modal .opera-box .btn-box { width: 85%; display: inline-block; height: 90rpx; line-height: 90rpx; border-radius: 6rpx; text-align: center; color: #fff; position: relative; }
.poster-modal .opera-box .btn-box text { height: 90rpx; line-height: 90rpx; border-radius: 8rpx; display: block; text-align: center; color: #fff; font-size: 32rpx; }
.poster-modal .opera-box .btn-box.bg-orange { background-color: #ff8140; }
.poster-modal .opera-box button { margin: 0 auto; opacity: 0; position: absolute; width: 100%; top: 0; height: 100%; z-index: 1; }
.no-data, .empty_tip { padding: 280rpx 0; }
.no-data image, .empty_tip image { width: 160rpx; height: 160rpx; display: block; margin: 0 auto; }
.no-data text, .empty_tip text { display: block; text-align: center; color: #999; font-size: 28rpx; margin-top: 10rpx; }
/* 直播海报 */
.poster-img-wrap{
  max-height: 85vh;
}
.live-poster-wrap{
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  z-index: 1006;
}
.live-poster-wrap .poster-img{
  margin: 0 auto;
  display: block;
  width: 580rpx;
}
.live-poster-wrap .shareInfo{
  height: 40rpx;
width: 40rpx;


}
.live-poster-wrap .save-btn{
  margin: 30rpx auto;
  width: 280rpx;
  height: 72rpx;
  line-height: 72rpx;
  background: #fff;
  color: #ff2200;
  border-radius: 10rpx;
  font-size: 26rpx;
 
  text-align: center;
  box-shadow: 0 0 5rpx #e5e5e5;

}
.close-btn{
  position: absolute;
  top: -80rpx;
  right: 90rpx;
  height: 40rpx;
  width: 40rpx;
}
/* 分销海报 */
.main-wrap{height: 100%;position: relative;z-index: 2;}
.poster-drawing{width: 640rpx;margin:0 auto;border-radius: 10rpx;background-color: #fff;height: 900rpx;line-height: 900rpx;color: #ff7555;text-align: center;}
.tips-txt {padding: 20rpx 0;}
.tips-txt text{color: #fff;text-align: center;line-height: 1.5;display: block;}
.opera-box { padding: 40rpx 2% 20rpx; font-size: 0; text-align: center; }
.opera-box .btn-box { width: 34%; margin: 0 4.5%; display: inline-block; height: 80rpx; line-height: 80rpx; border-radius: 45rpx; text-align: center; color: #fff; position: relative;box-shadow: 2rpx 2rpx 15rpx #ff704b; }
.opera-box .btn-box text { height: 80rpx; line-height: 80rpx; border-radius: 45rpx; display: block; text-align: center; color: #ff7555; font-size: 30rpx; }
.opera-box .btn-box.bg-orange { background-color: #fff; }
.opera-box .btn-box.bg-green { background-color: #fff; }
.opera-box button { margin: 0 auto; opacity: 0; position: absolute; width: 100%; top: 0; height: 100%; z-index: 1; }

/* 分享标题配置弹出层 */
.sharetitle-modal-mask { position: fixed; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.02); z-index: 1000003; }
.sharetitle-modal-con { position: fixed; left: 2%; bottom: 15rpx; width: 96%; background-color: #fff; border: 1px solid #eee; border-radius: 8rpx; overflow: hidden; z-index: 1000004; box-shadow: 0 0 20rpx #ccc; transition: all 0.3s; transform: translateY(200%); }
.sharetitle-modal-con.show { transform: translateY(0); }
.sharetitle-modal-con .label-title { font-size: 32rpx; font-weight: bold; padding: 20rpx 30rpx; position: relative; }
.sharetitle-modal-con .label-title .cancel-btn { color: #999; position: absolute; top: 0; right: 0; padding: 20rpx 30rpx; z-index: 1; font-size: 28rpx; font-weight: normal; }
.sharetitle-modal-con .title-input { padding: 0 30rpx; box-sizing: border-box; }
.sharetitle-modal-con .title-input textarea { font-size: 30rpx; padding: 15rpx; height: 120rpx; border: 1px solid #eee; border-radius: 8rpx; box-sizing: border-box; width: 100%; }
.sharetitle-modal-con .share-btn-area { padding: 25rpx 30rpx; text-align: center; }
.sharetitle-modal-con .share-btn-area view { height: 70rpx; line-height: 72rpx; border-radius: 38rpx; text-align: center; color: #999; padding: 0 30rpx; display: inline-block; vertical-align: middle; font-size: 30rpx; font-weight: bold; letter-spacing: 1px; }
.sharetitle-modal-con .share-btn-area .confirm-share { position: relative;background-color: #0A98FA; }
.sharetitle-modal-con .share-btn-area .confirm-share button { position: absolute; left: 0; top: 0; width: 100%; height: 100%; margin: 0; z-index: 1; opacity: 0; }