// pages/components/pageShare/pageShare.js
import {
  authorizeUserInfo
} from "../../../api/reuseRequest"
// import { promises } from "dns";
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    requestData: { //获取海报接口
      type: Object,
      value: {}
    },
    posterType: {
      type: String, //海报类型
      value: ''
    },
    useLivePlugin: { //是否使用直播插件
      type: Boolean,
      value: false
    },
    imgField: { //预览海报时，后台返回的海报字段
      type: String,
      value: 'shareImg'
    },
    iseditTitle: { //是否编辑分享标题
      type: Boolean,
      value: false
    },
    shareTitle: { //默认分享标题
      type: String,
      value: ''
    },
    link: {
      type: String,
      value: ''
    },
    isGoodDetail: {
      type: String,
      value: ''
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isShowshare: false, //显示底部分享按钮
    showPoster: false,
    postImg: "",
    isShowposter: false, //canvas 海报展示
    tips: '',
    shareInfo: {}
  },
  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      var res = app.globalData.systemInfo ? app.globalData.systemInfo : '';
      this.setData({
        appid:app.globalData.appid,
        winW: res && res.windowWidth ? res.windowWidth : '',
        winH: res && res.windowHeight ? res.windowHeight : '',
        slient: app.globalData.slient
      })
    },
    detached: function () {
      // 在组件实例被从页面节点树移除时执行
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    showSharemodal: function () {
      this.setData({
        isShowshare: true
      })
    },
    hideSharemodal: function () {
      this.setData({
        isShowshare: false
      })
    },
    authorizeUserInfo,
    copyLink() {
      console.log("link", this.data.link)
      wx.setClipboardData({
        data: this.data.link,
        success: (res) => {
          console.log(res)
          wx.showToast({
            title: '复制成功',
          })
        },
        fail: (res) => {
          console.log('复制失败')
          wx.showToast({
            title: '复制失败',
          })
        }
      })
    },
    //预览海报
    previewPoster() {
      let imgField = this.data.imgField;
      wx.showLoading({
        title: '正在生成海报',
      })
      wx.$get(this.data.requestData, {
        showLoading: false,
        showError: false,
        useLive: this.data.useLivePlugin,
      }).then(res => {
        wx.hideLoading();
        let postImg = res.data[imgField]
        if (this.data.posterType) {
          this.setData({
            showPoster: true,
            postImg: postImg
          })
        } else {
          wx.previewImage({
            current: postImg,
            urls: [postImg]
          })
        }

        this.hideSharemodal()
      }).catch(err => {
        wx.hideLoading();
      })
    },
    closeLivePoster() {
      this.setData({
        showPoster: false,
      })
    },
    // 直播保存海报
    savePoster() {
      let that = this;
      wx.downloadFile({
        url: this.data.postImg, //仅为示例，并非真实的资源
        success: (res) => {
          // 只要服务器有响应数据，就会把响应内容写入文件并进入 success 回调，业务需要自行判断是否下载到了想要的内容
          if (res.statusCode === 200) {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success(res1) {
                console.log('保存成功', res1)
                wx.showToast({
                  title: '保存成功',
                })
                that.setData({
                  showPoster: false,
                })
              },
              fail(res1) {
                console.log('保存失败', res1)
              }
            })
          }
        },
        fail: (res) => {
          wx.showToast({
            title: '图片下载失败',
            icon: 'none'
          })
        }
      })
    },
    // canvas海报展示
    showPoster: function () {
      this.setData({
        isShowposter: true,
      })
      let isDrawfinish = this.data.isDrawfinish;
      if (isDrawfinish) {
        return;
      }
      this.requestShareinfo(this.data.requestData);
    },
    hidePoster: function () {
      this.setData({
        isShowposter: false,
      })
    },
    showDistriPoster() {
      this.requestShareinfo(this.data.requestData);
    },
    // 宣传海报
    requestShareinfo: function (requestData) {
      let _this = this;
      wx.showLoading({
        title: '海报绘制中',
      })
      wx.$get(requestData, {
        showLoading: false,
      }).then(res => {

        let shareInfo = res.data;
        this.triggerEvent('getInfoSuccess', {
          shareInfo
        })
        shareInfo.type = requestData.type;

        _this.downImageNew(shareInfo, 'cover').then(
          () => _this.downImageNew(shareInfo, 'qrcode'),
        ).then(
          () => _this.downImageNew(shareInfo, 'shopLogo'),
        ).then(
          () => {
            shareInfo.price = "￥" + shareInfo.price + " 扫码购买"
            _this.setData({
              shareInfo: shareInfo
            })
            _this.createCanvas(requestData.type)
          },
          err => {
            console.log("rejected: ", err)
            _this.setData({
              isDrawfinish: false,
              tips: err
            })
            wx.hideLoading()
          }
        )
      }).catch(err => {

      })
    },
    downImageNew(shareInfo, imgType) {
      let errtip = "",
        _this = this
      return new Promise(function (resolve, reject) {
        if (!shareInfo[imgType]) {
          switch (imgType) {
            case 'cover':
              errtip = "封面不存在"
              break;
            case 'qrcode':
              errtip = "小程序码不存在"
              break;
            case 'shoplogo':
              errtip = "店主太忙了，还没来及上传LOGO。\n您可以偷偷告诉店主：\n小程序后台右上角-> 店铺设置 -> 上传店铺logo"
              break;
          }

          reject(errtip)
          return
        }
        wx.downloadFile({
          url: shareInfo[imgType], //仅为示例，并非真实的资源
          success(res) {
            // 只要服务器有响应数据，就会把响应内容写入文件并进入 success 回调，业务需要自行判断是否下载到了想要的内容
            if (res.statusCode === 200) {
              shareInfo[imgType] = res.tempFilePath
              _this.setData({
                shareInfo: shareInfo
              })
              resolve()
            } else {
              showErrorTip()
              reject(errtip)
            }
          },
          fail() {
            showErrorTip()
            reject(errtip)
          }
        })
      })

      function showErrorTip() {
        switch (imgType) {
          case 'cover':
            errtip = "封面下载失败"
            break;
          case 'qrcode':
            errtip = "小程序码下载失败"
            break;
          case 'shoplogo':
            errtip = "店铺logo下载失败"
            break;
        }
      }
    },
    createCanvas: function (posterType) {
      let that = this;
      let shareInfo = that.data.shareInfo;
      const ctx = wx.createCanvasContext('myCanvas', this);
      let winW = that.data.winW;
      let shareDesc = shareInfo.shareDesc;
      let shareDescArr = [];
      let singleTxtnum = Math.floor((winW * 0.9 * 0.8 - 30) / 15);
      if (posterType == 'distri') {
        singleTxtnum = Math.floor((winW * 0.82 - 30) / 15);
      }
      shareDesc = shareDesc.replace(/\s*/g, "");
      let txtLinenum = Math.ceil(shareDesc.length / singleTxtnum) <= 3 ? Math.ceil(shareDesc.length / singleTxtnum) : 3;
      for (let i = 0; i < txtLinenum; i++) {
        let length = shareDescArr.length;
        let txtObj = shareDesc.substring(length * singleTxtnum, length * singleTxtnum + singleTxtnum);
        shareDescArr.push(txtObj);
      }
      shareInfo.shareDesc = shareDescArr;
      let canvasW = winW * 0.9 * 0.8;
      let canvasH = winW * 0.9 * 0.8 + shareDescArr.length * 15 * 1.3 + canvasW * 0.34 + 54;
      if (posterType == 'distri') {
        canvasW = winW * 0.82;
        canvasH = winW * 0.82 + shareDescArr.length * 15 * 1.3 + canvasW * 0.34 + 54;
      }
      if (shareInfo.type == 'news') {
        canvasH = (canvasW - 30) * 14 / 25 + 30 + shareDescArr.length * 15 * 1.3 + canvasW * 0.34 + 54;
      }
      that.setData({
        canvasW: canvasW,
        canvasH: canvasH
      })

      // console.log("画布宽" + canvasW)
      if (posterType == 'distri') {
        let x = 0,
          y = 0,
          radius = 5,
          width = canvasW,
          height = canvasH;
        ctx.rect(0, 0, canvasW, canvasH)
        ctx.setFillStyle('#ffffff')
        ctx.fill()
        ctx.save()
        ctx.beginPath()
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
        ctx.clip()
      } else {
        ctx.rect(0, 0, canvasW, canvasH)
        ctx.setFillStyle('#ffffff')
        ctx.fill()
      }
      let imgW = canvasW - 30;
      let imgH = canvasW - 30;
      if (shareInfo.type == 'news') {
        imgH = imgW * 14 / 25;
      }
      // console.log("图片宽" + imgW)
      ctx.drawImage(shareInfo.cover, 15, 15, imgW, imgH)
      ctx.setFontSize(15)
      for (let i = 0; i < shareDescArr.length; i++) {
        let textY = (imgH + 30) + i * 15 * 1.2 + 10;
        ctx.setFillStyle('#333333');
        ctx.fillText(shareDescArr[i], 15, textY);
      }
      let priceY = (imgH + 30) + shareDescArr.length * 15 * 1.3 + 14;
      if (shareInfo.type == 'news') {
        priceY = (imgH + 30) + shareDescArr.length * 15 * 1.3;
      }
      ctx.setFillStyle('#ff2020');
      ctx.setFontSize(15);
      if (shareInfo.type != 'news') {
        ctx.fillText(shareInfo.price, 15, priceY);
      }
      let lineY = priceY + 16;
      ctx.moveTo(20, lineY);
      ctx.lineTo(canvasW - 30, lineY);
      ctx.setStrokeStyle('#eeeeee');
      ctx.stroke();
      ctx.drawImage(shareInfo.qrcode, 15, lineY + 10, canvasW * 0.34, canvasW * 0.36);
      ctx.setFontSize(12);
      ctx.setFillStyle('#999999');
      ctx.fillText('长按识别小程序码访问', canvasW * 0.36 + 23, lineY + canvasW * 0.36 * 0.34 + 10);
      ctx.drawImage(shareInfo.shopLogo, canvasW * 0.36 + 25, lineY + canvasW * 0.36 * 0.32 + 22, canvasW * 0.10, canvasW * 0.10);
      let shopName = shareInfo.shopName;
      let shopNameArr = [];
      txtLinenum = Math.ceil(shopName.length / 9) <= 2 ? Math.ceil(shareDesc.length / 9) : 2;
      for (let i = 0; i < txtLinenum; i++) {
        let length = shopNameArr.length;
        let txtObj = shopName.substring(length * 9, length * 9 + 9);
        shopNameArr.push(txtObj);
      }
      if (shopName.length <= 9) {
        ctx.fillText(shareInfo.shopName, canvasW * 0.36 + 30 + canvasW * 0.10, lineY + canvasW * 0.36 * 0.32 + 22 + canvasW * 0.06);
      } else {
        for (let i = 0; i < shopNameArr.length; i++) {
          let textY = (lineY + canvasW * 0.36 * 0.32 + 22) + i * 12 * 1.2 + 10;
          ctx.fillText(shopNameArr[i], canvasW * 0.36 + 30 + canvasW * 0.10, textY);
        }
      }
      ctx.draw();
      wx.hideLoading();
      that.setData({
        isDrawfinish: true
      })
    },
    saveImage: function () {
      let canvasW = this.data.canvasW;
      let canvasH = this.data.canvasH;
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: canvasW,
        height: canvasH,
        canvasId: 'myCanvas',
        fileType: 'jpg',
        success: function (res) {
          let tempFilePathShow = res.tempFilePath;
          wx.showLoading({
            title: '正在保存',
            mask: true,
            time: 100000
          }).then(
            wx.saveImageToPhotosAlbum({
              filePath: tempFilePathShow,
              success(res) {
                wx.showToast({
                  title: '图片保存成功',
                  icon: 'none'
                })

              },
              fail(f) {
                wx.showToast({
                  title: '图片保存失败',
                  icon: 'none'
                })
              },
              complete() {
                wx.hideLoading();
              }
            })
          ).catch({})
        },
        fail(err) {
          console.log(err)
        }
      }, this)
    },
    showSharetitle: function () {
      this.setData({
        shareTitleShow: true,
        isShowshare: false
      });
    },
    hideSharetitle: function () {
      this.setData({
        shareTitleShow: false
      })
    },
    shareTitleChange: function (e) {
      this.setData({
        shareTitle: e.detail.value
      })
      this.triggerEvent('changeshareTitle', this.data.shareTitle)
    },
  }
})