// pages/components/joinGroup/joinGroup.js
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    joinData: {
      type:Object,
      observer: 'joinData'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    joinModelShow:false
  },
  ready: function () {
    var that = this;
  },
  /**
   * 组件的方法列表
   */
  methods: {
    showJoinModel: function () {
      var that = this;
      that.setData({
        joinModelShow: true
      })
    },
    hideJoinModel:function(){
      var that = this;
      that.setData({
        joinModelShow:false
      })
    },
    saveImage: function (e) {
      var that = this;
      var qrcode = that.data.joinData.qrcode;
      wx.showLoading({
        title: '正在保存',
        mask: true,
        time: 100000
      });
      wx.downloadFile({
        url: qrcode,
        success: function (res) {
          if (res.statusCode == 200) {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success(res) {
                wx.$showToast( "图片保存成功");
              },
              fail(f) {
                wx.$showToast( "图片保存失败");
              },
              complete() {
                wx.hideLoading();
              }
            })
          } else {
            console.log("Logo不存在");
          }
        },
        fail: function () {
          console.log("下载失败");
        }
      })
    },
  }
})
