/* pages/components/joinGroup/joinGroup.wxss */
.flex-wrap {display: flex; align-items: center; }
.flex-wrap1 {display: flex; }
.flex-con {flex: 1; }
.flex-vertical { flex-flow: column; }
/*图片渐入效果*/
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}
.fade_in { animation: fadeIn 1s both; }
.error-tip { padding: 20rpx 25rpx; background-color: rgba(0, 0, 0, .7); color: #fff; position: fixed; top: 50%; left: 50%; border-radius: 4px; transform: translate(-50%, -50%); z-index: 1000009; max-width: 80%; display: inline-block; text-align: center; }
/* 进群 */
.hotBag-group{background-color:#fff;padding:20rpx;margin-top:16rpx;}
.hotBag-group .cover{display: block;width:100rpx;height:100rpx;border-radius:50%;}
.hotBag-group .hotBag-infor{padding:0 20rpx;max-width:430rpx;}
.hotBag-group .title{color:#111;font-size:30rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.hotBag-group .desc-wrap{color:#999;font-size:24rpx;display: -webkit-box !important;overflow: hidden;text-overflow:ellipsis;word-break:break-all;-webkit-box-orient:vertical;-webkit-line-clamp:2;}
.hotBag-group .btn-wrap{padding:10rpx 40rpx;border-radius:30rpx;color:#fff;background-color:#FF8D3A;font-size:26rpx;position:relative;}

/* 二维码弹窗 */
.code-mask{background-color:rgba(0,0,0,0.5);position:fixed;top:0;left:0;right:0;bottom:0;z-index:100;}
.code-model{width:600rpx;padding:0 0 50rpx;background-color:#fff;border-radius:20rpx;position:fixed;top:50%;left:50%;margin-left:-300rpx;z-index:101;transform: translateY(-50%);-webkit-transform: translateY(-50%);}
.code-model .close-btn{width:40rpx;height:40rpx;position:absolute;top:20rpx;right:20rpx;z-index:102;}
.code-model .close-btn .close-img{display:block;width:100%;height:100%;}
.code-model .title-wrap{height:165.52rpx;position:relative;color:#fff;font-size:40rpx;text-align: center;letter-spacing: 2rpx;}
.code-model .title-wrap .title-bg{display:block;width:100%;height:100%;border-radius:20rpx 20rpx 0 0;}
.code-model .title-wrap .title{position:absolute;top:0;left:0;right:0;bottom:0;text-align:center;}
.code-model .code-wrap{width:260rpx;height:260rpx;margin:50rpx auto;}
.code-model .code-wrap .code{display: block;width:100%;height:100%;}
.code-model .save-btn{width:260rpx;padding:22rpx 0;text-align:center;color:#fff;font-size:30rpx;margin:0 auto;border-radius:46rpx;background-color:#FF8D3A;letter-spacing: 1rpx;}