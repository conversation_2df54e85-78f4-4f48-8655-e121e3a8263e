/* 显示更多菜单 */
.movable-area{position: fixed;left: 0;top:0;width: 100%;height: 100%;z-index: 5;}
.border-t, .border-b, .border-l, .border-r { position: relative; }
.border-t:before, .border-b:after { content: ''; position: absolute; left: 0; top: 0; background-color: #e2e2e2; right: 0; height: 1px; -webkit-transform: scaleY(0.5); transform: scaleY(0.5); -webkit-transform-origin: 0 0; transform-origin: 0 0; }
.border-l::before, .border-r::after { content: ''; position: absolute; bottom: 0; top: 0; background-color: #e2e2e2; left: 0; width: 1px; -webkit-transform: scaleX(0.5); transform: scaleX(0.5); -webkit-transform-origin: 0 0; transform-origin: 0 0; }
.border-b:after { top: auto; bottom: 0; }
.border-r::after { right: 0; left: auto; }
.addmenu { position: fixed; right: 20rpx; bottom: 230rpx; height: 72rpx; width: 72rpx; border-radius: 50%; background-color: #0099CC; padding: 0 20rpx; box-sizing: border-box; z-index: 10001; box-shadow: 0 0 10rpx rgba(255, 255, 255, .1);display:flex;align-items:center;font-size: 35rpx;text-align: center;line-height: 80rpx;color:#fff;}
.addmenu.img{padding: 0;display: block;}
.addmenu .btnImg{display: block;width: 100%;height:100%;border-radius: 50%;}
.addmenu .line-box{width: 100%;}
.addmenu .line { display: block; margin: 10rpx auto; height: 2px; background-color: #fff; -webkit-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; }
.addmenu.is-active .line:nth-child(2) { opacity: 0; }
.addmenu.is-active .line:nth-child(1) { -webkit-transform: translateY(7px) rotate(45deg); transform: translateY(7px) rotate(45deg); }
.addmenu.is-active .line:nth-child(3) { -webkit-transform: translateY(-7px) rotate(-45deg); transform: translateY(-7px) rotate(-45deg); }
.addmenu.showmenu .btnImg { transform: rotate(135deg); }
.more-menu-mask { position: fixed; left: 0; top: 0; width: 100%; height: 100%; z-index: 9999; background-color: rgba(0, 0, 0, 0.4); }
.more-menu { position: fixed; left: 0; top: -1500rpx; z-index: 10000; border-radius: 0 10rpx 10rpx 0; overflow: hidden; transition: all 0.5s; opacity: 0; background-color: #fff; box-shadow: 2rpx 0 10rpx #ddd; width: 254rpx; box-sizing: border-box; -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.more-menu.show { top: 50%; opacity: 1; }
.more-menu .scroll-view { width: 100%; text-align: right; white-space: nowrap; max-height: 600rpx; padding: 0 16rpx; height: auto; }
.more-menu .menu-item { padding: 25rpx 4rpx; position: relative; max-width: 214rpx; text-align: left; }
.more-menu .menu-item.border-b:after { background-color: #ededed; }
.more-menu .menu-item:last-child.border-b:after { height: 0; }
.more-menu .menu-item .kefu-btn,.more-menu .menu-item .button { position: absolute; left: 0; width: 100%; height: 100%; z-index: 3; opacity: 0; margin: 0; }
.more-menu .menu-item .img { height: 60rpx; width: 60rpx; display: inline-block; vertical-align: middle; margin: 0 auto; border-radius: 50%; box-sizing: border-box; margin-right: 15rpx; }
.more-menu .menu-item .icon_sy { padding: 12rpx; background-color: #ff0033; }
.more-menu .menu-item .flex-con { text-align: left; }
.more-menu .menu-item .flex-con .img { height: 26rpx; width: 26rpx; margin-left: 10rpx; vertical-align: middle; display: inline-block; position: relative; top: -1rpx; }
.more-menu .menu-item .title { font-size: 28rpx; text-align: left; color: #333; line-height: 38rpx; position: relative; top: 1rpx; display: inline-block; vertical-align: middle; max-width: 140rpx; white-space: nowrap; overflow: hidden;  }
.error-tip { padding: 20rpx 25rpx; background-color: rgba(0, 0, 0, .7); color: #fff; position: fixed; top: 50%; left: 50%; border-radius: 4px; transform: translate(-50%, -50%); z-index: 10000; max-width: 80%; display: inline-block; text-align: center; }