//获取应用实例
// 组件数据
import {getGlobalData,setNavColor} from "../../../utils/reuseFunc"
const app = getApp()
Component({
  properties: {
    
  },
  /**
   * 组件的初始数据
   */
  data: {
    'isShowmenu': false
  },

  /**
   * 组件的方法列表
   */
  attached:function(){
    var that = this;
    this.getGlobalData('themeColor');//获取主题配色
    setNavColor.call(this);
    that._moremenu_show();
    wx.getSystemInfo({
      success: function(res) {
        that.setData({
          winW: res.windowWidth,
          winH: res.windowHeight,
          x: res.windowWidth - 10 - 80 / 750 * res.windowWidth,
          y: res.windowHeight-100
        })
      },
    })
    this.getGlobalData('serviceSetting'); 
  },
  methods: {
    getGlobalData,
    _moremenu_show: function () {
      var that = this;
      var isHasdata = app.globalData.isHasdata;
      if (!isHasdata) {
        wx.$get({
          map: 'applet_applet_suspension_menu'
        }).then(res=>{
          let responseData = res.data;
          app.globalData.suspensionMenu = responseData.suspensionMenu;
          app.globalData.menuAllShow = responseData.suspensionMenuShow;
          var sessionForm = {};
          if (responseData.sessionForm){
            sessionForm= {
              "nickName": app.globalData.userInfo.nickname,
              "city": app.globalData.userInfo.city
            }
          } 
          app.globalData.btnImg = responseData.btnImg;
          app.globalData.sessionForm = JSON.stringify(sessionForm);
          app.globalData.isHasdata = true;
          app.globalData.indexUrl = responseData.indexUrl;
          app.globalData.telphone = app.globalData.telphone ? app.globalData.telphone:responseData.mobile;
          that._moremenu_setMenudata();
          that._moremenu_isIndex();
        }).catch(err=>{
          setTimeout(function () {
            that._moremenu_isIndex();
          }, 2000)
        })
      } else {
        that._moremenu_isIndex();
        that._moremenu_setMenudata();
      }
    },
    _moremenu_setMenudata: function () {
      this.setData({
        'suspensionMenu': app.globalData.suspensionMenu,
        'menuAllShow': app.globalData.menuAllShow,
        'mobile': app.globalData.telphone ? app.globalData.telphone:'',
        'sessionForm': app.globalData.sessionForm,
        'indexUrl': app.globalData.indexUrl,
        'btnImg': app.globalData.btnImg
      })
    },
    _moremenu_isIndex: function () {
      var path = "/"+getCurrentPages()[getCurrentPages().length - 1].__route__;
      var indexUrl = this.data.indexUrl ? this.data.indexUrl : '/pages/index/index';
      var isIndex = 1;
      if (path == indexUrl) {
        isIndex = 1;
      } else {
        isIndex = 0;
      }
      this.setData({
        'isIndex': isIndex
      })
    },
    // 显示更多菜单
    _moremenu_toggleMenu: function () {
      var that = this;
      var isShowmenu = that.data.isShowmenu;
      if (isShowmenu) {
        isShowmenu = false;
      } else {
        isShowmenu = true;
      }
      that.setData({
        'isShowmenu': isShowmenu
      })
    },
    // 禁止滑动页面
    _catchtouchstart: function () {
      console.log("触摸屏幕阻止冒泡");
    },
    // 菜单操作
    _moremenu_menuOpera: function (e) {
      var that = this;
      var indexUrl = that.data.indexUrl ? that.data.indexUrl:'/pages/index/index';
      var type = e.currentTarget.dataset.type;

      if (type == 'index') {
        app.backHome();
      }
      if (type == '102') {
        var mobile = e.currentTarget.dataset.mobile;
        if (mobile) {
          app.makeCall(mobile);
        } else {
          wx.$showToast( "暂未获取到电话");
        }
      } else if (type == '103') {
        wx.navigateTo({
          url: '/pages/sharepage/sharepage'
        })
      } else if (type == '3') {
        var url = e.currentTarget.dataset.url;
        var vrInfo = {
          vrurl: url
        }
        wx.setStorage({
          key: 'webviewUrl',
          data: vrInfo,
          success: function () {
            wx.navigateTo({
              url: '/pages/commonView/commonView',
            })
          }
        })
      } else if (type == '105') {
        that._requestSign();
      } else if (type != '106' && type != '101') {
        var url = e.currentTarget.dataset.url;
        console.log(url);
        wx.navigateTo({
          url: url
        })
      }
    },
    _requestSign: function () {
      var that = this;

      wx.$get({
        map: 'applet_community_sign_point'
      }).then(res=>{
        wx.showToast({
          title:res.em,
        })
      }).catch(err =>{
        console.log(err)
      })

    },
    _touchstart: function (e) {
      var that = this,
          clientX = e.touches[0].clientX,
          clientY = e.touches[0].clientY;
      that.setData({
        startclientX: clientX,
        startclientY: clientY
      })
    },
    _touchmove: function (e) {
      var that = this,
          clientX = e.touches[0].clientX,
          clientY = e.touches[0].clientY,
          that = this,
          clientX = e.touches[0].clientX,
          clientY = e.touches[0].clientY,
          moveDistanceX = that.data.startclientX - clientX,
          moveDistanceY = that.data.startclientY - clientY;
      if (moveDistanceX != 0 || moveDistanceY != 0) {
        wx.stopPullDownRefresh();
        this.moveFunc(clientX, clientY);
      }
    },
    _touchend:function(e){
      var that = this,
          clientX = e.changedTouches[0].clientX,
          clientY = e.changedTouches[0].clientY,
          moveDistanceX = that.data.startclientX - clientX,
          moveDistanceY = that.data.startclientY - clientY;
      if (moveDistanceX != 0 || moveDistanceY != 0) {
        that.moveFunc(clientX, clientY, 'end');
      }
    },
    moveFunc: function (clientX, clientY, type) {
      var that = this,
          winH = that.data.winH,
          winW = that.data.winW,
          menuW = parseInt(80 / 750 * winW),
          maxLeft = winW - menuW - menuW / 4,
          maxTop = winH - menuW;
      var x = clientX - menuW / 2;
      x = x < 0 ? 0 : x;
      x = x > maxLeft ? maxLeft : x;
      var y = clientY - menuW / 2;
      y = y < 0 ? 0 : y;
      y = y > maxTop ? maxTop : y;
      if (type == 'end') {
        var middleW = winW/2;
        // if (x < middleW) {
        //   x = menuW / 4;
        // }
        // if (x >= middleW) {
        //   x = winW - menuW - menuW / 4;
        // }
        x = winW - menuW - menuW / 4;
      }
      that.setData({
        left: x,
        top: y
      });
    },
    // 客服回复组件间传值
    getreplyData: function (e) {
      this.setData(e.detail);
    },
  },
})