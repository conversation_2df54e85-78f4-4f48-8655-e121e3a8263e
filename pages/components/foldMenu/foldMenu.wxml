<block wx:if="{{(isIndex==0&&menuAllShow==1)||(isIndex==1&&suspensionMenu.length>0)}}">
  <!-- 显示更多菜单 -->
  <view class="addmenu {{isShowmenu?'is-active':''}} {{btnImg&&!isShowmenu?'img':''}}" style="left:{{left}}px;top:{{top}}px;background-color:{{btnImg&&!isShowmenu?'rgba(0,0,0,0)':themeColor1}};" bindtouchstart="_touchstart" catchtouchmove="_touchmove" bindtouchend="_touchend" bindtap="_moremenu_toggleMenu">
    <block wx:if="{{btnImg&&!isShowmenu}}">
      <image src="{{btnImg}}" class="btnImg"></image>
    </block>
    <view class="line-box" wx:if="{{(btnImg&&isShowmenu)||!btnImg}}">
      <text class="line"></text>
      <text class="line"></text>
      <text class="line"></text>
    </view>
  </view>
  <view class="more-menu-mask fade_in" catchtouchstart="_catchtouchstart" wx:if="{{isShowmenu}}" bindtap="_moremenu_toggleMenu"></view>
  <view class="more-menu {{isShowmenu?'show':''}}">
    <scroll-view class="scroll-view" scroll-y>
      <view class="menu-item border-b" data-type="index" bindtap="_moremenu_menuOpera" wx:if="{{!isIndex}}">
        <image class="img fade_in" src="/images/icon_sy.png" mode="aspectFit"></image>
        <text>返回首页</text>
      </view>
       <block wx:key="index" wx:for="{{suspensionMenu}}" wx:for-item="menu">
        <view class="menu-item border-b" data-type="{{menu.type}}" data-url="{{menu.link}}" data-mobile="{{mobile}}" bindtap="_moremenu_menuOpera">
          <block wx:if="{{menu.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{menu.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{menu.path}}" app-id="{{menu.link}}" open-type="navigate" />
          </block>
          <image class="img fade_in" src="{{menu.imgsrc}}" mode="aspectFit"></image>
          <text class="title">{{menu.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
</block>
<!-- 客服回复提示弹窗 -->
<kefu-replytip is-showkfreply="{{isShowkfreply}}" reply-tips="{{replyTips}}" customer-mobile="{{customerMobile}}" contact-phone="{{contactPhone}}"></kefu-replytip>
 