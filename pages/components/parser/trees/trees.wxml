<!--trees 递归子组件-->
<wxs module="handler">
var inline = {
  abbr: 1,
  b: 1,
  big: 1,
  code: 1,
  del: 1,
  em: 1,
  i: 1,
  ins: 1,
  label: 1,
  q: 1,
  small: 1,
  span: 1,
  strong: 1,
  sub: 1,
  sup: 1
}
module.exports = {
  visited: function (e, owner) {
    if (!e.instance.hasClass('_visited'))
      e.instance.addClass('_visited')
    owner.callMethod('linkpress', e)
  },
  use: function (item) {
    return !item.c && !inline[item.name] && (item.attrs.style || '').indexOf('display:inline') == -1
  }
}
</wxs>
<block wx:for="{{nodes}}" wx:for-item="n" wx:for-index="i" wx:key="i">
  <!--图片-->
  <view wx:if="{{n.name=='img'}}" id="{{n.attrs.id}}" class="_img {{n.attrs.class}}" style="{{n.attrs.style}}" data-attrs="{{n.attrs}}" bindtap="imgtap">
    <rich-text nodes="{{[{attrs:{src:loading&&ctrl[i]!=2?loading:(lazyLoad&&!ctrl[i]?placeholder:n.attrs.src||''),alt:n.attrs.alt||'',width:n.attrs.width||'',style:'-webkit-touch-callout:none;width:100%;display:block'+(n.attrs.height?';height:'+n.attrs.height:'')},name:'img'}]}}" />
      <image class="_image" src="{{lazyLoad&&!ctrl[i]?placeholder:n.attrs.src}}" lazy-load="{{lazyLoad}}" show-menu-by-longpress="{{!n.attrs.ignore}}" data-i="{{i}}" data-index="{{n.attrs.i}}" data-source="img" bindload="loadImg" binderror="error" />
  </view>
  <!--文本-->
  <text wx:elif="{{n.type=='text'}}" decode>{{n.text}}</text>
  <text wx:elif="{{n.name=='br'}}">\n</text>
  <!--链接-->
  <view wx:elif="{{n.name=='a'}}" id="{{n.attrs.id}}" class="_a {{n.attrs.class}}" hover-class="_hover" style="{{n.attrs.style}}" data-attrs="{{n.attrs}}" bindtap="{{canIUse?handler.visited:'linkpress'}}">
    <trees class="_node" nodes="{{n.children}}" />
  </view>
  <!--视频-->
  <block wx:elif="{{n.name=='video'}}">
    <view wx:if="{{n.lazyLoad&&!n.attrs.autoplay}}" id="{{n.attrs.id}}" class="_video {{n.attrs.class}}" style="{{n.attrs.style}}" data-i="{{i}}" bindtap="loadVideo" />
    <video wx:else id="{{n.attrs.id}}" class="{{n.attrs.class}}" style="{{n.attrs.style}}" autoplay="{{n.attrs.autoplay}}" controls="{{n.attrs.controls}}" loop="{{n.attrs.loop}}" muted="{{n.attrs.muted}}" poster="{{n.attrs.poster}}" src="{{n.attrs.source[n.i||0]}}" unit-id="{{n.attrs['unit-id']}}" data-i="{{i}}" data-source="video" binderror="error" bindplay="play" />
  </block>
  <!--音频-->
  <audio wx:elif="{{n.name=='audio'}}" id="{{n.attrs.id}}" class="{{n.attrs.class}}" style="{{n.attrs.style}}" author="{{n.attrs.author}}" autoplay="{{n.attrs.autoplay}}" controls="{{n.attrs.controls}}" loop="{{n.attrs.loop}}" name="{{n.attrs.name}}" poster="{{n.attrs.poster}}" src="{{n.attrs.source[n.i||0]}}" data-i="{{i}}" data-source="audio" binderror="error" bindplay="play" />
  <!--广告-->
  <ad wx:elif="{{n.name=='ad'}}" class="{{n.attrs.class}}" style="{{n.attrs.style}}" unit-id="{{n.attrs['unit-id']}}" data-source="ad" binderror="error" />
  <!--列表-->
  <view wx:elif="{{n.name=='li'}}" id="{{n.attrs.id}}" class="{{n.attrs.class}}" style="{{n.attrs.style}};display:flex">
    <view wx:if="{{n.type=='ol'}}" class="_ol-bef">{{n.num}}</view>
    <view wx:else class="_ul-bef">
      <view wx:if="{{n.floor%3==0}}" class="_ul-p1">█</view>
      <view wx:elif="{{n.floor%3==2}}" class="_ul-p2" />
      <view wx:else class="_ul-p1" style="border-radius:50%">█</view>
    </view>
    <trees class="_node _li" lazyLoad="{{lazyLoad}}" loading="{{loading}}" nodes="{{n.children}}" />
  </view>
  <!--富文本-->
  <rich-text wx:elif="{{handler.use(n)}}" id="{{n.attrs.id}}" class="_p __{{n.name}}" nodes="{{[n]}}" />
  <!--继续递归-->
  <trees wx:else id="{{n.attrs.id}}" class="_node _{{n.name}} {{n.attrs.class}}" style="{{n.attrs.style}}" lazyLoad="{{lazyLoad}}" loading="{{loading}}" nodes="{{n.children}}" />
</block>