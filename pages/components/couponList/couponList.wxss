/* pages/components/couponList/couponList.wxss */
.flex-wrap{
  display: flex;
  align-items: center;
}
.flex-con{
  flex: 1;
}
/* 优惠券 */
.new-coupon-wrap { padding: 22rpx 0 22rpx 22rpx; background-color: #fff; box-sizing: border-box; }
.new-coupon-wrap .scroll-view { width: 100%; white-space: nowrap; font-size: 0; }
.new-coupon-wrap .coupon-item { display: inline-block; position: relative; line-height: 1.35; overflow: hidden; margin-right: 22rpx; }
.new-coupon-wrap .style1 .coupon-item { display: block; width: 640rpx; height: 202rpx; margin: 0 33rpx; }
.new-coupon-wrap .style1 .left-info { text-align: center; margin-top: 40rpx; height: 140rpx; }
.new-coupon-wrap .style1 .left-info .money { margin-top: 10rpx; margin-bottom: 10rpx; padding: 10rpx 50rpx 10rpx 30rpx; border-right: 1rpx solid rgba(255, 255, 255, 0.6); }
.new-coupon-wrap .style1 .money text { font-size: 60rpx; }
.new-coupon-wrap .style1 .coupon-title { padding-top: 10rpx; }
.new-coupon-wrap .style1 .right-info { height: 180rpx; text-align: left; padding: 0 40rpx; }
.new-coupon-wrap .style1 .con-wrap { margin-top: 10rpx; }
.new-coupon-wrap .style1 .right-info .desc { margin-bottom: 20rpx; }
.new-coupon-wrap .style1 .right-info .coupon-btn { width: 100rpx; height: 100rpx; border-radius: 50%; background-color: #fff; color: #F85C4E; margin-top: 0; font-size: 30rpx; box-sizing: border-box; padding: 12rpx; line-height: 38rpx; text-align: center; }
.new-coupon-wrap .style2 .coupon-item, .new-coupon-wrap .style3 .coupon-item { width: 342rpx; height: 186rpx;  /* height:175rpx; */ }
.new-coupon-wrap .style3 .coupon-item { width: 320rpx; margin-right: 0; }
.new-coupon-wrap .style3 .coupon-item:last-child { margin-right: 20rpx; }
.new-coupon-wrap .coupon_bg { display: block; width: 100%; height: 100%; }
.new-coupon-wrap .coupon-info { position: absolute; top: 10rpx; left: 35rpx; right: 30rpx; bottom: 10rpx; z-index: 1; }
.new-coupon-wrap .info-wrap { height: 118rpx; }
.new-coupon-wrap .style1 .info-wrap { height: 180rpx; }
.new-coupon-wrap .coupon-title { text-align: center; }
.new-coupon-wrap .coupon-title .title { position: relative; display: inline-block; padding: 10rpx 10rpx 0; }
.new-coupon-wrap .coupon-title text { color: rgba(255, 255, 255, 0.8); font-size: 22rpx; display: inline-block; max-width: 150rpx; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
.new-coupon-wrap .coupon-title .title:after, .new-coupon-wrap .coupon-title .title:before { width: 30rpx; height: 2rpx; background-color: rgba(255, 255, 255, 0.8); content: ''; position: absolute; top: 50%; margin-top: 2rpx; }
.new-coupon-wrap .coupon-title .title:after { right: -30rpx; }
.new-coupon-wrap .coupon-title .title:before { left: -30rpx; }
.new-coupon-wrap .left-info .money { font-size: 22rpx; color: rgba(255, 255, 255, 0.9); margin-bottom: 6rpx; }
.new-coupon-wrap .money text { font-size: 42rpx; display: inline-block; margin-top: -10rpx; }
.new-coupon-wrap .limit-use { font-size: 22rpx; color: rgba(255, 255, 255, 0.6); }
.new-coupon-wrap .right-info { text-align: center; }
.new-coupon-wrap .right-info .desc { color: #fff; font-size: 32rpx; }
.new-coupon-wrap .right-info .coupon-btn { color: rgba(255, 255, 255, 0.8); font-size: 22rpx; border: 1rpx solid rgba(255, 255, 255, 0.8); border-radius: 30rpx; padding: 6rpx 10rpx; margin-top: 10rpx; position: relative; }
.new-coupon-wrap .right-info .coupon-btn button { position: absolute; left: 0; top: 0; width: 100%; height: 100%; opacity: 0; z-index: 1; margin: 0; }