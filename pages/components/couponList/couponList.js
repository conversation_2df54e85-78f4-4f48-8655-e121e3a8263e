// components/navbar/index.js
const app = getApp();
import { couponReceive } from "../../../api/reuseRequest.js";
Component({
  properties: {
    couponList: {      
      type: Array,
      value:[]
    },
    esId:{          //领取店铺优惠券，店铺id
      type:String,
      value:""   
    }
  },
  data: {
    couponInfo:[]
  },
  observers: {
    'couponList': function(couponList) {
      this.setData({
        couponInfo:JSON.parse(JSON.stringify(couponList))
      })
      console.log("优惠券组件",this.data.couponInfo)
    }
  },
  attached: function () {
    
  },
  methods: {
    shareGet(e){
      let index=e.currentTarget.dataset.index;
      this.setData({
        [`couponInfo[${index}].needShare`]:0
      })
    },
    getCoupon: function (e) {
      var that = this;
      app.getSubId('applet_coupon_receive').then(res =>{
        that.toGetCoupon(e)
      });
      
    },
    // 领取优惠券
    toGetCoupon: function (e) {
      let data = {
        id: e.currentTarget.dataset.id
      }
      if(this.data.esId){
        data.esId = this.data.esId
      }
      couponReceive(data).then(res=>{
        console.log("优惠券领取成功",res)
      }).catch(err=>{
        console.log("优惠券领取失败",err)
      })
    },
  }
})