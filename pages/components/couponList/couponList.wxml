<!-- 新优惠券-->
<view class="new-coupon-wrap" wx:if="{{couponInfo.length>0}}">
	<scroll-view scroll-x class="scroll-view {{couponInfo.length==1?'style1':''}} {{couponInfo.length==2?'style2':''}} {{couponInfo.length>2?'style3':''}}">

		<block wx:key="index" wx:for="{{couponInfo}}" wx:for-item="coupon">
			<view class="coupon-item">
				<image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/couponimg/image_youhuijuan.png" class="coupon_bg fade_in" wx:if="{{couponInfo.length>1}}"></image>
				<image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/couponimg/image_bj.png" class="coupon_bg fade_in" wx:if="{{couponInfo.length==1}}"></image>
				<view class="coupon-info" wx:if="{{couponInfo.length>1}}">
					<view class="coupon-title">
						<view class="title"><text>{{coupon.name}}</text></view>
					</view>
					<view class="info-wrap flex-wrap">
						<view class="flex-con flex-wrap">
							<view class="left-info flex-con">
								<view class="money">￥<text>{{coupon.value}}</text></view>
								<text class="limit-use">{{coupon.limit==0?'无使用限制':'满'+coupon.limit+'减'+coupon.value}}</text>
							</view>
							<view class="right-info">
								<view class="desc">优惠券</view>
								<block wx:if="{{coupon.received==1}}">
									<view class="coupon-btn">已领取</view>
								</block>
								<block wx:if="{{coupon.startLeft==0&&coupon.received==0}}">
									<view class="coupon-btn" wx:if="{{coupon.needShare==0}}" bindtap="getCoupon" data-id="{{coupon.id}}">{{coupon.hadReceive==1?'已':'立即'}}领取</view>
									<view class="coupon-btn" wx:if="{{coupon.needShare==1}}">分享领取<button open-type="share" data-index="{{index}}"  data-id="{{coupon.id}}" bindtap='shareGet'></button></view>
								</block>
								<block wx:if="{{coupon.startLeft>0}}">
									<view class="coupon-btn">暂未开抢</view>
								</block>
							</view>
						</view>
					</view>
				</view>
				<view class="coupon-info" wx:if="{{couponInfo.length==1}}">
					<view class="info-wrap flex-wrap">
						<view class="flex-con flex-wrap">
							<view class="left-info flex-wrap">
								<view class="money">￥<text>{{coupon.value}}</text></view>
							</view>
							<view class="right-info flex-con">
								<view class="coupon-title">
									<view class="title"><text>{{coupon.name}}</text></view>
								</view>
								<view class="con-wrap flex-wrap">
									<view class="desc-con flex-con">
										<view class="desc">优惠券</view>
										<text class="limit-use">{{coupon.limit==0?'无使用限制':'满'+coupon.limit+'减'+coupon.value}}</text>
									</view>

									<block wx:if="{{coupon.received==1}}">
										<view class="coupon-btn">已经领取</view>
									</block>
									<block wx:if="{{coupon.startLeft==0&&coupon.received==0}}">
										<view class="coupon-btn" wx:if="{{coupon.needShare==0}}" bindtap="getCoupon" data-id="{{coupon.id}}">{{coupon.hadReceive==1?'已经':'立即'}}领取</view>
										<view class="coupon-btn" wx:if="{{coupon.needShare==1}}">分享领取<button open-type="share" data-index="{{index}}"  data-id="{{coupon.id}}" bindtap='shareGet'></button></view>
									</block>
									<block wx:if="{{coupon.startLeft>0}}">
										<view class="coupon-btn">暂未开抢</view>
									</block>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</block>
	</scroll-view>
</view>