// 自定义外链
const app = getApp();
Page({
  data: {
    
  },
  onLoad: function (e) {
    var that = this;
    that.requestUrl();
  },
  onShow:function(){
    app.setCartnum();//更新购物车数量
  },
  requestUrl:function(){
    var that = this;
 
    wx.$get({
      map: 'applet_promotional_video'
    },{
      pageList:true
    }).then(res=>{
      var data = res.data;
          that.setData({
            linksrc: data.vrurl,
            title: data.vrShareTitle ? data.vrShareTitle:'',
            cover: data.vrShareCover ? data.vrShareCover:''
          })
    }).catch(err=>{
      that.setData({
        linksrc: ''
      })
      console.log(err)
    })
  },
  onPullDownRefresh:function(){
    var that = this;
    that.requestUrl();
  },
  onShareAppMessage: function () {
    var that = this,
        linksrc = that.data.linksrc,
        title = that.data.title ? that.data.title:'',
        cover = that.data.cover ? that.data.cover:'';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/commonViewTab/commonViewTab'
    }
  }
})