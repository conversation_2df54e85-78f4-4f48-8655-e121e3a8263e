.list {
  width: 90%;
  margin: auto;
  margin-bottom: 10rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  background-color: white;
}

.bucket-number {
  display: flex;
  justify-content: space-between;
  padding-bottom: 14rpx;
  font-weight: 500;
  border-bottom: 1px solid #f2f2f2;
}

.bucket-detail {
  display: flex;
  padding: 20rpx 0;
}

.bucket-detail-price {
  margin-left: 20rpx;
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  align-items: center;
}

.bucket-detail-price-name{
  font-weight: 500;
}

.bucket-detail-price-totle{
  font-size: 25rpx;
}

.bucket-img {
  width: 200rpx;
  height: 160rpx;
}

.totle {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 20rpx 0;
  font-weight: 500;
  font-size: 25rpx;
  border-top: 1px solid #f2f2f2;
}

.computed-number {
  display: flex;
  align-items: center;
}

.minus-and-add {
  width: 50rpx;
  height: 50rpx;
}

.number {
  margin: 0 10rpx;
}
.button {
  margin-top: 40rpx;
  width: 200rpx;
}