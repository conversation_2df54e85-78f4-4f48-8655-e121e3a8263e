<import src="/pages/template/couponTemplate/couponTemplate.wxml" />
<!-- 自定义导航栏 -->
<nav-bar page-name="{{titleName}}" show-home="{{showHome}}" show-nav="{{showNav}}" is-gradient="{{isGradient}}" opacity-value="{{opacityValue}}" show-bar="{{showBar}}"></nav-bar>
<!-- 隐私协议弹窗 -->
<privacy-props></privacy-props>

<page-skeleton wx:if="{{!indexInfo}}"></page-skeleton>
<!-- <block wx:if="{{slient=='1'}}">
  <get-authorizeinfo bindinitPage="initPage"></get-authorizeinfo>
</block>
<block wx:if="{{slient=='0'}}"> -->
<!-- 客服回复提示弹窗 -->
<kefu-replytip is-showkfreply="{{isShowkfreply}}" reply-tips="{{replyTips}}" customer-mobile="{{customerMobile}}" contact-phone="{{contactPhone}}"></kefu-replytip>
<!-- 关注公众号 -->
<view style="background-color:#fff;padding:0 20rpx;" wx:if="{{followOpen==1}}">
  <official-account></official-account>
</view>
<!-- <video src="https://hujiatong123.zyzcc.cn/77W11184.mp4" autoplay controls></video> -->
<!--折叠菜单  -->
<fold-menu wx:if="{{isShowFoldMenu}}"></fold-menu>
<!--背景音乐组件  -->
<bgMusic is-play="{{isPlay}}" music-url="{{musicUrl}}" music-title="{{musicTitle}}"></bgMusic>
<!-- 添加桌面提示 -->
<block wx:if="{{scene!=1154}}">
  <addDesktopTip></addDesktopTip>
</block>
<!-- 订单提醒 -->
<order-tips is-show="{{isShowOrdertip}}"></order-tips>
<!-- 申请分销提示 -->
<view class="fenxiao-tip flex-wrap" wx:if="{{distribOpen==1&&isShowFenxiao==0&&isapply==0}}">
  <image src="{{userInfo.avatar}}" mode='aspectFill' class='fade_in user-avatar' webp="{{true}}"></image>
  <view class='tips-txt flex-con'>
    <view>亲爱的 <text>{{userInfo.nickname}}</text>，申请成为分销商即可分销赚佣金！</view>
  </view>
  <view class="fenxiao-btn" bindtap='toApplyFenxiao'><text>立即申请</text></view>
  <view class='close-tip' catchtap='hideFenxiaotip'>
    <image src="/images/icon_chose_white.png" mode='aspectFit'></image>
  </view>
</view>
<!-- 数据渲染提示 -->
<view class="no-data-loading" wx:if="{{!tempInfo.temp&&tempInfo.temp!=0}}">
  <!-- <image class="load-img" src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/loading_img.png"></image> 
  <view class="loading-tip">数据渲染中...</view>-->
  <image class="load-img" src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/loading-home.gif"></image>
</view>
<!--模板一-->
<view class="index-temp1" wx:if="{{tempInfo.temp == '3'}}">
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="#57BCBB" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <!-- <view class="top-part">
    <image src="{{tempInfo.backImg}}" mode="widthFix" class="top-bg"></image>
    <image src="{{shopLogo}}" mode="aspectFit" class="avatar"></image> 
  </view> -->
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <view class="good-list-wrap">
    <view class="title-name">
      <text>店铺推荐</text>
    </view>
    <view class="good-list good-view{{tempInfo.goodsStyle}}">
      <block wx:key="index" wx:for="{{shopGoods}}" wx:for-item="good">
        <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
          <view class="item-wrap">
            <view class="good-image">
              <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
              <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
              <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
            </view>
            <view class="good-intro">
              <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
              <view class="price-buy">
                ￥
                <text class="now-price">{{good.price}}</text>
                <text class="origin-price" wx:if="{{good.oriPrice>0}}">￥{{good.oriPrice}}</text>
              </view>
              <text class="buy-btn">购买</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
  <!--上拉加载提示-->
  <view class="loading-tip" wx:if="{{showLoading}}">
    <view class="icon_load">
      <view id="floatingBarsG">
        <view class="blockG" id="rotateG_01"></view>
        <view class="blockG" id="rotateG_02"></view>
        <view class="blockG" id="rotateG_03"></view>
        <view class="blockG" id="rotateG_04"></view>
        <view class="blockG" id="rotateG_05"></view>
        <view class="blockG" id="rotateG_06"></view>
        <view class="blockG" id="rotateG_07"></view>
        <view class="blockG" id="rotateG_08"></view>
      </view>
    </view>
    <text>努力加载中...</text>
  </view>
  <view class="nomore-tip" wx:if="{{noMoretip}}">没有更多数据了</view>
</view>
<!--模板二-->
<!-- 秒杀加过 -->
<view class="index-temp2" wx:if="{{tempInfo.temp == '4'}}">
  <view class="top-part">
    <!-- VR全景图 -->
    <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <image src="{{tempInfo.backImg}}" class="top-bg"></image>
    <view class="avatar-name">
      <view class="avatar">
        <image src="{{shopLogo}}" mode="aspectFit" webp="{{true}}"></image>
      </view>
      <text>{{shopName}}</text>
    </view>
    <view class="menu-wrap">
      <view class="menu-item active">
        <image src="../../images/icon_menu_sy.png" mode="aspectFit"></image>
        <text>首页</text>
      </view>
      <view class="menu-item" data-type="all" bindtap="allGoods">
        <image src="../../images/icon_menu_qbsp.png" mode="aspectFit"></image>
        <text>全部商品</text>
      </view>
      <view class="menu-item" data-type="sale" bindtap="allGoods">
        <image src="../../images/icon_menu_cxsp.png" mode="aspectFit"></image>
        <text>促销商品</text>
      </view>
      <view class="menu-item" bindtap="shopNotice">
        <image src="../../images/icon_menu_dpgg.png" mode="aspectFit"></image>
        <text>店铺公告</text>
      </view>
    </view>
  </view>
  <view class="search-wrap" bindtap="searchPage">
    <image src="../../images/icon_search.png" mode="aspectFit"></image>
    <text>在店内搜索</text>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <view class="banner-wrap" wx:if="{{slideImgUrls.length>0}}">
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="#F4555A" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <view class="address-show flex-wrap" data-name="{{tempInfo.title}}" data-address="{{tempInfo.address}}" data-lng="{{tempInfo.lng}}" data-lat="{{tempInfo.lat}}" bindtap="seemap">
    <text class="flex-con">{{tempInfo.address}}</text>
    <image src="../../images/icon_dw.png" mode="aspectFit"></image>
  </view>
  <view class="good-fenlei" style="margin-bottom:0;" wx:if="{{shortMenu.length>0}}">
    <view class="title-name">
      <text>推荐分类</text>
    </view>
    <!-- 分类导航 -->
    <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
      <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
        <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
          <swiper-item class="swiper-item">
            <view class="common-nav-list">
              <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
                <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                  <image src="{{menu.icon}}" class="nav-img"></image>
                  <text class="nav-text">{{menu.name}}</text>
                  <block wx:if="{{menu.type=='106'}}">
                    <navigator class="applet-jump" target="miniProgram" app-id="{{menu.url}}" path="{{menu.path}}" open-type="navigate" />
                  </block>
                </view>
              </block>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <view class="good-list-wrap">
    <view class="title-name">
      <text>推荐宝贝</text>
    </view>
    <view class="good-list good-view{{goodShow}}">
      <block wx:key="index" wx:for="{{shopGoods}}" wx:for-item="good">
        <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
          <view class="item-wrap">
            <view class="good-image">
              <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
              <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
              <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
            </view>
            <view class="good-intro">
              <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text> {{good.name}}</view>
              <view class="price-buy">
                <view>
                  <text class="now-price">￥{{good.price}}</text>
                  <text class="origin-price" wx:if="{{good.oriPrice>0}}">￥{{good.oriPrice}}</text>
                </view>
              </view>
              <text class="buy-btn">购买</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
  <!--上拉加载提示-->
  <view class="loading-tip" wx:if="{{showLoading}}">
    <view class="icon_load">
      <view id="floatingBarsG">
        <view class="blockG" id="rotateG_01"></view>
        <view class="blockG" id="rotateG_02"></view>
        <view class="blockG" id="rotateG_03"></view>
        <view class="blockG" id="rotateG_04"></view>
        <view class="blockG" id="rotateG_05"></view>
        <view class="blockG" id="rotateG_06"></view>
        <view class="blockG" id="rotateG_07"></view>
        <view class="blockG" id="rotateG_08"></view>
      </view>
    </view>
    <text>努力加载中...</text>
  </view>
  <view class="nomore-tip" wx:if="{{noMoretip}}">没有更多数据了</view>
</view>
<!--数码模板三-->
<!-- 秒杀已加 -->
<view class="index-temp3" wx:if="{{tempInfo.temp == '15'}}">
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr bottom-right" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <view class="search-wrap">
      <view class="search-container" bindtap="searchPage">
        <image src="../../images/<EMAIL>" mode="aspectFit"></image>
        <text>{{tempInfo.searchText}}</text>
      </view>
    </view>
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="rgba(255,255,255,0.5)" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <!-- 分类导航 -->
  <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
    <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
      <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item class="swiper-item">
          <view class="common-nav-list">
            <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
              <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                <image src="{{menu.icon}}" class="nav-img"></image>
                <text class="nav-text">{{menu.name}}</text>
                <block wx:if="{{menu.type=='106'}}">
                  <navigator class="applet-jump" target="miniProgram" path="{{menu.path}}" app-id="{{menu.url}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <view class="hot-recommend">
    <view class="hot-title border-b">{{tempInfo.recommendTitle}}</view>
    <view class="hot-goods flex-wrap">
      <view class="left-good border-r" data-id="{{recommend[0].link}}" bindtap="goodDetail">
        <view class="left-good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{recommend[0].name}}</view>
        <view class="left-good-price">￥<text>{{recommend[0].price}}</text></view>
        <image src="{{recommend[0].img}}" mode="aspectFill"></image>
      </view>
      <view class="right-good">
        <view class="right-good-item border-b" data-id="{{recommend[1].link}}" bindtap="goodDetail">
          <view class="left-good-title">{{recommend[1].name}}</view>
          <view class="left-good-price">￥<text>{{recommend[1].price}}</text></view>
          <image src="{{recommend[1].img}}" mode="aspectFill"></image>
        </view>
        <view class="right-good-item" data-id="{{recommend[2].link}}" bindtap="goodDetail">
          <view class="left-good-title">{{recommend[2].name}}</view>
          <view class="left-good-price">￥<text>{{recommend[2].price}}</text></view>
          <image src="{{recommend[2].img}}" mode="aspectFill"></image>
        </view>
      </view>
    </view>
  </view>
  <block wx:key="index" wx:for="{{kinds}}" wx:for-item="kind">
    <view class="good-list-wrap" wx:if="{{kind.goods.length>0}}">
      <view class="title-name flex-wrap">
        <text class="flex-con">{{kind.name}}</text>
        <view class="more-enter" data-secondid="{{kind.link}}" data-title="{{kind.name}}" bindtap="flAllgoods">
          更多
          <image src="/images/icon_more_enter.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="good-list good-view2">
        <block wx:key="index" wx:for="{{kind.goods}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
            <view class="item-wrap border-l border-b">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
                <view class="price-buy">
                  ￥<text class="now-price">{{good.price}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>
</view>
<!--运动模板四-->
<view class="index-temp4" wx:if="{{tempInfo.temp == '16'}}">
  <view class="banner-wrap" style="margin-bottom:10rpx;">
    <!-- VR全景图 -->
    <view class="icon-vr bottom-right" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <view class="search-wrap">
      <view class="search-container" bindtap="searchPage">
        <image src="../../images/ydhw-ss.png" mode="aspectFit"></image>
        <text>{{tempInfo.searchText}}</text>
      </view>
    </view>
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="#0096D5" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <block wx:key="index" wx:for="{{kinds}}" wx:for-item="kind">
    <view class="good-list-wrap" wx:if="{{kind.goods.length>0}}">
      <view class="title-name flex-wrap">
        <image src="{{kind.img}}" class="title-img fade_in" mode="aspectFill"></image>
        <text>{{kind.name}}</text>
        <view class="flex-con" style="padding:5rpx 0;text-align:right" data-secondid="{{kind.link}}" data-title="{{kind.name}}" bindtap="flAllgoods">
          <image src="/images/ydhw_jt.png" class="icon-enter" mode="aspectFit"></image>
        </view>
      </view>
      <view class="good-list good-view2">
        <block wx:key="index" wx:for="{{kind.goods}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
            <view class="item-wrap">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
                <view class="price-buy">
                  ￥<text class="now-price">{{good.price}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>
</view>
<!--食品模板五-->
<!-- 秒杀已加 -->
<view class="index-temp5" wx:if="{{tempInfo.temp == '17'}}">
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="#51CBFB" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <view class="search-wrap">
    <view class="search-container" bindtap="searchPage">
      <image src="../../images/ydhw-ss.png" mode="aspectFit"></image>
      <text>{{tempInfo.searchText}}</text>
    </view>
  </view>
  <!-- 分类导航 -->
  <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
    <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
      <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item class="swiper-item">
          <view class="common-nav-list">
            <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
              <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                <image src="{{menu.icon}}" class="nav-img"></image>
                <text class="nav-text">{{menu.name}}</text>
                <block wx:if="{{menu.type=='106'}}">
                  <navigator class="applet-jump" target="miniProgram" path="{{menu.path}}" app-id="{{menu.url}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <!-- 今日特价 -->
  <block wx:key="index" wx:for="{{kinds}}" wx:for-item="kind">
    <view class="good-list-wrap" style="margin-top:16rpx;" wx:if="{{kind.goods.length>0}}">
      <view class="title-name flex-wrap">
        <text class="flex-con">{{kind.name}}</text>
        <view class="more-enter" data-secondid="{{kind.link}}" data-title="{{kind.name}}" bindtap="flAllgoods">
          <image src="/images/icon_more_enter.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="good-list good-view2">
        <block wx:key="index" wx:for="{{kind.goods}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
            <view class="item-wrap border-l border-b">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
                <view class="price-buy">
                  ￥<text class="now-price">{{good.price}}</text> 元
                </view>
                <!-- <view class="buy-btn">
                  <image src="/images/icon_add_cart.png" mode="aspectFit"></image>
                </view> -->
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>
</view>
<!--家纺模板六-->
<!-- 秒杀已加 -->
<view class="index-temp6" wx:if="{{tempInfo.temp == '18'}}">
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="#57BCBB" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <view class="search-wrap">
    <view class="search-container" bindtap="searchPage">
      <image src="../../images/ydhw-ss.png" mode="aspectFit"></image>
      <text>{{tempInfo.searchText}}</text>
    </view>
  </view>
  <!-- 分类导航 -->
  <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
    <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
      <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item class="swiper-item">
          <view class="common-nav-list">
            <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
              <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                <image src="{{menu.icon}}" class="nav-img"></image>
                <text class="nav-text">{{menu.name}}</text>
                <block wx:if="{{menu.type=='106'}}">
                  <navigator class="applet-jump" target="miniProgram" path="{{menu.path}}" app-id="{{menu.url}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <view class="hot-recommend" style="margin-top:16rpx;">
    <view class="title-name flex-wrap">
      <text class="flex-con" style="padding:20rpx;">{{tempInfo.recommendTitle}}</text>
      <view class="more-enter" data-type="sale" wx:if="{{recommend.length>1}}" bindtap="allGoods">
        查看全部
        <image src="/images/icon_more_enter.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="recommend-good">
      <scroll-view class="{{recommend.length<=1?'single-img':''}}" scroll-x>
        <block wx:key="index" wx:for="{{recommend}}" wx:for-item="recommend">
          <image src="{{recommend.img}}" class="fade_in" data-id="{{recommend.link}}" bindtap="goodDetail"></image>
        </block>
      </scroll-view>
    </view>
  </view>
  <block wx:key="index" wx:for="{{kinds}}" wx:for-item="kind">
    <view class="good-list-wrap" wx:if="{{kind.goods.length>0}}">
      <!-- <view class="title-name">
        <text>{{kind.name}}</text>
      </view> -->
      <view class="title-name flex-wrap" bindtap="openGoodList" data-secondid="{{kind.link}}" data-title="{{kind.name}}">
        <text class="flex-con">{{kind.name}}</text>
        <view class="more-enter">
          更多
          <image src="/images/icon_more.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="good-list good-view2">
        <block wx:key="index" wx:for="{{kind.goods}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
            <view class="item-wrap">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
                <view class="price-buy">
                  ￥<text class="now-price">{{good.price}}</text>
                </view>
                <!-- <view class="buy-btn">
                  <image src="/images/icon_add_cart.png" mode="aspectFit"></image>
                </view> -->
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>
</view>
<!--女装模板七-->
<!-- 秒杀已加过 -->
<view class="index-temp7" wx:if="{{tempInfo.temp == '19'}}">
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <swiper indicator-dots="true" indicator-color="#fff" indicator-active-color="#63BCE3" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <view class="search-wrap">
    <view class="search-container" bindtap="searchPage">
      <image src="../../images/ydhw-ss.png" mode="aspectFit"></image>
      <text>{{tempInfo.searchText}}</text>
    </view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <!-- 分类导航 -->
  <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
    <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
      <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item class="swiper-item">
          <view class="common-nav-list">
            <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
              <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                <image src="{{menu.icon}}" class="nav-img"></image>
                <text class="nav-text">{{menu.name}}</text>
                <block wx:if="{{menu.type=='106'}}">
                  <navigator class="applet-jump" target="miniProgram" path="{{menu.path}}" app-id="{{menu.url}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <view class="good-list-wrap" style="margin-top:16rpx">
    <view class="title-name flex-wrap">
      <text class="flex-con">{{tempInfo.recommendTitle}}</text>
      <view class="more-enter" data-type="sale" bindtap="allGoods">
        更多
        <image src="/images/icon_more.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="recommend-con">
      <block wx:key="index" wx:for="{{recommend}}" wx:for-item="recommend">
        <view class="fl-img-item" data-id="{{recommend.link}}" bindtap="goodDetail">
          <image src="{{recommend.img}}" class="fade_in"></image>
        </view>
      </block>
    </view>
  </view>
  <block wx:key="index" wx:for="{{kinds}}" wx:for-item="kind">
    <view class="good-list-wrap" wx:if="{{kind.goods.length>0}}">
      <view class="title-img-wrap" data-secondid="{{kind.link}}" data-title="{{kind.name}}" bindtap="flAllgoods">
        <view class="fl-img">
          <image src="{{kind.img}}" class="fade_in" mode="widthFix"></image>
        </view>
        <view class="title-con">
          <image src="/images/title_left.png" mode="aspectFit"></image>
          <text>{{kind.name}}</text>
          <image src="/images/title_right.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="good-list good-view2">
        <block wx:key="index" wx:for="{{kind.goods}}" wx:for-item="good">
          <view class="good-item" data-id="{{good.id}}" bindtap="goodDetail">
            <view class="item-wrap">
              <view class="good-image">
                <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="good-intro">
                <view class="good-title"><text class="seckill-label" wx:if="{{good.seckill==1}}">秒杀</text>{{good.name}}</view>
                <view class="price-buy flex-wrap">
                  ￥<text class="now-price">{{good.price}}</text>
                  <text class="flex-con sold" wx:if="{{good.soldShow==1}}">{{good.sold}}人付款</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </block>
</view>
<!--微酒水模板八-->
<view class="index-temp8" wx:if="{{tempInfo.temp == '48'}}">
  <view class="search-wrap flex-wrap">
    <!-- <view class="address-show" data-name="{{tempInfo.title}}" data-address="{{tempInfo.address}}" data-lng="{{tempInfo.lng}}" data-lat="{{tempInfo.lat}}" bindtap="seemap">
      <image src="/images/icon_dingwei.png" mode="aspectFit"></image>
      <text>{{tempInfo.address}}</text>
    </view> -->
    <view class="search-container flex-con" bindtap="searchPage">
      <image src="/images/icon_sousuo.png" mode="aspectFit"></image>
      <text>{{tempInfo.searchText}}</text>
    </view>
  </view>
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <swiper indicator-dots="true" indicator-color="##747376" indicator-active-color="#fff" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <!-- 分类导航 -->
  <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
    <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
      <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item class="swiper-item">
          <view class="common-nav-list">
            <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
              <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                <image src="{{menu.icon}}" class="nav-img"></image>
                <text class="nav-text">{{menu.name}}</text>
                <block wx:if="{{menu.type=='106'}}">
                  <navigator class="applet-jump" target="miniProgram" path="{{menu.path}}" app-id="{{menu.url}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <block wx:key="index" wx:for="{{kindsGoods}}" wx:for-item="kind">
    <view class="good-list-wrap" style="margin-top:16rpx;">
      <view class="title-name flex-wrap border-b">
        <text class="flex-con">{{kind.name}}</text>
      </view>
      <view class="recommend-con">
        <scroll-view scroll-x class="second-flview">
          <view class="second-fllist">
            <block wx:key="index" wx:for="{{kind.kindData}}" wx:for-item="secondkind">
              <view class="second-item" data-firstid="{{kind.id}}" data-index="{{index}}" data-goods="{{secondkind.goods}}" bindtap="toggleSecondgoods">
                <image src="{{secondkind.img}}" mode="aspectFill"></image>
                <text class="flname">{{secondkind.name}}</text>
              </view>
            </block>
          </view>
        </scroll-view>
        <view class="no-data" style="padding:30rpx 0;" wx:if="{{kind.selectedGoods.length<=0}}">
          <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png" style="width:80rpx;height:80rpx;"></image>
          <text>暂无对应商品哦~</text>
        </view>
        <scroll-view scroll-x class="flgoods" wx:if="{{kind.selectedGoods.length>0}}">
          <view class="flgoods-list">
            <block wx:key="index" wx:for="{{kind.selectedGoods}}" wx:for-item="good">
              <view class="flgoods-item" data-id="{{good.id}}" bindtap="goodDetail">
                <view class="good-image">
                  <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
                  <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
                  <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
                </view>
                <view class="flgoods-intro">
                  <view class="flgoods-title">{{good.name}}</view>
                  <view class="price">
                    <view class="now-price">￥<text>{{good.newPrice}}</text></view>
                    <view class="ori-price">￥{{good.oldPrice}}</view>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>
</view>
<!--微图书模板九-->
<view class="index-temp9" wx:if="{{tempInfo.temp == '47'}}">
  <view class="banner-wrap">
    <!-- VR全景图 -->
    <view class="icon-vr bottom-right" wx:if="{{vrurl&&vrurl!=''}}" data-vrurl="{{vrurl}}" data-title="{{vrShareTitle}}" data-cover="{{vrShareCover}}" bindtap="toWebview">
      <image src="/images/icon_vr.png" mode="aspecFit"></image>
      <text>全景图</text>
    </view>
    <view class="search-wrap">
      <view class="search-container" bindtap="searchPage">
        <image src="../../images/icon_sousuo.png" mode="aspectFit"></image>
        <text>{{tempInfo.searchText}}</text>
      </view>
    </view>
    <image src="/images/banner_top_img.png" class="banner-top-img"></image>
    <swiper indicator-dots="true" indicator-color="##747376" indicator-active-color="#fff" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image" mode="aspectFit" />
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
    <view class="video-play" hover-class="video_hover" data-url="{{videoInfo.url}}" wx:if="{{showPlaybtn}}">
      <navigator data-url="{{videoInfo.url}}" bindtap="toVideo">
        <image src="../../images/play.png" class="play-btn fade_in" mode="aspectFit"></image>
        <text class="video-duration">{{videoInfo.time}}</text>
      </navigator>
    </view>
  </view>
  <!-- 分类导航 -->
  <view class="common-nav-wrap" wx:if="{{shortMenu.length>0}}">
    <swiper class="common-nav-swiper {{shortMenu.length<=5?'nav-style1 flex':''}} {{shortMenu.length==4?'num4':''}} {{shortMenu.length<=3?'num3':''}} {{shortMenu.length==6?'nav-style1 num3 num6':''}} {{(shortMenu.length==7||shortMenu.length==8)?'nav-style1 num4 num7':''}} {{shortMenu.length>8?'nav-style1 num9':''}} {{shortMenu.length>10?'num11':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicatorColor="#f0f0f0" indicatorActiveColor="#aaa">
      <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item class="swiper-item">
          <view class="common-nav-list">
            <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="menu">
              <view class="common-nav-item" data-id="{{menu.link}}" data-type="{{menu.type}}" data-url="{{menu.url}}" bindtap="openFenleiLink">
                <image src="{{menu.icon}}" class="nav-img"></image>
                <text class="nav-text">{{menu.name}}</text>
                <block wx:if="{{menu.type=='106'}}">
                  <navigator class="applet-jump" target="miniProgram" path="{{menu.path}}" app-id="{{menu.url}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 头条 -->
  <view class="notice-wrap flex-wrap" wx:if="{{noticeList.length>0&&noticeInfo.noticeStatus==1}}" data-title="{{noticeInfo.noticeTitle}}" bindtap="toinformationPage">
    <view class="tt-title" style="color:{{noticeInfo.noticeColor?noticeInfo.noticeColor:'#FE6668'}}">{{noticeInfo.noticeTitle}}</view>
    <view class="beauty-toutiao flex-con">
      <swiper vertical="true" autoplay="true" circular="true" interval="4000" duration="600">
        <block wx:key="index" wx:for="{{noticeList}}" wx:for-item="noticeitem">
          <swiper-item>
            <block wx:key="index" wx:for="{{noticeitem}}">
              <view class="toutiao-item {{noticeitem.length==1?'style1':''}}">{{item}}</view>
            </block>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>
  <!-- 店铺名称地址 -->
  <view class="shopinfo-wrap" wx:if="{{indexInfo.shopInfo.open==1}}">
    <view class="shop-info flex-wrap">
      <image src="{{indexInfo.shopInfo.logo}}" class="shop-logo fade_in" mode="aspectFit"></image>
      <view class="flex-con">
        <view class="shop-name">{{indexInfo.shopInfo.name}}</view>
        <view class="shop-address">
          <image src="/images/syaddrimg/<EMAIL>" mode="aspectFit"></image>
          <text>{{indexInfo.shopInfo.address}}</text>
        </view>
      </view>
      <view class="short-opera" data-name="{{indexInfo.shopInfo.name}}" data-address="{{indexInfo.shopInfo.address}}" data-lng="{{indexInfo.shopInfo.lng}}" data-lat="{{indexInfo.shopInfo.lat}}" bindtap="seemap">
        <image src="/images/syaddrimg/icon_daohang.png" mode="aspectFit"></image>
      </view>
      <view class="short-opera">
        <image src="/images/syaddrimg/icon_kefu.png" mode="aspectFit"></image>
        <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
      </view>
      <view class="short-opera" data-mobile="{{indexInfo.shopInfo.mobile}}" bindtap='makePhone'>
        <image src="/images/syaddrimg/icon_lianxi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="shop-intro-txt" wx:if="{{indexInfo.shopInfo.brief!=''}}" data-link="{{indexInfo.shopInfo.link}}" bindtap="toCuslink">{{indexInfo.shopInfo.brief}}
      <text>查看更多</text>
    </view>
  </view>
  <!-- 分类页面跳转 -->
  <view class="fenlei-nav" wx:if="{{fenleiOPen==1}}">
    <view class="fenlei-nav-title"><text>{{fenleiTitle}}</text></view>
    <scroll-view class="{{fenleiMenu.length<=2?'lesstwo':''}}" scroll-x>
      <block wx:key="index" wx:for="{{fenleiMenu}}">
        <view class="fenlei-item" data-mobile="{{indexInfo.mobile}}" data-type="{{item.type}}" data-url="{{item.link}}" bindtap="openFenleiLink">
          <block wx:if="{{item.type=='101'}}">
            <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
          <image src="{{item.imgsrc}}"></image>
          <text>{{item.title}}</text>
        </view>
      </block>
    </scroll-view>
  </view>
  <!-- 优惠券信息 -->
  <template is="couponTemplate" data="{{couponList:couponList}}" />
  <!-- 热品推荐 -->
  <view class="good-list-wrap" style="margin-top:16rpx;" wx:if="{{recommend.length>0}}">
    <view class="title-name flex-wrap">
      <text class="flex-con">{{tempInfo.recommendTitle}}</text>
      <!-- <view class="more-enter" data-type="sale" bindtap="allGoods">
        查看更多
        <image src="/images/food-jt.png" mode="aspectFit"></image>
      </view> -->
    </view>
    <view class="recommend-con">
      <scroll-view scroll-x class="flgoods">
        <view class="flgoods-list">
          <block wx:key="index" wx:for="{{recommend}}">
            <view class="flgoods-item" data-id="{{item.link}}" bindtap="goodDetail">
              <!-- <image src="{{item.img}}" mode='aspectFill'></image> -->
              <view class="good-image">
                <image src="{{item.img}}" mode="aspectFill" class="img"></image>
                <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/vip-jiaobiao.png" wx:if="{{item.isVipPrice==1}}" class="vip-tag"></image>
                <view class="no-good" wx:if="{{good.stock<=0}}">售罄</view>
              </view>
              <view class="flgoods-intro">
                <view class="flgoods-title">{{item.name}}</view>
                <view class="price">
                  <view class="now-price">￥<text>{{item.price}}</text></view>
                  <!-- <view class="ori-price">￥1280</view> -->
                </view>
              </view>
            </view>
          </block>
        </view>
      </scroll-view>
    </view>
  </view>
  <view class="part-wrap" style='background-color:#fff;'>
    <!-- <view class="title-name border-b">全部产品
      <view class="more" bindtap="seeInformationpage">
        <text>查看更多</text>
        <image src="/images/img_right_jiantou.png" mode="aspectFit"></image>
      </view> 
    </view> -->
    <view class="service-tab">
      <scroll-view scroll-x>
        <block wx:key="index" wx:for="{{categoryGoods}}" wx:for-item="fl">
          <text class="{{curCategoryfl==fl.id?'active':''}}" data-id="{{fl.id}}" data-list="{{fl.list}}" bindtap='toggleCategoryfl'>{{fl.name}}</text>
        </block>
      </scroll-view>
    </view>
    <view class="no-data" wx:if="{{curCategorygoods.length<=0}}">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
      <text>暂无对应商品哦~</text>
    </view>
    <view class="service-list" wx:if="{{curCategorygoods.length>0}}">
      <block wx:key="index" wx:for="{{curCategorygoods}}" wx:for-item="good">
        <view class="service-item border-b flex-wrap" data-id="{{good.id}}" bindtap="goodDetail">
          <!-- <image src="{{good.cover}}" class="good-img fade_in" mode="aspectFill"></image> -->
          <view class="good-image">
            <image src="{{good.cover}}" mode="aspectFill" class="img"></image>
            <text class="good-img-label" style="background:{{themeColor1?themeColor1:'#F94700'}};" wx:if="{{good.listLabel}}">{{good.listLabel}}</text>
          </view>
          <view class="service-intro flex-con">
            <view class="title">{{good.name}}</view>
            <view class="brief">{{good.brief}}</view>
            <view class="price">￥<text>{{good.price}}</text> <text class="ori-price" wx:if="{{good.oriPrice>0}}">￥{{good.oriPrice}}</text></view>
          </view>
        </view>
      </block>
    </view>
  </view>
</view>
<!-- 自定义首页模板 -->
<view wx:if="{{tempInfo.temp == '0'}}">
  <indexCustom id="indexCustom"></indexCustom>
</view>
<!-- 首次领取的红包弹窗 -->
<view class="redPacket-part modal-coupon-part" wx:if="{{modelShow}}">
  <view class="redPacket-mask" bindtap="closeRedPacket"></view>
  <view class="redPacket-con modal-coupon-con">
    <image class="coupon-img-top" mode="scaleToFill" src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/indexCoupon/coupon-img-top.png" />
    <view class="close" bindtap="closeRedPacket">
      <image class="icon" mode="aspectFit" src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/indexCoupon/icon_guanbi.png" />
    </view>
    <view class="redPacket-title">
      <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/indexCoupon/img_zuo.png" mode="aspectFit"></image>
      <text>优惠券</text>
      <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/indexCoupon/img_you.png" mode="aspectFit"></image>
    </view>
    <scroll-view scroll-y class="coupon-list">
      <block wx:key="id" wx:for="{{coupon}}" wx:for-item="coupon">
        <view class="coupon-item flex-wrap">
          <view class="flex-con">
            <view class="coupon-name">
              <block wx:if="{{coupon.limit==0}}">{{coupon.value}}元无门槛</block>
              <block wx:else>满{{coupon.limit}}元减{{coupon.value}}元优惠券</block>
            </view>
            <view class="end-time">有效期：{{coupon.start}}-{{coupon.end}}</view>
          </view>
          <view class="coupon-val semicircle">
            <view class="face-val">￥<text class="big">{{coupon.value}}</text></view>
            <view class="get-btn" wx:if="{{coupon.received==1}}" bindtap="closeRedPacket">去使用</view>
            <view class="get-btn" wx:else data-id="{{coupon.id}}" bindtap="getCoupon">立即领取</view>
          </view>
        </view>
      </block>
    </scroll-view>
    <view class="coupon-get-tip">优惠券已放入您的账户中，点击“<text bindtap="tomyCoupon">我的优惠券</text>”即可查看</view>
  </view>
</view>
<!-- 幸运大转弹出层 -->
<view class="reward-modal fade_in" wx:if="{{isShowreward&&hasReward==1}}">
  <view class="modal-content">
    <view class="award-enter animated {{isShowreward?'zoomIn':''}}" bindtap='toLottery'>
      <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/lotteryimg/image_huodong.png"></image>
    </view>
    <view class="close" catchtap='hideAward'>
      <image src="/images/rewardimg/icon_guanbi.png" class="close"></image>
    </view>
  </view>
</view>
<!--幸运抽奖悬浮入口  -->
<view class="reward-enter" wx:if="{{!isShowreward&&hasReward==1}}" bindtap='toLottery'>
  <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/lotteryimg/image_cjrk.png"></image>
</view>
<!-- 首页配置的多类型跳转弹窗 -->
<view class="img-jump-modal fade_in" wx:if="{{showImgjump}}">
  <view class="img-jump-modal-con">
    <view class="close-btn" bindtap="closeImgjump">
      <image src="/images/icon_chose_white.png" mode="aspectFit"></image>
    </view>
    <image src="{{popup.cover}}" class="jump-img fade_in" mode="widthFix" data-type="{{popup.type}}" data-url="{{popup.url}}" bindtap="openFenleiLink"></image>
    <block wx:if="{{popup.type==101}}">
      <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
    </block>
    <block wx:if="{{popup.type=='106'}}">
      <navigator class="button" target="miniProgram" path="{{popup.path}}" app-id="{{popup.url}}" open-type="navigate" />
    </block>
  </view>
</view>
<!--联系客服-->
<view class="contact-wrap" style="background-color:{{themeColor1?themeColor1:navColor}};" wx:if="{{customerService==1}}">
  <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
  <image src="/images/icon_kefu.png" class='icon-kf' mode='aspectFit'></image>
</view>
<!--联系电话-->
<view class="makecall" style="background-color:{{themeColor1?themeColor1:navColor}};" wx:if="{{phoneOpen==1}}" bindtap="makeCall">
  <image src="/images/icon_bddh.png" mode="aspectFit"></image>
  <!-- <text class="iconfont icon-fenxiang"></text> -->
</view>
<!-- 分享 -->
<view class="makeservice" style="background-color:{{themeColor1?themeColor1:navColor}};" wx:if="{{shareOpen==1}}" catchtap="sharePage">
  <image src="/images/icon_fenx.png" mode="aspectFit"></image>
</view>
<!-- 组队领红包弹出层 -->
<view class="open-modal-mask" wx:if="{{isShowredpacket}}" data-type="isShowredpacket" bindtap="hideModal"></view>
<view class="open-modal" wx:if="{{isShowredpacket}}">
  <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zdhbimg/home_img_hb.png" class="modal-bg"></image>
  <view class="open-modal-con">
    <view class="shop-name">天店通微商城</view>
    <view class="shop-name">送您<text>88元</text>红包</view>
    <view class="opera-btn">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zdhbimg/home_zdhong_btn.png" class="btn-bg"></image>
      <text class="btn-txt">领红包</text>
    </view>
    <view class="close-btn" data-type="isShowredpacket" bindtap="hideModal">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zdhbimg/icon_close.png" mode="aspectFit"></image>
    </view>
  </view>
</view>
<!-- </block> -->
<!-- 获取用户信息提示框 -->
<!-- <view class="get-userinfo-modal" wx:if="{{slient==1&&isShowgetuser}}">
  <view class="get-userinfo">
    <view class="label-title">小程序授权提示</view>
    <view class="tip-txt">允许小程序获得你的头像昵称信息</view>
    <view class="flex-wrap border-t" style="padding:10rpx 0;">
      <view class="btn flex-con border-r" bindtap="hideGetuser">取消</view>
      <view class="btn flex-con confirm-btn">确定<button open-type='getUserInfo' catchtap='authorizeUserInfo'></button></view>
    </view>
  </view>
</view> -->
<!-- 朋友圈分享打开去往小程序提示 -->
<block wx:if="{{scene==1154}}">
  <go-applet-tip></go-applet-tip>
</block>