//index.js
//获取应用实例
const app = getApp();
Page({
  data: {
    showLoading: true,
    noMoretip: false,
    page: 0,
    cardData:{
      biz:"MzA4MDQxOTIxMQ==",
      encrypt_card_id:"dD9satX/U+kHLcj0B674joRlwDwRnjc+6crGXPus4K1aHbYpBaYTLo9MF0yt6DzX",
      outer_str:"Z0FXR0"
    }
  },
  onShow: function () {
    var that = this;
    that.requestCardList();
    app.setCartnum();//更新购物车数量
  },
  requestCardList: function (cid) {
    var that = this,
        data = {
          page : that.data.page,
          map : 'applet_card_coupons_list',
          // page : page,
        };
    let page = that.data.page
    wx.$get(data,{
      pageList:true
    }).then(res=>{
      let responseData = res.data;
      var allArr = [],
          initArr = that.data.cardList ? that.data.cardList : '',
          curArr = responseData.list,
          lastPageLength = curArr.length;
      console.log("initArr",initArr)
      if (page > 0) {
        allArr = initArr.concat(curArr);
      } else {
        allArr = responseData.list;
      }
      that.setData({
        cardList: allArr,
        cardCover: responseData.background
      })
      if (lastPageLength < 10) {
        that.setData({
          noMoretip: true,
          showLoading: false
        });
      }
    }).catch(err=>{
      if (page <= 0) {
        that.setData({
          cardList: [],
          noMoretip: false,
          showLoading: false
        })
      } else {
        that.setData({
          noMoretip: true,
          showLoading: false
        });
      }
      console.log(err)
    })
  },
  onPullDownRefresh: function () {
    var that = this;
    that.setData({
      page: 0,
      noMoretip: false,
      showLoading: true
    });
    that.requestCardList();
     
  },
  onReachBottom: function () {
    var that = this,
        isMore = that.data.noMoretip,
        page = that.data.page;
     
    page++;
    that.setData({
      page: page
    });
    if (!isMore) {
      that.requestCardList();
    }
  },
  getCard:function(e){
    var cardid = e.currentTarget.dataset.cardid,
        timestamp = e.currentTarget.dataset.timestamp,
        signature = e.currentTarget.dataset.signature,
        nonce_str = e.currentTarget.dataset.nonce_str;
    var cardext = JSON.stringify({ "timestamp": timestamp, "nonce_str": nonce_str, "signature": signature });
    wx.addCard({
      cardList: [
        {
          cardId: cardid,
          cardExt: cardext
        }
      ],
      success: function (res) {
        console.log(res.cardList) // 卡券添加结果
        // wx.openCard({
        //   cardList: res.cardList,
        //   success: function (res1) {
        //   }
        // })
      },
      fail:function(res){
        console.log(res);
      }
    })
  }
})
