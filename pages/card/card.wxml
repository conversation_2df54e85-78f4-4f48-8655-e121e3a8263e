
<back-home></back-home>
<view class="card-wrap"  wx:if="{{cardList.length>0}}">
  <view class="card-banner">
    <image src="{{cardCover}}" mode="widthFix"></image>
  </view>
  <view class="card-list">
    <block wx:key="index" wx:for="{{cardList}}" wx:for-item="card">
    <view class="card-item flex-wrap" data-cardid="{{card.cardId}}" data-timestamp="{{card.cardExt.timestamp}}" data-signature="{{card.cardExt.signature}}" data-nonce_str="{{card.cardExt.nonce_str}}" bindtap='getCard'>
      <image src="{{card.logo}}" class="fade_in left-logo"></image>
      <view class="right-info">
        <text class="tip-title">{{card.brandName}}</text>
        <text class="title">{{card.title}}</text>
      </view>
    </view>
    </block>
    <!-- <view class="card-item flex-wrap">
      <image src="/images/logo_200_200.png" class="fade_in left-logo"></image>
      <view class="right-info">
        <text class="tip-title">天店通小程序</text>
        <text class="title">小程序行业交流会</text>
      </view>
    </view>
    <view class="card-item flex-wrap">
      <image src="/images/logo_200_200.png" class="fade_in left-logo"></image>
      <view class="right-info">
        <text class="tip-title">天店通小程序</text>
        <text class="title">小程序行业交流会</text>
      </view>
    </view> -->
  </view>
</view>
<view class="empty-tip" wx:if="{{cardList.length<=0}}">
  <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png" mode="widthFix"></image>
  <text>暂时没有可以领取的卡券~</text>
</view>
<!--上拉加载提示-->
<view class="loading-tip" wx:if="{{showLoading}}">
  <view class="icon_load">
    <view id="floatingBarsG">
      <view class="blockG" id="rotateG_01"></view>
      <view class="blockG" id="rotateG_02"></view>
      <view class="blockG" id="rotateG_03"></view>
      <view class="blockG" id="rotateG_04"></view>
      <view class="blockG" id="rotateG_05"></view>
      <view class="blockG" id="rotateG_06"></view>
      <view class="blockG" id="rotateG_07"></view>
      <view class="blockG" id="rotateG_08"></view>
    </view>
  </view>
  <text>努力加载中...</text>
</view>
<view class="nomore-tip" wx:if="{{noMoretip&&cardList.length>0}}">没有更多数据了</view>
<!--错误提示-->
 
