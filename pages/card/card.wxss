page{
  background-color: #F2F2F2;
  border:none;
}
.card-banner{
  margin-bottom: 25rpx;
}
.card-banner image{
  display: block;
  width: 100%;
}
.card-list{
  padding: 0 25rpx 20rpx;
}
.card-item{
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 2rpx 2rpx 10rpx #dfdfdf;
  margin-top: 20rpx;
  padding: 20rpx;
}
.card-item .left-logo{
  height: 90rpx;
  width: 90rpx;
  display: block;
  border-radius: 50%;
  position: relative;
  top: -4rpx;
}
.right-info{
  padding-left: 20rpx;
  box-sizing: border-box;
  position: relative;
  top: 8rpx;
  max-width: 550rpx;
}
.right-info text{
  display: block;
  line-height: 1.6;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.right-info .tip-title{
  font-size: 26rpx;
  color: #555;
}
.right-info .title{
  font-size: 36rpx;
  color: #0f0f0f;
}
/* 内容为空提示 */
.empty-tip{
  padding: 40% 0;
}
.empty-tip image{
  width: 20%;
  display: block;
  margin: 0 auto;
}
.empty-tip text{
  font-size: 28rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
  display: block;
}