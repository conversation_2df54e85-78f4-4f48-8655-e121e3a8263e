// pages/commonFenlei/commonFenlei.js
const rqcfg = require('../../utils/constant.js');
const app = getApp();
Page({
  data: {
    pageType: '',
    categoryIdObject: {}
  },
  onLoad: function (e) {
    console.log("eeee", e)
    this.setData({
      'categoryIdObject.categoryId': e.categoryId ? e.categoryId : '',
      'categoryIdObject.cursecondId': e.cursecondId ? e.cursecondId : '',
    })
    var that = this;
    if (e) {
      that.setData({
        queryData: e
      })
    }
    if (e && e.pageType) {
      that.setData({
        savePageType: e.pageType
      })
    }
    that.requestFltype();
  },
  onShow() {
    const e = {}
    this.onLoad(e);
  },
  requestFltype: function () {
    var that = this;
    wx.$get({
      map: 'applet_goods_style'
    }).then(res => {
      var datainfo = res.data;
      that.setData({
        fontsizeStatus: datainfo.fontsize_status,
        pageType: datainfo.page
      })
    }).catch(err => {
      console.log(err)
    })
  },
  changeQueryData: function (e) {
    this.setData({
      queryData: e.detail
    })
    console.log("接收到的参数改变", this.data.queryData)
  },
  onPullDownRefresh: function () {
    var that = this;
    that.requestFltype();
    that.setData({
      isRefresh: !that.data.isRefresh
    })
  },
  onReachBottom: function () {
    var that = this;
    that.setData({
      isReachbottom: !that.data.isReachbottom
    })
  },
  // 获取列表转发url
  getShareUrl() {
    var that = this,
      title = '分类商品',
      shareInfo = app.globalData.shareInfo,
      url = '/pages/commonFenlei/commonFenlei',
      pageType = that.data.pageType,
      queryData = that.data.queryData;
    title = shareInfo.shareTitle ? shareInfo.shareTitle : title;
    // 一级分类商品参数
    if (pageType == 'one-flgoods') {
      var id = queryData.id ? queryData.id : '';
      url = url + '?id=' + id
    }
    // 二级分类商品参数
    if (pageType == 'fenlei-goods') {
      var cate1 = queryData.cate1 ? queryData.cate1 : '',
        cate2 = queryData.cate2 ? queryData.cate2 : '';
      // url = url + '?cate1=' + cate1 + '&cate2=' + cate2
      url = `${url}?categoryId=${this.data.categoryIdObject.categoryId}&cursecondId=${this.data.categoryIdObject.cursecondId}`
    }
    // 商品分类页面参数
    if (pageType == 'all-flgoods') {
      var title = queryData.title ? queryData.title : '';
      url = url + '?title=' + title;
    }
    // 全部商品页面参数
    if (pageType == 'all-goods') {
      var oneid = queryData.oneid ? queryData.oneid : '',
        secondid = queryData.secondid ? queryData.secondid : '',
        title = queryData.title ? queryData.title : '';
      url = url + '?oneid=' + oneid + '&secondid=' + secondid + '&title=' + title
    }
    // 搜索商品页面
    if (pageType == 'search-list') {
      var id = queryData.id ? queryData.id : '',
        type = queryData.type ? queryData.type : '',
        limit = queryData.limit ? queryData.limit : '',
        value = queryData.value ? queryData.value : '',
        title = queryData.title ? queryData.title : '';
      url = url + '?id=' + id + '&type=' + type + '&limit=' + limit + '&value=' + value + '&title=' + title
    }
    // 分类商品页面
    if (pageType == 'wn-goodslist') {
      var id = queryData.id ? queryData.id : '',
        title = queryData.title ? queryData.title : '';
      url = url + '?id=' + id + '&title=' + title
    }
    console.log(url);
    return url;
  },
  getCategoryId(e) {
    console.log("eeee", e);
    this.setData({
      categoryIdObject: e.detail
    })
  },
  onShareAppMessage: function () {
    console.log("12323r2345123412", app.globalData.shareInfo)
    var that = this,
      title = '分类商品',
      shareInfo = app.globalData.shareInfo || {},
      title = shareInfo.shareTitle || title;
    app.getPoint(that);
    let url = that.getShareUrl();
    return {
      title: title,
      path: url
    }
  },
  //朋友圈转发
  onShareTimeline() {
    var that = this;
    var title = that.data.title;
    var shareInfo = app.globalData.shareInfo || {};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    let url = that.getShareUrl();
    let queryData = url.replace('/pages/commonFenlei/commonFenlei?', '');
    queryData = queryData + "&suid=" + rqcfg.suid + '&appid=' + rqcfg.appid;
    console.log(queryData);
    return {
      title: title,
      query: queryData,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})