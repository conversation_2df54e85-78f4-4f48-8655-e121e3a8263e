<!--pages/commonFenlei/commonFenlei.wxml-->
<!-- 一级分类商品页面 -->
<block wx:if="{{pageType=='one-flgoods'}}">
  <one-flgoods fontsize-status="{{fontsizeStatus}}" class="flgoods" is-refresh="{{isRefresh}}" bindchangeQuery="changeQueryData" is-bottom="{{isReachbottom}}" query-data="{{queryData}}"></one-flgoods>
</block>

<!-- 商品分类页面 -->
<block wx:if="{{pageType=='all-flgoods'}}">
  <all-flgoods fontsize-status="{{fontsizeStatus}}" is-refresh="{{isRefresh}}" bindchangeQuery="changeQueryData" is-bottom="{{isReachbottom}}" query-data="{{queryData}}"></all-flgoods>
</block>
<!-- 全部商品页面 -->
<block wx:if="{{pageType=='all-goods'}}">
  <all-goods fontsize-status="{{fontsizeStatus}}" is-refresh="{{isRefresh}}" is-bottom="{{isReachbottom}}" query-data="{{queryData}}"></all-goods>
</block>
<!-- 搜索商品页面 -->
<block wx:if="{{pageType=='search-list'}}">
  <search-list fontsize-status="{{fontsizeStatus}}" is-refresh="{{isRefresh}}" is-bottom="{{isReachbottom}}" query-data="{{queryData}}"></search-list>
</block>
<!-- 分类商品页面 -->
<block wx:if="{{pageType=='wn-goodslist'}}">
  <wn-goodslist fontsize-status="{{fontsizeStatus}}" is-refresh="{{isRefresh}}" is-bottom="{{isReachbottom}}" query-data="{{queryData}}" bindchangeQuery="changeQueryData"></wn-goodslist>
</block>
<!-- 二级分类商品页面 -->
<block wx:if="{{pageType=='fenlei-goods'}}">
  <fenlei-goods fontsize-status="{{fontsizeStatus}}" bind:getCategoryId="getCategoryId" is-refresh="{{isRefresh}}" bindchangeQuery="changeQueryData" is-bottom="{{isReachbottom}}" query-data="{{queryData}}" category-object="{{categoryIdObject}}"></fenlei-goods>
</block>