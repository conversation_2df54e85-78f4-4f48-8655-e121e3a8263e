//index.js
//获取应用实例
const app = getApp();
const rqcfg = require('../../utils/constant.js');
Page({
  data: {
    
  },
  onLoad: function (e) {
    var that = this;
    var navBar= this.selectComponent('.nav-bar');
    this.setData({
      navHeight:navBar.getNavheight()
    })
    if(e){
      that.setData({
        queryData:e
      })
    }
    var title = '商品';
    if (e && e.title) {
      title = e.title
    }
    that.setData({
      title: title
    })
    app.setNavtitle(e.title);
 
      that.setData({
        showComponent:true
      })
    
  },
  onShow: function () {
    var that = this;
  },
  onPullDownRefresh: function () {
    var that = this;
    that.setData({
      isRefresh:!that.data.isRefresh
    })
  },
  onReachBottom: function () {
    var that = this;
    that.setData({
      isReachbottom:!that.data.isReachbottom
    })
  },
  onShareAppMessage: function () {
    var that = this,
        mid  = app.globalData.userInfo?app.globalData.userInfo.mid:'',
        title = that.data.title ? that.data.title : '商品列表',
        oneid = that.data.queryData.oneid ? that.data.queryData.oneid:'',
        secondid = that.data.queryData.secondid ? that.data.queryData.secondid : '';
    app.getPoint(that);
    return {
      title: title,
      path: '/pages/allgoodsPage/allgoodsPage?oneid=' + oneid + '&secondid=' + secondid + '&title=' + title+'&mid='+mid
    }
  },
  //朋友圈转发
  onShareTimeline(){
    var that = this;
    var mid  = app.globalData.userInfo?app.globalData.userInfo.mid:'',
        oneid = that.data.queryData.oneid ? that.data.queryData.oneid:'',
        secondid = that.data.queryData.secondid ? that.data.queryData.secondid : '';
    var title = that.data.title;
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid+'&oneid=' + oneid + '&secondid=' + secondid + '&title=' + title+'&mid='+mid,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})