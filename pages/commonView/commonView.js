// 自定义外链
const app = getApp();
Page({
  data: {
    
  },
  onLoad: function (e) {
    var that = this;
    if (e && e.type) {
      if (e.type == 'share') {
        that.setData({
          linksrc: unescape(e.url),
          title: e.title,
          cover: e.cover
        })
        return
      }
      
    }
    wx.getStorage({
      key: 'webviewUrl',
      success: function(res) {
        var vrInfo = res.data;
        app.setNavtitle('VR全景');
        if (!wx.canIUse('web-view')) {
          wx.showModal({
            title: '提示',
            content: '当前微信版本过低，无法使用该webview组件，请升级到最新微信版本后重试。'
          })
        } else {
          that.setData({
            linksrc: vrInfo.vrurl,
            title: vrInfo.title ? vrInfo.title:'',
            cover: vrInfo.cover ? vrInfo.cover:''
          })
        }
      },
      fail:function(){
        
      }
    })
  },
  onShareAppMessage:function(){
    var that = this,
        linksrc = that.data.linksrc,
        title = that.data.title,
        cover = that.data.cover;
    linksrc = escape(linksrc);
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/commonView/commonView?type=share&&url=' + linksrc + '&title=' + title + '&cover=' + cover
    }
  }
})