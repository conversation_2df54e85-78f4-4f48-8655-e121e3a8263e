const app = getApp();
import { setPageviewlink } from "../../utils/reuseFunc.js";
const rqcfg = require('../../utils/constant.js');  
Page({
  data: {
    tempid: '',
    isPullDownRefresh: false,
  },
  onLoad: function (e) {
    var that = this;
    if(e&&e.id){
      that.setData({
        tempid: e.id
      })
    }
    setPageviewlink(that, '');
  },
  onShow: function () {
    app.setCartnum();//更新购物车数量
  },
  onPullDownRefresh: function () {
    var that = this;
    that.setData({
      isPullDownRefresh: true
    });
    setTimeout(function () {
      wx.stopPullDownRefresh();
      that.setData({
        isPullDownRefresh: false
      });
    }, 600)
  },
  onShareAppMessage: function () {
    var that = this;
    var title = '';
    let shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    let cover = shareInfo.shareCover || '';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/webviewTab3/webviewTab3?id='+that.data.tempid
    }
  },
  //朋友圈转发
  onShareTimeline(){
    var that = this;
    var title = '';
    let shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    let cover = shareInfo.shareCover || '';
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid+'&id='+that.data.tempid,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})