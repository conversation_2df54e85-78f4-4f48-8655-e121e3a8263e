<back-home></back-home>
<!--折叠菜单  -->
<fold-menu></fold-menu>
<block wx:if="{{isapply==2}}">
  <view class="nofenxiao-tip">
    <image src="/images/success_tip.png" mode='aspectFit'></image>
    <text>您已经成为分销商！</text>
  </view>
</block>
<block wx:if="{{isapply==1}}">
  <view class="nofenxiao-tip">
    <image src="/images/icon_shz.png" mode='aspectFit'></image>
    <text>您的申请正在审核中，请耐心等待！</text>
  </view>
</block>
<block wx:if="{{isapply==0}}">
  <view class="fenxiao-cover">
    <image src="{{partners.banner}}" mode='widthFix'></image>
  </view>
  <view class="myphone">
    <view class="fenxiao-tip">
      <view wx:if="{{!partners.welcomeText}}">欢迎您成为
        <text>{{partners.shopName}}</text> 分销商，请填写申请信息。
      </view>
      <view wx:if="{{partners.welcomeText}}">{{partners.welcomeText}}</view>
      <view>邀请人：
        <text>{{partners.recommender}}</text>（请核对）
      </view>
      <!-- <view>欢迎您成为
      <text>{{partners.shopName}}</text> 分销商，请填写申请信息。</view>
    <view>邀请人：
      <text>{{partners.recommender}}</text>（请核对）</view> -->
    </view>
    <view class="info-wrap">
      <view class="info-input" wx:if="{{partners.hasname==1}}">
        <input type="text" maxlength='10' value="{{name}}" placeholder='请填写真实姓名' bindinput='nameChange'></input>
      </view>
      <view class="info-input" wx:if="{{partners.hasphone==1}}">
        <input type="number" maxlength='11' value="{{phone}}" placeholder='请填写手机号，方便联系' bindinput='phoneChange'></input>
      </view>
      <view class="info-input" wx:if="{{partners.haswx==1}}">
        <input type="text" placeholder='请填写微信号' value="{{wechatnum}}" bindinput='wechatnumChange'></input>
      </view>
    </view>
    <view class='confirm-btn' bindtap='submitRequest'>{{partners.btnText?partners.btnText:'我要成为分销商'}}</view>
  </view>
  <view class='partners-privilege'>
    <text class='label-name'>分销商特权</text>
    <view class="privilege-info">
      <block wx:key="index" wx:for="{{partners.privilege}}" wx:for-item="privilege">
        <view class='privilege-item flex-wrap border-b'>
          <image src="{{privilege.iconSrc}}"></image>
          <view class="flex-con">
            <text class='privilege-name'>{{privilege.firstTitle}}</text>
            <text class='privilege-intro'>{{privilege.secondTitle}}</text>
          </view>
        </view>
      </block>
      <view class='privilege-item border-b'>
        <view class='privilege-txt'>
          <text decode="{{true}}">{{partners.desc}}</text>
        </view>
      </view>
    </view>
  </view>
</block>
<!--错误提示-->