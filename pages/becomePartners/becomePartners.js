const app = getApp();
Page({
  data: {
		isUnfold:true,
    name: '',
    phone: '',
    wechatnum: ''
  },
  onLoad: function (options) {
    var that = this;
    
 
      that.requestPartners();
    
  },
  onReady: function () {
  
  },
  onShow: function () {
    app.setNavtitle('成为分销商');
  },
  requestPartners: function () {
    var that = this;
    var data = {};
    data.map = 'applet_three_configure_new';
    //发起请求，获取列表列表
    wx.showToast({
      title: '加载中',
      icon: 'loading',
      mask: true,
      duration: 10000
    });
    
  wx.$get(data,{
    stopPull:true
  }).then(res=>{
    let responseData = res.data;
    that.setData({
      partners: responseData,
      isapply: responseData.isapply
    })
  }).catch(err=>{

  console.log(err)
  })
  },
  nameChange:function(e){
    var that = this;
    that.setData({
      name:e.detail.value
    })
  },
  phoneChange: function (e) {
    var that = this;
    that.setData({
      phone: e.detail.value
    })
  },
  wechatnumChange: function (e) {
    var that = this;
    that.setData({
      wechatnum: e.detail.value
    })
  },
  submitRequest: function () {
    var that = this;
    app.getSubId( 'applet_three_apply_distribution_new').then(res =>{that.toSubmitRequest()})
  },
  toSubmitRequest:function(e){
    var that = this;
    var data = {
      map:'applet_three_apply_distribution_new',
      realname: that.data.name,
      mobile: that.data.phone,
      wxno: that.data.wechatnum,
    },
    hasname = that.data.partners.hasname,
    hasphone = that.data.partners.hasphone,
    haswx = that.data.partners.haswx;
    if (hasname==1){
      if (!data.realname) {
        wx.$showToast( "请输入您的姓名");
        return;
      }
    }

    if (haswx == 1) {
      if (!data.wxno) {
        wx.$showToast( "请输入您的微信号");
        return;
      }
    }
    
    wx.$get(data,{
      stopPull:true
    }).then(res=>{
      let responseData = res.data;
      wx.$showToast( responseData.msg);
      that.setData({
        name: '',
        phone: '',
        wechatnum: '',
        isapply: responseData.isapply
      })
      app.globalData.isdistrib = responseData.isdistrib;
      app.globalData.isapply = responseData.isapply;
    }).catch(err=>{
  
    console.log(err)
    })
  }
})