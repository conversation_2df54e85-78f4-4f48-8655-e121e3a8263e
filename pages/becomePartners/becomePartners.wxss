page { background-color: #F4F5F7; }

/* 没有分销提示 */
.nofenxiao-tip { padding: 35% 0; }
.nofenxiao-tip image { height: 140rpx; width: 140rpx; display: block; margin: 0 auto; margin-bottom: 20rpx; }
.nofenxiao-tip text { line-height: 1.6; display: block; width: 60%; margin: 0 auto; text-align: center; font-size: 40rpx; color: #3EB74F; }
.fenxiao-cover image { width: 100%; }
.fenxiao-tip { padding: 10rpx 30rpx; font-size: 28rpx; color: #666; }
.fenxiao-tip text { color: #FF8132; }
.title-name { font-weight: bold; padding: 30rpx 20rpx; text-align: center; }
.curPhone { color: #FF5325; font-size: 36rpx; line-height: 1.5; font-weight: bold; margin-bottom: 60rpx; text-align: center; }
.info-input { padding: 10rpx 30rpx; }
.info-input input { font-size: 28rpx; border-radius: 8rpx; padding: 10rpx 20rpx; height: 86rpx; box-sizing: border-box; background-color: #fff; width: 100%; }
.info-input .get-code { margin-left: 20rpx; height: 84rpx; line-height: 84rpx; text-align: center; border-radius: 8rpx; padding: 0 20rpx; background-color: #FF343F; color: #fff; }
.confirm-btn { height: 86rpx; line-height: 86rpx; width: 91%; margin: 20rpx auto; text-align: center; border-radius: 8rpx; background-color: #FF343F; color: #fff; }
.partners-privilege { padding: 10rpx 60rpx; margin-top: 20rpx; margin-bottom: 30rpx; }
.partners-privilege .label-name { color: #666; }
.privilege-info { border: 1px solid #efefef; border-radius: 10rpx; background-color: #fff; margin: 0 auto; }
.privilege-item { padding: 25rpx; }
.privilege-item image { height: 90rpx; width: 90rpx; display: block; border-radius: 50%; margin-right: 15rpx; }
.privilege-item .privilege-name, .privilege-item .privilege-intro { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 475rpx; display: block; }
.privilege-item .privilege-name { font-size: 30rpx; }
.privilege-item .privilege-intro { font-size: 26rpx; color: #999; }
.privilege-txt { color: #999; line-height: 1.5; font-size: 26rpx; }