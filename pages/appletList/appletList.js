//获取应用实例
const app = getApp();
Page({
  data: {
   
  },
  onLoad: function (e) {
    var that = this;
    that.requestApplet();
  },
  onShow: function () {
    app.setNavtitle('导航');
    app.setCartnum();//更新购物车数量
  },
  requestApplet: function () {
    var that = this;
    //发起请求，获取列表列表
    wx.$get({
      map: 'applet_applet_jump_list'
    },
    {
      stopPull:true
    }).then(res=>{
      that.setData({
        appletInfo: res.data
      })
    }).catch(err=>{
      that.setData({
        appletInfo: ""
      })
    })
  },
  onPullDownRefresh:function(){
    this.requestApplet();
  },
  openApplet: function (e) {
    var path = e.currentTarget.dataset.path;
    var appid = e.currentTarget.dataset.appid;

    wx.navigateToMiniProgram({
      appId: appid,
      path: path,
      envVersion: 'release',
      success(res) {
        // 打开成功
      }
    })
  },
  onShareAppMessage: function () {
    var that = this,
        shareInfo = app.globalData.shareInfo,
        title = shareInfo.shareTitle ? shareInfo.shareTitle : '',
        cover = shareInfo.shareCover ? shareInfo.shareCover : '';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/appletList/appletList'
    }
  }
})
