
<back-home></back-home>
<!--折叠菜单  -->
<fold-menu></fold-menu>
<view class="empty-tip" wx:if="{{appletInfo.length<=0}}">
  <image src="/images/empty_img.png" mode="widthFix"></image>
  <text>暂未添加跳转小程序~</text>
</view>
<view class="applet_list" wx:if="{{appletInfo.list.length>0}}">
   <block wx:key="index" wx:for="{{appletInfo.list}}" wx:for-item="applet" wx:if="{{appletInfo.style==1}}">
  <navigator class="applet_image_item" target="miniProgram" app-id="{{applet.appid}}" path="{{applet.path}}" open-type="navigate">
    <image src="{{applet.background}}" class="applet_bg" mode="aspectFill"></image>
    <view class="applet_logo_name flex-wrap">
      <image src="{{applet.logo}}" class="applet_logo"></image>
      <view class="flex-con">
        <text class="name">{{applet.name}}</text>
        <text class="intro">{{applet.brief}}</text>
      </view>
      <image src="/images/food-jt.png" class="jiantou" mode="aspectFit"></image>
    </view>
  </navigator>
  </block>
   <block wx:key="index" wx:for="{{appletInfo.list}}" wx:for-item="applet" wx:if="{{appletInfo.style==2}}">
  <navigator class="applet_item flex-wrap" target="miniProgram" app-id="{{applet.appid}}" path="{{applet.path}}" open-type="navigate">
    <image src="{{applet.logo}}" class="icon_applet" mode="aspectFill"></image>
    <view class="applet_info border-b flex-con">
      <text class="name">{{applet.name}}</text>
      <text class="intro">{{applet.brief}}</text>
    </view>
    <view class="open_btn">打开</view>
  </navigator>
  </block>
</view>
<!--错误提示-->
 
