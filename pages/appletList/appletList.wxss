page { border: none; }
.applet_list { padding-bottom: 20rpx; }
.applet_item { padding: 0 20rpx; background-color: #fff; position: relative; }
.applet_item .jiaobiao { position: absolute; top: 0; left: 0; width: 60rpx; height: 60rpx; z-index: 1; }
.applet_item .jiaobiao image { width: 100%; height: 100%; display: block; }
.applet_item .jiaobiao text { color: #fff; font-size: 24rpx; position: absolute; left: 0; top: 8rpx; -webkit-transform: rotate(-45deg); transform: rotate(-45deg); }
.applet_item .icon_applet { height: 120rpx; width: 120rpx; border-radius: 30rpx; margin-right: 20rpx; border: 1rpx solid #E7E7E7; display: block; }
.applet_item .applet_info { padding: 30rpx 0; }
.applet_item .applet_info text { line-height: 1.6; display: block; max-width: 500rpx; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; font-size: 32rpx; }
.applet_item .applet_info .intro { font-size: 28rpx; color: #999; margin-top: 15rpx; }
.applet_item:last-child .applet_info.border-b:after { height: 0; }
.applet_item .open_btn { position: absolute; top: 25rpx; right: 25rpx; border: 1px solid #E7E7E7; background-color: #F7F7F7; color: #006DFA; width: 130rpx; text-align: center; height: 60rpx; line-height: 62rpx; border-radius: 32rpx; font-size: 28rpx; }

/* 列表样式2 */
.applet_image_item { position: relative; width: 94%; margin: 0 auto; margin-top: 22rpx;background-color:#fff;box-shadow:4rpx 4rpx 15rpx #e8e8e8;border-radius:8rpx;overflow:hidden;}
.applet_image_item .applet_bg { display: block; width: 100%; height: 350rpx; }
.applet_image_item .applet_logo_name { padding: 20rpx; }
.applet_logo_name .applet_logo { height: 80rpx; width: 80rpx; border-radius: 50%; display: block; margin-right: 20rpx; }
.applet_logo_name .jiantou{width: 20rpx;height: 24rpx;}
.applet_logo_name view text { line-height: 1.4; display: block; max-width: 500rpx; text-overflow: ellipsis; overflow: hidden; white-space: nowrap; font-size: 32rpx; color: #333; }
.applet_logo_name view text.name { margin-top: 5rpx; }
.applet_logo_name view .intro { font-size: 26rpx; font-weight: normal;color: #999; }

/* 内容为空提示 */
.empty-tip { padding: 40% 0; }
.empty-tip image { width: 20%; display: block; margin: 0 auto; }
.empty-tip text { font-size: 28rpx; color: #999; margin-top: 10rpx; text-align: center; display: block; }