// pages/Generalreservation/Generalreservation.js
const rqcfg = require('../../utils/constant.js');
const app = getApp();
Page({
  onLoad: function (e) {
    var that = this;
  },
  onShow: function () {
    var that = this;
    that.loadData(); 
    if(that.data.title){
      that.setData({
        title: that.data.title
      })
    }
    app.setCartnum();//更新购物车数量
  },
  // 客服回复组件间传值
  getreplyData: function (e) {
    console.log(e.detail);
    this.setData(e.detail);
  },
  onPullDownRefresh: function () {
    var that = this;
    that.loadData();
  },
  loadData: function () {
    var that = this;
    if (!that.data.info) {
      wx.showLoading({
        title: '加载中...',
      })
    }
    wx.$get({
        map: 'applet_appointment_index',
    }).then(res => {
      let responseData = res.data;
      that.setData({
        info: responseData,
        title: responseData.template.title
      })
      app.setNavtitle(responseData.template.title);
      app.globalData.mustAddress = responseData.mustAddress;
      app.globalData.mustTime = responseData.mustTime;
    }).catch(err => {
      console.log(err);

    })
  },
  noteableClick: function (e) {
    var id = e.currentTarget.dataset.id;
    if (id == 0) {
      return;
    }
    wx.navigateTo({
      url: '/pages/informationDetail/informationDetail?id=' + id + "&title=" + e.currentTarget.dataset.title,
    })
  },
  cellClick: function (e) {
    var id = e.currentTarget.dataset.id;
    if (!id || id.length == 0) {
      return;
    }
    wx.navigateTo({
      url: '/subpages/Generalreservationdetail/Generalreservationdetail?id=' + id,
    })
  },
  myorderClick: function (e) {
    var title = e.currentTarget.dataset.title; 
    wx.navigateTo({
      url: '/subpages/Generalreservationlist/Generalreservationlist?title='+title,
    })
  },
  makeCall:function(e){
    var mobile = e.currentTarget.dataset.mobile;
    app.makeCall(mobile);
  },
  seemap: function (e) {
    var latitude = e.currentTarget.dataset.lat;
    var longitude = e.currentTarget.dataset.lng;
    var address = e.currentTarget.dataset.address;
    var name = e.currentTarget.dataset.name;
    wx.openLocation({
      latitude: Number(latitude),
      longitude: Number(longitude),
      name: name,
      address: address,
      scale: 18
    })
  },
  //打开更多预约项目
  openMoreProject:function(){
    wx.navigateTo({
      url: '/subpages/GeneralreservationProject/GeneralreservationProject',
    })
  },
  seeDetail: function (e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/informationDetail/informationDetail?id=' + id
    })
  },
  //转发
  onShareAppMessage: function () {
    var that = this;
    var title = that.data.title;
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/Generalreservation/Generalreservation'
    }
  },
  //朋友圈转发
  onShareTimeline(){
    var that = this;
    var title = that.data.title;
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})