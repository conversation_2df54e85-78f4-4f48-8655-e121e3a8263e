
<!-- 客服回复提示弹窗 -->
<kefu-replytip is-showkfreply="{{isShowkfreply}}" reply-tips="{{replyTips}}" customer-mobile="{{customerMobile}}" contact-phone="{{contactPhone}}"></kefu-replytip>
<!-- <back-home></back-home> -->
<nav-bar page-name="{{title}}"></nav-bar>
<view class="content-wrap">
  <view class="banner-wrap" wx:if="{{info.slide.length>0}}">
    <view class="notice-wrap flex-wrap" wx:if="{{info.notice.length!=0}}">
      <image class="icon-notice" src="/images/oppoint/gonggao.png" mode="aspectFit"></image>
      <view class="notice-list flex-con">
        <swiper class="notable-swiper" autoplay="{{true}}" interval="{{3000}}" duration="{{1000}}" circular="{{true}}" vertical="true">
          <block wx:key="index" wx:for="{{info.notice}}" wx:for-item="note">
            <swiper-item>
              <view class="notable-item" data-title="{{note.title}}" data-id="{{note.link}}" bindtap="noteableClick">{{note.title}}</view>
            </swiper-item>
          </block>
        </swiper>
      </view>
    </view>
    <swiper class="slide-swiper" indicator-dots="ture" autoplay="{{true}}" interval="3000" duration="1000" circular="{{true}}" indicator-active-color="#fff" indicator-color="rgba(0,0,0,0)">
       <block wx:key="index" wx:for="{{info.slide}}">
        <swiper-item>
          <image src="{{item.img}}" class="slide-image" mode="aspectFill" data-id="{{item.link}}" bindtap="cellClick" />
        </swiper-item>
      </block>
    </swiper>
  </view>
  <view class="oppoint-info">
    <view class="title-brief border-b" wx:if="{{info.template.appointBrief}}" data-id="{{info.template.appointLink}}" bindtap="seeDetail">
      <view class="title-wrap flex-wrap1">
        <view class="title flex-con">{{info.template.appointTitle}}</view>
        <image class="title-icon" src="/images/oppoint/icon_you.png" mode="aspectFit"></image>
      </view>
      <view class="opponit-brief">
        <text selectable="{{true}}" space="{{true}}" decode="{{true}}">{{info.template.appointBrief}}</text></view>
    </view>
    <view class="time-address border-b">
      <view class="con-item flex-wrap1">
        <view class="icon"><image src="/images/oppoint/icon_yingye.png" mode="aspectFit"></image></view>
        <view class="con flex-con">营业时间：{{info.template.openTime}}</view>
      </view>
      <view class="con-item flex-wrap1" data-name="{{info.template.address}}" data-address="{{info.template.address}}" data-lng="{{info.template.lng}}" data-lat="{{info.template.lat}}" bindtap="seemap">
        <view class="icon"><image src="/images/oppoint/icon_dizhi.png" mode="aspectFit"></image></view>
        <view class="con flex-con">{{info.template.address}}</view>
      </view>
    </view>
    <view class="opera-wrap flex-wrap">
      <view class="opera-item flex-con" data-mobile="{{info.template.mobile}}" bindtap="makeCall">
        <image src="/images/oppoint/icon_dianhua.png" mode="aspectFit"></image>
        <text>电话</text>
      </view> 
      <view class="opera-item flex-con" data-title="{{info.template.orderTitle}}" bindtap="myorderClick">
        <image src="/images/oppoint/icon_dingdan.png" mode="aspectFit"></image>
        <text>{{info.template.orderTitle}}</text>
      </view> 
      <view class="opera-item flex-con">
        <image src="/images/oppoint/icon_fenxiang.png" mode="aspectFit"></image>
        <text>分享</text>
        <button open-type="share"></button>
      </view> 
    </view>
  </view>
  <view class="project-wrap">
    <view class="project-title border-b">
      <image src="/images/oppoint/icon_yuyue.png" mode="aspectFit"></image>
      <text>{{info.template.goodTitle}}</text>
    </view>
    <view class="project-list">
      <block wx:key="index" wx:for="{{info.goods}}" wx:for-item="goods">
        <view class="project-item flex-wrap border-b" data-id="{{goods.id}}" bindtap="cellClick">
          <view class="cover-img">
            <image src="{{goods.cover}}" mode="aspectFill"></image>
          </view>
          <view class="info-wrap flex-con">
            <view class="title">{{goods.name}}</view>
            <view class="price-btn flex-wrap">
              <view class="price flex-con"><block wx:if="{{goods.price}}">￥{{goods.price}}</block><block wx:else>免费</block></view>
              <view class="btn">{{info.template.buttonText ? info.template.buttonText : '立即预约'}}</view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <view class="more-btn" bindtap="openMoreProject">
      <text>查看更多</text>
      <image src="/images/public_arrow_right.png" mode="aspectFit"></image>
    </view>
  </view>
</view>
<!--联系客服-->
<view class="contact-wrap" wx:if="{{customerService==1}}">
  <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
  <image src="/images/icon_kefu.png" class='icon-kf' mode='aspectFit'></image>
</view>
<!--错误提示-->
 
