/* pages/Generalreservation/Generalreservation.wxss */
page { background-color: #f2f2f2; }
.banner-wrap{height:360rpx;position:relative;}
.slide-swiper { width: 100%; height: 360rpx; }
.slide-swiper image { width: 100%; height: 360rpx; display: block; }
.slide-swiper .wx-swiper-dot{border:2rpx solid #fff;}
.slide-swiper .wx-swiper-dots.wx-swiper-dots-horizontal{margin-bottom:20rpx;}
.notice-wrap{background-color:rgba(0,0,0,0.4);padding:8rpx 20rpx;box-sizing:border-box;position:absolute;top:10rpx;left:20rpx;right:20rpx;z-index:1;border-radius:6rpx;}
.notice-wrap .icon-notice{display: block;width:32rpx;height:32rpx;margin-right:12rpx;}
.notice-wrap .notable-swiper{height:40rpx;}
.notice-wrap .notable-swiper .notable-item{height: 100%; width: 100%; line-height: 40rpx;overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-size: 28rpx; color: #fff;}

.oppoint-info{
  padding:50rpx 25rpx 36rpx;
  border-radius:20rpx;
  margin-top:-20rpx;
  position:relative;
  z-index:1;
  background-color:#fff;
}
.oppoint-info .title-brief{
  padding-bottom:25rpx;
}
.oppoint-info .title-wrap .title{
  color:#333;
  font-size:36rpx;
  font-weight: 700;
  margin-right:30rpx;
}
.oppoint-info .title-wrap .title-icon{
  display: block;
  width:28rpx;
  height:28rpx;
  margin-top:14rpx;
}
.oppoint-info .opponit-brief{
  color:#999;
  font-size:26rpx;
  margin-top:16rpx;
}
.oppoint-info .time-address{
  padding-bottom:20rpx;
}
.oppoint-info .time-address .con-item{
  padding-top:25rpx;
}
.oppoint-info .time-address .con-item .icon{
  width:30rpx;
  height:30rpx;
  margin:10rpx 15rpx 0 0;
}
.oppoint-info .time-address .con-item .icon image{
  display: block;
  width:100%;
  height:100%;
}
.oppoint-info .time-address .con-item .con{
  color:#666;
  font-size:30rpx;
}
.oppoint-info .opera-wrap{
  padding-top:20rpx;
}
.oppoint-info .opera-wrap .opera-item{
  text-align: center;
  font-size:0;
  position:relative;
}
.oppoint-info .opera-wrap .opera-item button{
  position: absolute; 
  left: 0; 
  top: 0; 
  width: 100%; 
  height: 100%; 
  opacity: 0; 
  z-index: 1; 
  margin: 0;
}
.oppoint-info .opera-wrap .opera-item image{
  width:32rpx;
  height:32rpx;
  vertical-align: middle;
  margin-right:10rpx;
}
.oppoint-info .opera-wrap .opera-item text{
  color:#666;
  font-size:28rpx;
  vertical-align: middle;
}
.project-wrap{
  padding:0 25rpx;
  background-color:#fff;
  margin-top:16rpx;
  border-radius:20rpx;
}
.project-wrap .project-title{
  padding:36rpx 0 25rpx;
}
.project-wrap .project-title image{
  width:30rpx;
  height:30rpx;
  vertical-align: middle;
  margin-right:10rpx;
}
.project-wrap .project-title text{
  color:#333;
  font-size:30rpx;
  vertical-align: middle;
}
.project-wrap .project-item{
  padding:30rpx 0;
}
.project-wrap .project-item .cover-img{
  width:180rpx;
  height:180rpx;
  border-radius:10rpx;
  margin-right:24rpx;
  overflow: hidden;
}
.project-wrap .project-item .cover-img image{
  display: block;
  width:100%;
  height:100%;
}
.project-wrap .project-item .title{
  color:#333;
  font-size:34rpx;
  font-weight: 700;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-bottom:56rpx;
}
.project-wrap .project-item .price{
  color:#F02221;
  font-size:34rpx;
}
.project-wrap .project-item .price-btn .btn{
  background-color:#18D198;
  width:165rpx;
  height:70rpx;
  line-height:70rpx;
  text-align:center;
  color:#fff;
  font-size:28rpx;
  border-radius:10rpx;
}
.more-btn { text-align: center; font-size: 30rpx; color: #666; padding: 30rpx 0; }
.more-btn text { vertical-align: middle; }
.more-btn image { display: inline-block; width: 13rpx; height: 23rpx; vertical-align: middle; margin-left: 12rpx; position: relative; top:0; }