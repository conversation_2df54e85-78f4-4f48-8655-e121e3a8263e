
<!--折叠菜单  -->
<fold-menu></fold-menu>
<view class="evalute-wrap">
  <view class="good-evalute">
     <block wx:key="index" wx:for="{{orderinfo.goods}}" wx:for-item="good">
      <view class="good-eval-item">
        <view class="good-info flex-wrap">
          <image src="{{good.img}}" mode="aspectFill"></image>
          <view class="good-intro flex-con">
            <text class="good-name">{{good.title}}</text>
            <text class="price">￥{{good.price}}</text>
          </view>
        </view>
        <view class="good-score flex-wrap">
          <text>评分</text>
          <view class="score flex-con">
            <image src="{{good.score>=1?scoredUrl:noscoreUrl}}" mode="aspectFit" data-gid="{{good.gid}}" data-score="1" bindtap="goodScore" ></image>
            <image src="{{good.score>=2?scoredUrl:noscoreUrl}}" mode="aspectFit" data-gid="{{good.gid}}" data-score="2" bindtap="goodScore" ></image>
            <image src="{{good.score>=3?scoredUrl:noscoreUrl}}" mode="aspectFit" data-gid="{{good.gid}}" data-score="3" bindtap="goodScore" ></image>
            <image src="{{good.score>=4?scoredUrl:noscoreUrl}}" mode="aspectFit" data-gid="{{good.gid}}" data-score="4" bindtap="goodScore" ></image>
            <image src="{{good.score>=5?scoredUrl:noscoreUrl}}" mode="aspectFit" data-gid="{{good.gid}}" data-score="5" bindtap="goodScore" ></image>
          </view>
        </view>
        <view class="eval-con">
          <textarea auto-height placeholder="宝贝不错，很喜欢" value="{{good.commentTxt}}" data-gid="{{good.gid}}" bindinput="commentChange"/>
        </view>
        <view class="upload-box clearfix">
           <block wx:key="index" wx:for="{{good.picsShow}}" wx:for-item="evalimg">
            <view class="img-item">
              <image src="{{evalimg}}" mode="aspectFill" data-index="{{index+1}}" data-src="{{evalimg}}" data-gid="{{good.gid}}" data-curimg="{{evalimg}}" data-imgs="{{good.picsShow}}" catchtap="peiviewImg"></image>
              <text class="del-img" data-index="{{index}}" data-gid="{{good.gid}}" bindtap="delUploadImg">×</text>
            </view>
          </block>
          <!-- bindtap="chooseImage" -->
          
            <view  bindtap="startChooseImg"  class="img-item add-item" hidden="{{good.picsShow.length>=5}}" data-gid="{{good.gid}}" data-index="{{index}}">
            <image src="../../images/icon_camera.png"></image>
          </view>
          
        </view>
      </view>
    </block>
  </view>
  <view class="shop-evalute">
    <text class="title-name">给店铺评分</text>
    <view class="shop-score-item flex-wrap">
      <text>发货速度</text>
      <view class="score flex-con">
        <image src="{{shopFhScore>=1?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="1" bindtap="shopScore" data-type="Fh"></image>
        <image src="{{shopFhScore>=2?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="2" bindtap="shopScore" data-type="Fh"></image>
        <image src="{{shopFhScore>=3?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="3" bindtap="shopScore" data-type="Fh"></image>
        <image src="{{shopFhScore>=4?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="4" bindtap="shopScore" data-type="Fh"></image>
        <image src="{{shopFhScore>=5?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="5" bindtap="shopScore" data-type="Fh"></image>
      </view>
    </view>
    <view class="shop-score-item flex-wrap">
      <text>服务态度</text>
      <view class="score flex-con">
        <image src="{{shopFwScore>=1?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="1" bindtap="shopScore" data-type="Fw"></image>
        <image src="{{shopFwScore>=2?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="2" bindtap="shopScore" data-type="Fw"></image>
        <image src="{{shopFwScore>=3?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="3" bindtap="shopScore" data-type="Fw"></image>
        <image src="{{shopFwScore>=4?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="4" bindtap="shopScore" data-type="Fw"></image>
        <image src="{{shopFwScore>=5?scoredUrl:noscoreUrl}}" mode="aspectFit" data-score="5" bindtap="shopScore" data-type="Fw"></image>
      </view>
    </view>
  </view>
</view>
<view class="bottom-zhanwei">
  <view class="evalute-btn" bindtap="saveEvaluate">
    发表评论
  </view>
</view>


<!--弹出层-->
<view class="modal-mask" wx:if="{{isShowModal}}" bindtap="hideModal"></view>
<view class="modal-content" wx:if="{{isShowModal}}" bindtap="hideModal">
  <view class="evalute-modal-wrap">
    <view class="cur-index">{{bigImgShow.curIndex}}/{{bigImgShow.sumLength}}</view>
    <image src="{{bigImgShow.src}}" mode="aspectFit"></image>
  </view>
</view>

<!--错误提示-->
 
<upload-Image class="upload-img" max-length="{{5-orderinfo.goods[uploadIndex].picsShow.length}}" bind:uploadSuccess="uploadComplete"></upload-Image>