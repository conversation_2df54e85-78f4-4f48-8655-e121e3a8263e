//获取应用实例
const app = getApp();
const rqcfg = require('../../utils/constant.js');
Page({
  data: {
    
  },
  onLoad: function (e) {
    var that = this;
    if(e){
      that.setData({
        queryData:e
      })
    }
  
      that.setData({
        showComponent:true
      })
    
  },
  onShow: function () {
    var that = this;
  },
  onPullDownRefresh: function () {
    var that = this;
    that.setData({
      isRefresh:!that.data.isRefresh
    })
  },
  onReachBottom: function () {
    var that = this;
    that.setData({
      isReachbottom:!that.data.isReachbottom
    })
  },
  onShareAppMessage: function (e) {
    wx.showShareMenu({
      withShareTicket: true
    })
    var that = this,
        shareInfo = app.globalData.shareInfo||{},
        title = shareInfo.shareTitle || '商品分类',
        cover = shareInfo.shareCover || '';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/allFlGoodsPage/allFlGoodsPage'
    }
  },//朋友圈转发
  onShareTimeline(){
    var that = this;
    var title = that.data.title?that.data.title:'';
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})