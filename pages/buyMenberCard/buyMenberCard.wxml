
<!--pages/buyMenberCard/buyMenberCard.wxml-->
<view class="main-content" wx:if="{{cardList.length>0}}">
  <view class="user-btn flex-wrap">
    <image class="avatar" src="{{userInfo.avatar}}" mode="aspectFill"></image>
    <view class="user-info flex-con">
      <view class="nickname">{{userInfo.nickname}}</view>
      <view class="buy-card" wx:if="{{discount}}">
        <image src="/images/memberCard/icon_zhekou.png" mode="aspectFit"></image>
        <text>{{discount}}</text>
      </view>
    </view>
    <view class="record-btn bg-color" bindtap="openMemberRecord">
      <text>购买记录</text>
      <image src="/images/memberCard/icon_jilu.png" mode="aspectFit"></image>
    </view>
  </view>
  <view class="card-banner">
    <swiper indicator-dots="{{false}}" interval="3000" circular duration="800" indicator-active-color="#f7c463" indicator-color="#ebebeb" current="{{current}}" bindchange="getCurrent" previous-margin="40rpx" next-margin="40rpx" easing-function="linear">
      <block wx:key="index" wx:for="{{cardList}}" wx:for-item="card" >
        <swiper-item class="flex-wrap">
          <view class="card-item {{card.hadExpire?'active':''}} {{current==index?'curHeight':''}}">
            <image class="bg fade_in" src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/memberCard/image_hyk.png" mode="aspectFill"></image>
            <view class="card-info">
              <view class="title-rights flex-wrap">
                <view class="name flex-con">{{card.name}}</view>
                <view class="rights-btn" wx:if="{{!card.hadBuy||(card.hadBuy&&card.hadExpire)}}" bindtap="openCardIntroduce">
                  <image src="/images/memberCard/icon_hyqy.png" mode="aspectFit"></image>
                  <text>会员权益</text>
                </view>
              </view>
              <view class="card-desc">
                <view class="desc-item">{{card.levelDiscountShow?card.levelDiscountShow:'无折扣'}}</view>
                <view class="desc-item" wx:if="{{card.returnPrice>0}}">获得{{card.returnPrice}}元</view>
              </view>
              <view class="days-money flex-wrap">
                <view class="days flex-con" wx:if="{{!card.hadBuy}}">{{card.longShow}}</view>
                <view class="days flex-con" wx:if="{{card.hadBuy&&!card.hadExpire}}">到期时间：{{card.expireTime}}</view>
                <view class="days flex-con" wx:if="{{card.hadExpire}}">已到期</view>
                <view class="money"><text>￥</text>{{card.price}}</view>
              </view>
            </view>
            <image class="bottom-icon" wx:if="{{current==index}}" src="/images/memberCard/image_hy.png" mode="aspectFit"></image>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <block  wx:if="{{!currentCard.hadBuy||(currentCard.hadBuy&&currentCard.hadExpire)}}">
    <view class="infor-wrap">
      <view class="title">完善信息</view>
      <view class="input-wrap">
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_wode.png" mode="aspectFit"></image>
          <view class="desc flex-con">姓名</view>
          <view class="input-box">
            <input type="text" placeholder="请输入姓名" bindinput="nameChange" value="{{name?name:''}}"></input>
          </view>
        </view>
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_xingbie.png" mode="aspectFit"></image>
          <view class="desc flex-con">性别</view>
          <view class="input-box">
            <text class="sex {{sexValue=='1'?'active':''}}" bindtap="sexSelect" data-sex="1">男</text>
            <text class="sex {{sexValue=='0'?'active':''}}" bindtap="sexSelect" data-sex="0">女</text>
          </view>
        </view>
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_shengri.png" mode="aspectFit"></image>
          <view class="desc flex-con">生日</view>
          <view class="input-box">
            <picker mode="date" value="{{date}}" start="1900-01-01" bindchange="bindDateChange">
              <view class="picker">
                {{date?date:'请选择生日'}}
              </view>
            </picker>
          </view>
        </view>
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_dianhua.png" mode="aspectFit"></image>
          <view class="desc flex-con">电话</view>
          <view class="input-box">
            <input type="number" value="{{telphone?telphone:''}}" placeholder="请输入电话" maxlength="11" bindinput="phoneChange"></input>
          </view>
        </view>
      </view>
    </view>
    <view class="infor-wrap" wx:if="{{currentCard.price>0}}">
      <view class="title">支付方式</view>
      <view class="input-wrap">
        <view class="input-item" style="padding:0">
          <view class="input-box">
          <radio-group class="radio-group" bindchange="radioChange">
              <view class="radio-box flex-sp">
                <label for="wxpay">微信支付</label>
                <radio color="#ffd36b" value="1" checked="true" name="pay" id="wxpay"/>
              </view>
              <view class="radio-box flex-sp">
                <view>
                  <label for="yepay">余额支付</label>
                  <view class="yue-price" style="color:#fed67c;">({{coin}}元)</view>
                </view>
                <radio disabled="{{coin<currentCard.price*1?true:false}}" color="#ffd36b" value="2" name="pay" id="yepay"/>     
              </view>
          </radio-group>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-zhanwei">
      <view class="bottom-fixed">
        <view class="submit-btn bg-color" bindtap="submitInfor" wx:if="{{currentCard.type==1}}">{{currentCard.price>0?'立即购买':'立即领取'}}</view>
        <view class="submit-btn bg-color" bindtap="submitInfor" wx:if="{{currentCard.type==3}}">提升等级</view>
        <view class="submit-btn bg-color" bindtap="submitInfor"  wx:if="{{currentCard.hadExpire&&currentCard.type==2}}">立即续费</view>
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="infor-wrap">
      <view class="title">完善信息</view>
      <view class="input-wrap">
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_wode.png" mode="aspectFit"></image>
          <view class="desc flex-con">姓名</view>
          <view class="input-box">
            {{name?name:''}}
          </view>
        </view>
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_xingbie.png" mode="aspectFit"></image>
          <view class="desc flex-con">性别</view>
          <view class="input-box">
            {{sexValue=='1'?'男':'女'}}
          </view>
        </view>
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_shengri.png" mode="aspectFit"></image>
          <view class="desc flex-con">生日</view>
          <view class="input-box">
            {{date?date:''}}
            <!-- <picker mode="date" value="{{date}}" start="1900-01-01" bindchange="bindDateChange">
              <view class="picker">
                {{date?date:'请选择生日'}}
              </view>
            </picker> -->
          </view>
        </view>
        <view class="input-item flex-wrap">
          <image class="icon" src="/images/memberCard/icon_dianhua.png" mode="aspectFit"></image>
          <view class="desc flex-con">电话</view>
          <view class="input-box">
            {{telphone?telphone:''}}
          </view>
        </view>
      </view>
    </view>
    <view class="rights-content">
      <view class="rights-item">
        <view class="had-rights-tit flex-wrap" bindtap="toggleHadCardRight" >
          <image class="left-icon" src="/images/memberCard/icon_quanyi.png"></image>
          <view class="flex-con">会员卡权益</view>
          <image class="pull-icon" src="/images/memberCard/icon_zhankai.png" wx:if="{{hadCardRight}}"></image>
          <image class="pull-icon" src="/images/memberCard/icon_xiala.png" wx:if="{{!hadCardRight}}"></image>
        </view>
        <text decode="true" class="had-rights-content" wx:if="{{hadCardRight}}">
          {{cardList[current].rights}}
        </text>
      </view>
      <view class="rights-item">
        <view class="had-rights-tit flex-wrap"  bindtap="toggleHadCardNotice">
          <image class="left-icon" src="/images/memberCard/icon_shuoming.png"></image>
          <view class="flex-con">会员卡需知</view>
          <image class="pull-icon" src="/images/memberCard/icon_zhankai.png" wx:if="{{hadCardNotice}}"></image>
          <image class="pull-icon" src="/images/memberCard/icon_xiala.png" wx:if="{{!hadCardNotice}}"></image>
        </view>
        <text decode="true" space="true" class="had-rights-content" wx:if="{{hadCardNotice}}">
          {{cardList[current].notice}}
        </text>
      </view>
    </view>
  </block>
  <!-- 会员卡信息弹窗 wx:if="{{showCardIntroduce}}"-->
  <view class='share-modal-mask' bindtap='closeCardIntroduce' wx:if="{{showCardIntroduce}}">
    <view class="introduce-wrap">
      <view class="introduce-scroll">
        <image class="scroll-icon scroll-right" src="/images/memberCard/image_zs_s.png" mode="aspectFit"></image>
        <image class="scroll-icon scroll-left" src="/images/memberCard/image_zs_x.png" mode="aspectFit"></image>
        <scroll-view class='card-introduce' scroll-y>
          <view class="member-rights">
            <view class="rights-con">
              <view class="commom-title">
                <view>
                  <image src="/images/memberCard/image_bt_z.png" mode="aspectFit"></image>
                  <text>会员卡权益</text>
                  <image src="/images/memberCard/image_bt_y.png" mode="aspectFit"></image>
                </view>
                <view class="title-bottom">
                  <image src="/images/memberCard/image_bt_x.png" mode="aspectFit"></image>
                </view>
              </view>
              <view class="con-wrap">
                <text space="true" decode="true" class="con">{{cardList[current].rights}}</text>
              </view>
            </view>
          </view>
          <view class="card-explain">
            <view class="commom-title">
              <view>
                <image src="/images/memberCard/image_bt_z.png" mode="aspectFit"></image>
                <text>会员卡需知</text>
                <image src="/images/memberCard/image_bt_y.png" mode="aspectFit"></image>
              </view>
              <view class="title-bottom">
                <image src="/images/memberCard/image_bt_x.png" mode="aspectFit"></image>
              </view>
            </view>
            <view class="con-wrap">
              <text space="true" decode="true" class="con">{{cardList[current].notice}}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      <image class="close-btn" src="/images/memberCard/icon_quxiao.png" mode="aspectFit" bindtap='closeCardIntroduce'></image>
    </view>
  </view>
</view>
<!-- 状态提示 -->
<view class="no-data" style="padding-top:300rpx;" wx:if="{{cardList.length<=0}}">
  <image src="/images/cardimg/empty.png" mode="aspectFit"></image>
  <text>暂无可用的会员卡</text>
</view>
<!--错误提示-->
 