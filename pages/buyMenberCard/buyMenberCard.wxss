/* pages/buyMenberCard/buyMenberCard.wxss */
page{border:none;height: 100%;}

.main-content{
  height:100vh;
  overflow-y: auto;
  background-color:#24242C;
  padding-bottom:20rpx;
}
.bg-color{
  background: linear-gradient(#fff2b0,#fed67c);
}
.main-content .user-btn{
  padding:30rpx 25rpx 4rpx;
} 
.main-content .user-btn .avatar{
  display: block;
  width:90rpx;
  height:90rpx;
  border-radius:50%;
  border:4rpx solid #fff;
}
.main-content .user-btn .user-info{
  padding:0 20rpx;
}
.main-content .user-btn .nickname{
  color:#fff;
  font-size:30rpx;
}
.main-content .user-btn .buy-card{
  margin-top:12rpx;
}
.main-content .user-btn .buy-card image{
  width:26rpx;
  height:26rpx;
  vertical-align: middle;
  margin-right:10rpx;
}
.main-content .user-btn .buy-card text{
  color:rgba(255,255,255,0.8);
  font-size:26rpx;
  vertical-align: middle;
}
.main-content .user-btn .record-btn{
  padding:14rpx;
  border-radius:30rpx;
  color:#333;
  font-size:0;
}
.main-content .user-btn .record-btn text{
  vertical-align: middle;
  font-size:22rpx;
}
.main-content .user-btn .record-btn image{
  width:18rpx;
  height:18rpx;
  vertical-align: middle;
  margin-left:4rpx;
}
.card-banner swiper{
  /* width:630rpx; */
  height:400rpx;
  margin: 0 auto;
}
.card-banner swiper .wx-swiper-dots.wx-swiper-dots-horizontal{
  margin-bottom: 0rpx;
}
.card-banner swiper .wx-swiper-dot{
  height: 8rpx;
  width: 20rpx;
  border-radius: 0 !important; 
  margin: 0 6rpx;
  position: relative;
  top:20rpx;
}
.card-banner swiper-item{
  width:630rpx;
}
.card-banner .card-item{
  border-radius: 30rpx;
  width:630rpx;
  height:310rpx;
  background-color:#fff;
  position:relative;
  color:#fff;
  z-index: 1;
  margin: 0 auto;
  transition: all 0.5s;
}
.card-banner .card-item .bottom-icon{
  display: block;
  width:594rpx;
  height:24rpx;
  position:absolute;
  bottom:-22rpx;
  left:18rpx;
}
.card-banner .card-item .bg{
  width:100%;
  height:100%;
  display: block;
  border-radius:30rpx;
}
.card-banner .card-item .card-info{
  position:absolute;
  top:0;
  right:0;
  left:0;
  bottom: 0;
  z-index:1;
  padding:30rpx;
  border-radius:30rpx;
}
.card-banner .card-item.curHeight{
  height:340rpx;
}
.card-banner .card-info .title-rights .name{
  color:#333;
  font-size:30rpx;
  font-weight: 700;
}
.card-banner .card-info .title-rights .rights-btn image{
  width:30rpx;
  height:30rpx;
  margin-right:10rpx;
  vertical-align: middle;
}
.card-banner .card-info .title-rights .rights-btn text{
  color:#333;
  font-size:26rpx;
  vertical-align: middle;
} 
.card-banner .card-info .card-desc{
  margin-top:10%;
}
.card-banner .card-info .card-desc .desc-item{
  color:#333;
  font-size:28rpx;
  padding-left:20rpx;
  position:relative;
  margin-bottom:10rpx;
}
.card-banner .card-info .card-desc .desc-item:before{
  content:'';
  width:10rpx;
  height:10rpx;
  background-color:#fff;
  border-radius:50%;
  position:absolute;
  top:50%;
  left:0;
  margin-top:-5rpx;
}
.card-banner .card-info .days-money{
  position:absolute;
  left:30rpx;
  right:30rpx;
  bottom:30rpx;
}
.card-banner .card-info .days-money .days{
  color:#333;
  font-size:26rpx;
}
.card-banner .card-info .days-money .days text{
  color:#CA8F0D;
  padding:0 4rpx;
}
.card-banner .card-info .days-money .money{
  padding:10rpx 25rpx;
  border-radius:30rpx;
  background-color:#EBC174;
  color:#fff;
  font-size:28rpx;
}
.card-banner .card-info .days-money .money text{
  font-size:24rpx;
}
.infor-wrap{
  background-color:#2C2C34;
  margin:32rpx 25rpx 0;
  padding:0 30rpx;
  border-radius:30rpx;
}
.infor-wrap .title{
  font-size:32rpx;
  color:#fff;
  font-weight: 700;
  padding:26rpx 0 10rpx;
}
.infor-wrap .input-item{
  padding:26rpx 0 ;
  font-size: 28rpx;
  box-sizing:border-box;
  border-bottom:1rpx solid rgba(255,255,255,0.2);
  letter-spacing: 1rpx;
}
.infor-wrap .input-item:last-child{
  border-bottom:none;
}
.infor-wrap .input-item .icon{
  display: block;
  width:34rpx;
  height:34rpx;
  margin-right:16rpx;
}
.infor-wrap .input-item .desc{
  font-size:30rpx;
  color:#fff;
}
.infor-wrap .input-item .input-box{
  color:#fff;
}
.infor-wrap .input-item .sex{
  display:inline-block;
  text-align: center;
  width:100rpx;
  height:60rpx;
  line-height:60rpx;
  margin-left:10rpx;
  color:#fff;
  font-size:28rpx;
}
.infor-wrap .input-item .sex.active{
  background: linear-gradient(#fff2b0,#fed67c);
  color:#333;
  border-radius:30rpx;
}
.infor-wrap .input-item input{
  display: block;
  width:200rpx;
  color:#fff;
  text-align: right;
  padding:0;
  font-size:28rpx;
}
.infor-wrap .input-item .radio-box{
  /* margin-bottom:20rpx; */
  font-size:28rpx;
  height: 100rpx;
  border-bottom:1rpx solid rgba(255,255,255,0.2);

}
.infor-wrap .input-item .radio-box label{
  color:#fff;
}
.infor-wrap .input-item .radio-box:last-child{
  border-bottom: none;
}
.infor-wrap .input-item picker{
  width:200rpx;
  text-align:right;
  color:#fff;
  font-size:28rpx;
}
.infor-wrap .input-item picker .picker{
  width:100%;
}
.bottom-zhanwei{
  padding-bottom:160rpx;
}
.bottom-fixed{
  position:fixed;
  left:0;
  right:0;
  bottom:0;
  z-index:2;
  background-color:#24242C;
  padding:30rpx 0;
}
.submit-btn{
  margin:0 30rpx;
  height:100rpx;
  line-height:100rpx;
  border-radius:50rpx;
  text-align: center;
  font-size:32rpx;
  letter-spacing: 1rpx;
  color:#333;
  box-shadow:0 8rpx 0 #FFB031;
}
.rights-content{
  background-color:#2C2C34;
  margin:32rpx 25rpx 0;
  padding:0 30rpx;
  border-radius:30rpx;
}
.rights-content .rights-item{
  padding-bottom:30rpx;
  border-bottom:1rpx solid rgba(255,255,255,0.2);
}
.rights-content .rights-item:last-child{
  border:none;
}
/* 已开通会员卡权益展示 */
.had-rights-tit{
  /* width: 710rpx; */
  margin: 0 auto;
  padding:30rpx 0rpx 0;
  font-size: 30rpx;
  color: #fff;
  font-weight: 600;

}
.had-rights-tit .left-icon{
  margin-right: 20rpx;
  width: 42rpx;
  height: 42rpx;
}
.had-rights-tit  .pull-icon{
  width: 32rpx;
  height: 32rpx;
}
.had-rights-content{
  display: block;
  padding: 0 62rpx;
  font-size: 28rpx;
  color:rgba(255,255,255,0.8);
}
/* 会员信息权益 */
.share-modal-mask{
  z-index:100;
}
.introduce-wrap{
  position: fixed;
  left: 75rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 600rpx;
  border-radius: 20rpx;
  z-index:101;
}
.introduce-wrap .introduce-scroll{
  position:relative;
  padding:40rpx 0;
  background-color: #24242D;
  border-radius: 20rpx;
  box-shadow: 0 0 10rpx #666139;
}
.introduce-wrap .introduce-scroll .scroll-icon{
  display:block;
  position:absolute;
}
.introduce-wrap .introduce-scroll .scroll-right{
  width:118rpx;
  height:90rpx;
  top:-35rpx;
  right:-30rpx;
}
.introduce-wrap .introduce-scroll .scroll-left{
  width:96rpx;
  height:42rpx;
  bottom:-15rpx;
  left:-20rpx;
}
.card-introduce{
  /* background-color: #24242D; */
  height:700rpx;
  /* border-radius: 20rpx; */
  /* box-shadow: 0 0 10rpx #666139; */
}
.introduce-wrap .close-btn{
  display: block;
  width:60rpx;
  height:60rpx;
  margin:30rpx auto 0;
}
.member-rights{
  padding:20rpx 40rpx;
  margin-bottom: 20rpx;
}
.member-rights .record{
  font-size:28rpx;
  color:#D3AA5E;
  text-align: right;
}
.commom-title{
  text-align:center;
  font-size:32rpx;
  letter-spacing: 1rpx;
  color:#FFF3B4;
  /* padding:20rpx 0; */
  box-sizing:border-box;
  font-weight: bold;
}
.commom-title text{
  vertical-align: middle;
  margin:0 10rpx;
}
.commom-title image{
  width:140rpx;
  height:15.7rpx;
  vertical-align: middle;
}
.commom-title .title-bottom image{
  display: block;
  width:178rpx;
  height:20rpx;
  margin:20rpx auto 0;
}
.introduce-wrap .con-wrap{
  background-color:#2C2B33;
  padding:16rpx;
  border-radius:20rpx;
  margin-top:20rpx;
}
.introduce-wrap .con-wrap .con{
  font-size:28rpx;
  color:#FFF3B4;
  letter-spacing: 1rpx;
  display: block;
  min-height: 140rpx;
}
.card-explain{
  padding: 0 40rpx 20rpx;
}