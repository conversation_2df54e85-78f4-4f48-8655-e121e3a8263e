// pages/buyMenberCard/buyMenberCard.js
const app = getApp();
Page({
    data: {
        current: 0,
        showCardIntroduce: false, //显示会员卡权益和使用说明
        currentCard: {},
        payType: '1',
        hadCardRight: true,
        hadCardNotice: true,
    },
    onLoad: function (e) {
        var that = this;

        wx.getSystemInfo({
            success: function (res) {
                var x = res.windowWidth - 80;
                var y = res.windowHeight - 70;
                that.setData({
                    x: x,
                    y: y,
                    showBtn: true
                })
            },
        })
    },
    onShow: function () {
        var that = this;
        app.setNavtitle('会员卡');

        that.requestCardList();
        that.getCurrentDate();
        that.getUserInfor();

    },
    requestCardList: function (cid) {

        var that = this;
        var data = {};
        data.map = 'applet_member_card_list';
        data.type = 2;


        wx.$get(data).then(res => {
            let responseData = res.data;
            that.setData({
                cardList: responseData,
                currentCard: responseData[that.data.current]
            })
        }).catch(err => {
            that.setData({
                cardList: []
            })
            console.log(err)
        })
    },
    toggleHadCardRight() {
        this.setData({
            hadCardRight: !this.data.hadCardRight
        })
    },
    toggleHadCardNotice() {
        this.setData({
            hadCardNotice: !this.data.hadCardNotice
        })
    },
    //获得当前的日期
    getCurrentDate: function () {
        var that = this;
        myDate = new Date(), //获取当前年
            year = myDate.getFullYear(), //获取当前月
            month = myDate.getMonth() + 1, //获取当前日
            day = myDate.getDate(),
            str = year + '-' + month + '-' + day;
        that.setData({
            date: str
        });
    },
    //获得所填信息
    getUserInfor: function () {
        var that = this;
        wx.$get({
            map: 'applet_store_member_info',
        }).then(res => {
            let responseData = res.data;
            that.setData({
                name: responseData.name,
                sexValue: responseData.gender ? responseData.gender : '1',
                date: responseData.birthday,
                telphone: responseData.telphone,
                coin: responseData.coin
            })
        }).catch(err => {

            console.log(err)
        })
    },
    //获得生日
    bindDateChange: function (e) {

        this.setData({
            date: e.detail.value
        })
    },
    //获得支付方式
    radioChange: function (e) {
        var that = this;
        that.setData({
            payType: e.detail.value
        })
    },
    sexSelect: function (e) {
        var that = this;
        var sex = e.currentTarget.dataset.sex;
        that.setData({
            sexValue: sex
        })
    },
    nameChange: function (e) {
        var that = this;
        that.setData({
            name: e.detail.value
        })
    },
    phoneChange: function (e) {
        var that = this;
        that.setData({
            telphone: e.detail.value
        })
    },
    submitInfor: function () {
        var that = this;
        app.getSubId('applet_buy_member_card').then(res => {
            that.toSubmitInfor()
        })
    },
    toSubmitInfor: function () {
        var that = this;
        if (!that.data.currentCard.levelCanBuy) {
            wx.$showToast("不能降级购买会员卡");
            return
        }
        var data = {},
            id = that.data.currentCard.id,
            type = that.data.currentCard.type,
            name = that.data.name,
            gender = that.data.sexValue,
            birthday = that.data.date,
            telphone = that.data.telphone,
            payType = that.data.payType;
        data.map = 'applet_buy_member_card';
        data.cardid = id;
        data.type = type;
        data.name = name;
        data.gender = gender;
        data.birthday = birthday;
        data.telphone = telphone;
        data.payType = payType;
        data.scene = app.globalData.enterScene;
        if (!data.telphone) {
            wx.$showToast("请输入手机号码");
            return;
        }
        wx.$showModal({
            title: '提示',
            content: '确认立即支付吗？',
        }).then(obj => {
            if (data.payType == '2') {
                wx.showModal({
                    content: '确认使用余额支付订单',
                    complete: (res) => {
                        if (res.confirm) {
                            wx.$get(data).then(res => {
                                let responseData = res.data;
                                console.log('支付', responseData);
                                if (responseData.status == 'dzf') {
                                    that.orderPay(responseData.tid);
                                } else if (responseData.status == 'zfcg') {
                                    wx.showToast({
                                        icon: 'success',
                                        title: '支付成功',
                                        success() {
                                            setTimeout(function () {
                                                wx.navigateBack({
                                                    delta: 1
                                                })
                                            }, 1500)
                                        }
                                    })

                                }
                            }).catch(err => {

                                console.log(err)
                            })
                        }
                    }
                })
            } else {
                wx.$get(data).then(res => {
                    let responseData = res.data;
                    console.log('支付', responseData);
                    if (responseData.status == 'dzf') {
                        that.orderPay(responseData.tid);
                    } else if (responseData.status == 'zfcg') {
                        wx.showToast({
                            icon: 'success',
                            title: '支付成功',
                            success() {
                                setTimeout(function () {
                                    wx.navigateBack({
                                        delta: 1
                                    })
                                }, 1500)
                            }
                        })

                    }
                }).catch(err => {

                    console.log(err)
                })
            }
        })

    },
    orderPay: function (tid) {
        var that = this;
        var data = {};
        data.map = 'applet_pay_member_card';
        data.tid = tid;
        //发起请求，获取列表列表
        wx.$get(data).then(res => {
            var params = res.data;
            app.commonRequestPayment(params)
                .then(res => {
                    //支付成功之后的操作处理
                    wx.showToast({
                        icon: 'success',
                        title: '支付成功',
                        success() {
                            setTimeout(function () {
                                wx.navigateBack({
                                    delta: 1
                                })
                            }, 1500)
                        },
                    })
                }, err => {
                    console.log(err);
                    wx.showToast({
                        icon: 'none',
                        title: '支付失败',
                    })
                })
        }).catch(err => {

            console.log(err)
        })
    },
    openMemberRecord: function () {
        var that = this;
        wx.navigateTo({
            url: '/subpages/memberRecord/memberRecord',
        })
    },
    // 会员卡介绍
    openCardIntroduce() {
        this.setData({
            showCardIntroduce: true
        })
    },
    closeCardIntroduce() {
        this.setData({
            showCardIntroduce: false
        })
    },
    //得到当前swiper的current值
    getCurrent: function (e) {
        var that = this;
        console.log(e.detail);
        that.setData({
            current: e.detail.current,
            currentCard: this.data.cardList[e.detail.current]
        });
    },
    changeCurrent: function (e) {
        var that = this,
            type = e.currentTarget.dataset.type,
            current = that.data.current;
        if (type == 'prev') {
            if (current > 0) {
                current--;
            }
        } else if (type == 'next') {
            if (current < that.data.cardList.length - 1) {
                current++;
            }
        }
        that.setData({
            current: current,
            currentCard: this.data.cardList[e.detail.current]
        })
    },
    prevCard: function () {
        var that = this,
            current = that.data.current;
        if (current == 0) {
            current = 1
        } else {
            current--;
        }
        that.setData({
            current: current
        });
    },
    nextCard: function () {
        var that = this,
            current = that.data.current;
        if (current == 1) {
            current = 0
        } else {
            current++;
        }
        that.setData({
            current: current
        });
    },
    //打开支付订单页面
    // openMemberCardPay:function(e){
    //   var that = this,
    //       id = e.currentTarget.dataset.id,
    //       cardName = e.currentTarget.dataset.name,
    //       price = e.currentTarget.dataset.price,
    //       type = e.currentTarget.dataset.type;
    //   console.log(id)
    //   console.log(cardName)
    //   console.log(price)
    //   wx.navigateTo({
    //     url: '/subpages/memberCardPay/memberCardPay?id=' + id + '&cardName=' + cardName+'&price='+price+'&type='+type,
    //   })
    // },
})