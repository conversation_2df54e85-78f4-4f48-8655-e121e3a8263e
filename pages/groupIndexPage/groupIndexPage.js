//index.js
//获取应用实例
import { splitArrData } from '../../utils/util.js';
import {getGlobalData} from "../../utils/reuseFunc"
const app = getApp();
import { couponReceive } from "../../api/reuseRequest.js";
const rqcfg = require('../../utils/constant.js');
Page({
  data: {
    page:0,
    showLoading: true,
    noMoretip: false,
  }, 
  onLoad: function (e) {
    if (e && e.mid) {
      this.setData({
        mid: e.mid
      })
      /* 绑定分销关系 */
      this.requestdistributionInfo(e.mid)
    }
    this.initFunction();
  },
  onShow: function () {
    
  },
  initFunction(){
    this.requestIndex();
    this.requestGrouplist();
    this.getGlobalData('adInfo');
  },
  getGlobalData,
  requestIndex: function () {  
    wx.$get({
      map:'applet_mall_group_index'
    },{
      stopPull:true,
      showError:false,
    }).then(res=>{
      let responseData = res.data;
      if (responseData.template.title) {
        wx.setNavigationBarTitle({
          title: responseData.template.title
        });
      }
      let categoryList = splitArrData(responseData.shortcut, 8);
      this.setData({
        indexInfo:responseData,
        slideImgUrls: responseData.slide,
        shortMenu: responseData.shortcut,
        categoryList: categoryList,
        title: responseData.template.title,
        coupon: responseData.coupon
      })
    }).catch(err=>{
      console.log(err);
    })
  },
  //成为分销商
  requestdistributionInfo: function (mid) {
    var that = this,
      data = {
        map: 'applet_three_set_level_new',
        mid: mid ? mid : '',
      };
    console.log('成为分销商', data);
    wx.$get(data, {
      stopPull: true,
      showLoading: false,
      showError: false,
    }).then(res => {
      console.log('分销关系建立成功', res)
      console.log(res.data.tplIds)
    }).catch(err => {
      console.log('分销关系建立失败', err)
    })
  },
  requestGrouplist: function () {
    let page = this.data.page;
    wx.$get({
      map: 'applet_mall_group_list',
      type: 0,
      page: page,
      version:1
    },{
      pageList:true,
    }).then(res=>{
      let allArr = res.data.goods,
          initArr = this.data.shopGoods,
          curArr = res.data.goods;
      if (page > 0) {
        allArr = initArr.concat(curArr);
      } 
      this.setData({
        shopGoods: allArr
      })
      if (curArr.length < 10) {
        this.setData({
          noMoretip: true,
          showLoading: false
        });
      }
    }).catch(err=>{
      if (page <= 0) {
        this.setData({
          shopGoods: [],
          showLoading: false
        })
      } else {
        this.setData({
          noMoretip: true,
          showLoading: false
        });
      }
    })
  },
  onPullDownRefresh: function () {
    this.setData({
      page: 0,
      noMoretip: false,
      showLoading: true
    });
    this.requestIndex();
    this.requestGrouplist();

  },
  onReachBottom: function () {
    let { noMoretip,page } = this.data;
    page++;
    this.data.page = page;
    if (!noMoretip) {
      this.requestGrouplist();
    }
  },
  goodDetail: function (e) {
    let goodid = e.currentTarget.dataset.goodid;
    wx.navigateTo({
      url: '/pages/groupGoodDetail/groupGoodDetail?goodid=' + goodid
    }) 
  },
  searchPage: function () {
    wx.navigateTo({
      url: '/pages/searchList/searchList'
    })
  },
  groupGoods:function(e){
    let { id,name } = e.currentTarget.dataset;
    wx.navigateTo({
      url: '/subpages0/groupGoodsList/groupGoodsList?menuid=' + id + '&menuname=' + name
    }) 
  },
  toCuslink: function (e) {
    let link = e.currentTarget.dataset.link;
    wx.navigateTo({
      url: link
    })
  },
  getCoupon: function (e) {
    app.getSubId( 'applet_coupon_receive').then(res =>{this.toGetCoupon(e)})
  },
  // 领取优惠券跳转
  toGetCoupon: function (e) {
    let curId = e.currentTarget.dataset.id;
    let data = {
      id: curId
    }
    couponReceive(data).then(res=>{
      console.log("优惠券领取成功",res)
    }).catch(err=>{
      console.log("优惠券领取失败",err)
    })
  },
  onShareAppMessage: function (e) {
    let mid = app.globalData.userInfo.mid
    if (e.target) {
      let index = e.target.dataset.index;
      let coupon = this.data.coupon;
      coupon[index].needShare = 0;
      this.setData({
        coupon: coupon
      })
    }
    let title = this.data.title,
        shareInfo = app.globalData.shareInfo||{},
        cover = shareInfo.shareCover || '';
    title = shareInfo.shareTitle || title;
    app.getPoint(this);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/groupIndexPage/groupIndexPage' + '?mid=' + mid
    }
  },
  //朋友圈转发
  onShareTimeline(){
    let title = this.data.title,
        shareInfo = app.globalData.shareInfo||{},
        cover = shareInfo.shareCover || '';
    title = shareInfo.shareTitle || title;
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid,
      imageUrl: cover,
      success: function (res) {},
      fail: function (res) {}
    }
  }
})