<!--subpages0/giftCard/cardhome/cardhome.wxml-->

<!--折叠菜单  -->
<fold-menu></fold-menu>
  <view class="card-banner" wx:if="{{indexInfo.slide.length>0}}">
    <swiper class="swiper" indicator-dots="true" indicator-color="#dadada" indicator-active-color="#fff" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{indexInfo.slide}}">
      <swiper-item class="swiper-item">
        <view class="slide-item" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image fade_in" />
          <block wx:if="{{item.type=='101'}}">
          <kefu-info service-setting="{{serviceSetting}}" bindgetreply="getreplyData"></kefu-info>
          </block>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="kefu-btn" target="miniProgram" path="{{item.path}}" app-id="{{item.link}}" open-type="navigate" />
          </block>
        </view>
      </swiper-item>
      </block>
    </swiper>
  </view>
  <view class="opera-box">
    <view class="buy-record border-r" bindtap="tocardOrderlist">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/giftcardimg/icon_order.png" class="icon-record" mode="aspectFit"></image>
      <view class="intro-title">
        <text class="title-name">订单列表</text>
        <text class="desc">一览无余</text>
      </view>
    </view>
    <view class="buy-record" bindtap="tocardBag">
      <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/giftcardimg/icon_card.png" class="icon-record" mode="aspectFit"></image>
      <view class="intro-title">
        <text class="title-name">我的卡包</text>
        <text class="desc">便捷使用</text>
      </view>
    </view>
  </view>
  <view class="card-list-title" wx:if="{{indexInfo.coverList.length>0}}">{{dataInfo.listTitle?dataInfo.listTitle:'小小心意 速速拿去'}}</view>
  <view class="card-list" wx:if="{{indexInfo.coverList.length>0}}">
    <block wx:key="id" wx:for="{{indexInfo.coverList}}" wx:for-item="cardcover">
    <view class="card-item">
      <view class="card-item-con" data-id="{{cardcover.id}}" bindtap="toCardinfo">
        <image src="{{cardcover.cover}}" class="card-img"></image>
        <view class="card-name">{{cardcover.name}}</view>
      </view>
    </view>
    </block>
  </view>
  <view class="no-data" wx:if="{{indexInfo.coverList.length<=0}}">
    <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png"></image>
    <text>暂无任何封面哦~</text>
  </view>
  <!--错误提示-->
  <view class="error-tip fade_in" wx:if="{{errorTip.isShow}}">
    {{errorTip.text}}
  </view>

