/* subpages0/giftCard/cardhome/cardhome.wxss */
.card-banner{width: 750rpx;height: 400rpx;}
.card-banner .swiper{width: 100%;height: 100%;}
.card-banner .slide-item,.card-banner .slide-image{width: 100%;height: 100%;}
.card-banner .wx-swiper-dot { height: 6rpx !important; width:28rpx !important;border-radius: 0;}
.card-list-title{font-size: 30rpx;font-weight: bold;padding: 0 36rpx;margin-top: 30rpx;}
.card-list{padding: 6rpx 15rpx 15rpx;font-size: 0;}
.card-list .card-item{display: inline-block;width: 50%;box-sizing: border-box;padding: 15rpx;}
.card-list .card-item-con{width: 100%;font-size: 30rpx;border-radius: 35rpx;background-color: #fff;padding: 6rpx;box-shadow: 0 2rpx 20rpx #eee;box-sizing: border-box;}
.card-list .card-item-con .card-img{display: block;border-radius: 30rpx;width: 100%;height: 200rpx;}
.card-list .card-item-con .card-name{height: 64rpx;line-height: 64rpx;text-align: center;font-size: 28rpx;margin-top: 6rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.opera-box{text-align: center;background-color: #fff;}
.buy-record{text-align: center;padding: 28rpx 0;display: inline-block;vertical-align: middle;width: 48%;font-size: 0;}
.buy-record.border-r::after{top:46rpx;bottom: 46rpx;}
.buy-record .icon-record{display: inline-block;vertical-align: middle;width: 80rpx;height: 80rpx;margin-right: 20rpx;}
.buy-record .intro-title{display: inline-block;vertical-align: middle;text-align: left;}
.buy-record .title-name{display: block;color: #333;font-size: 30rpx;}
.buy-record .desc{display: block;color: #b1b1b1;font-size: 26rpx;}