// subpages0/giftCard/cardhome/cardhome.js
const app = getApp();
Page({
  data: {
    
  },
  onLoad: function (e) {
    var that = this;
    if (e && e.title) {
      app.setNavtitle(e.title);
    } else {
      app.setNavtitle('礼品卡');
    }
   
  
      that.requestIndex();
    
  },
  onShow: function () {

  },
  onPullDownRefresh:function(){
    var that = this;
    that.requestIndex();
  },
  requestIndex: function () {
    var that = this;
    
    wx.$get({
      map: 'applet_giftcard_index'
    },{
      stopPull:true
    }).then(res=>{
      let responseData = res.data;
          var dataInfo = responseData;
          if (dataInfo.title) {
            wx.setNavigationBarTitle({
              title: dataInfo.title
            });
          }
          that.setData({
            indexInfo: responseData
          })
    }).catch(err=>{
  
      console.log(err)
    })

  },
  //快捷导航跳转
  openFenleiLink: function (e) {
    var that = this;
    var type = e.currentTarget.dataset.type;
    console.log(type);
    if (type == 'index') {
      wx.reLaunch({
        url: '/pages/singlePage/singlePage'
      })
    }
    if (type == '102') {
      var mobile = app.globalData.telphone ? app.globalData.telphone : '';
      if (mobile) {
        app.makeCall(mobile);
      } else {
        wx.$showToast( "暂未获取到电话");
      }
    } else if (type == '103') {
      wx.navigateTo({
        url: '/pages/sharepage/sharepage'
      })
    } else if (type == '105') {
      that.requestSign();
    } else if (type == '3') {
      var url = e.currentTarget.dataset.url;
      var vrInfo = {
        vrurl: url
      }
      wx.setStorage({
        key: 'webviewUrl',
        data: vrInfo,
        success: function () {
          wx.navigateTo({
            url: '/pages/commonView/commonView',
          })
        }
      })
    } else if (type != '106' && type != '101') {
      var url = e.currentTarget.dataset.url;
      console.log(url);
      if (url) {
        wx.navigateTo({
          url: url
        })
      }
    }
  },
  toCardinfo: function (e) {
    var coverid = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/subpages0/giftCard/cardinfo/cardinfo?coverid=' + coverid
    })
  },
  tocardOrderlist: function () {
    wx.navigateTo({
      url: '/subpages0/giftCard/cardOrderlist/cardOrderlist',
    })
  },
  tocardBag: function () {
    wx.navigateTo({
      url: '/subpages0/giftCard/cardBag/cardBag',
    })
  },
  onShareAppMessage: function () {
    var that = this;
    var title = that.data.indexInfo.title;
    return {
      title: title
    }
  }
})