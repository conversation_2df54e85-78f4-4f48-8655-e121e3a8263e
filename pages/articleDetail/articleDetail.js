
const app = getApp();
Page({
  data: {
    title:''
  },
  onLoad: function (e) {
    var that = this;  
    if (e && e.enterfrom) {
      var enterfrom = e.enterfrom == 'share' ? true : false;
      that.setData({
        enterfrom: enterfrom
      })
    }
    var id = e.id;
    if (e.title.length>0){
      wx.setNavigationBarTitle({
        title: e.title
      })
      that.setData({
        title: e.title,
        id: e.id
      })
    }
    setTimeout(function(){
      //发起请求，获取列表列表
      wx.$get({
        map: 'applet_shop_service_details',
          id: id
      }).then(res=>{
        var responseData = res.data;
            that.setData({
              articleInfo: responseData
            })
            if (that.data.title.length<=0){
              that.setData({
                title: responseData.title
              })
              wx.setNavigationBarTitle({
                title: responseData.title
              })
            }
      }).catch(err=>{

        console.log(err)
      })
    },1)
  },
  onShareAppMessage: function () {
    var that = this,
        cover = '',
        title = that.data.title,
        id = that.data.id,
        shareInfo = app.globalData.shareInfo;
    title = shareInfo.shareTitle ? shareInfo.shareTitle : title;
    cover = shareInfo.shareCover ? shareInfo.shareCover : '';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/articleDetail/articleDetail?id=' + id + '&title=' + title
    }
  }
})