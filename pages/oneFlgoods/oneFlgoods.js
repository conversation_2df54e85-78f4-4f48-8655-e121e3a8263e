//获取应用实例
const rqcfg = require('../../utils/constant.js');
const app = getApp();
Page({
  data: {
    
  },
  onLoad: function (e) {
    var that = this;
    if(e){
      that.setData({
        queryData:e
      })
    }
      that.setData({
        showComponent:true
      })
  },
  onShow: function () {
    var that = this;
  },
  onPullDownRefresh: function () {
    var that = this;
    that.setData({
      isRefresh:!that.data.isRefresh
    })
  },
  onReachBottom: function () {
    var that = this;
    that.setData({
      isReachbottom:!that.data.isReachbottom
    })
  },
  onShareAppMessage: function () {
    var that = this;
    var title = '分类商品';
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    app.getPoint(that);
    var id = that.data.queryData.id ?  that.data.queryData.id : '';
    return {
      title: title,
      path: '/pages/oneFlgoods/oneFlgoods?id='+id
    }
  },
  //朋友圈转发
  onShareTimeline(){
    var that = this;
    var id = that.data.queryData.id ?  that.data.queryData.id : '';
    var title = '分类商品';
    let shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    let cover = shareInfo.shareCover || '';
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid+'&id=' + id,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})