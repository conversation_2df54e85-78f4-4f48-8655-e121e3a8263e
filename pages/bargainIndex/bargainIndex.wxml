
<!-- <back-home></back-home> -->
<nav-bar page-name="{{title}}"></nav-bar> 
<!--折叠菜单  -->
<fold-menu></fold-menu>
<view id="topdistance">
  <view class="banner-wrap" wx:if="{{slideImgUrls.length>0}}">
    <swiper indicator-dots="true" indicator-color="#cdced0" indicator-active-color="#fff" autoplay="true" interval="3000" duration="1000" circular="true">
      <block wx:key="index" wx:for="{{slideImgUrls}}">
        <swiper-item>
          <image src="{{item.img}}" class="slide-image" mode="aspectFill" />
        </swiper-item>
      </block>
    </swiper>
  </view>
  <!-- 分类导航 --> 
  <view class="nav-swiper" wx:if="{{category.length>0}}">
    <block wx:if="{{category.length<=5}}">
      <view class="nav-list flex-wrap {{category.length==4?'styletwo':''}} {{category.length==5?'stylethree':''}}">
          <block wx:key="index" wx:for="{{category}}" wx:for-item="category">
        <view class="nav-item flex-con" data-title="{{category.name}}" data-type="{{category.type}}" data-id="{{category.id}}" data-link="{{category.url}}" bindtap="openLink">
          <image src="{{category.icon}}" mode="aspectFill"></image>
          <text>{{category.name}}</text>
          <block wx:if="{{category.type=='106'}}">
            <navigator class="skip-btn" target="miniProgram" path="{{category.path}}" app-id="{{category.link}}" open-type="navigate" />
          </block>
        </view>
        </block>
      </view>
    </block>
    <block wx:elif="{{category.length<=8}}">
      <view class="nav-list twoline">
          <block wx:key="index" wx:for="{{category}}" wx:for-item="category">
        <view class="nav-item flex-con" data-type="{{category.type}}" data-title="{{category.name}}" data-id="{{category.id}}" data-link="{{category.url}}" bindtap="openLink">
          <image src="{{category.icon}}" mode="aspectFill"></image>
          <text>{{category.name}}</text>
          <block wx:if="{{category.type=='106'}}">
            <navigator class="skip-btn" target="miniProgram" path="{{category.path}}" app-id="{{category.link}}" open-type="navigate" />
          </block>
        </view>
        </block>
      </view>
    </block> 
    <block wx:else>
    <swiper class="{{categoryList[0].length<=5?'singleH':''}}" autoplay="{{false}}" circular="{{false}}" duration="600" indicator-dots="{{categoryList.length>1}}" indicator-color="#ddd" indicator-active-color="#009DDA">
        <block wx:key="index" wx:for="{{categoryList}}" wx:for-item="categoryitem">
        <swiper-item>
          <view class="fl-nav-wrap">
              <block wx:key="index" wx:for="{{categoryitem}}" wx:for-item="category">
              <view class="fl-nav-item" data-type="{{category.type}}" data-title="{{category.name}}" data-id="{{category.id}}" data-link="{{category.url}}" bindtap="openLink">
                <image src="{{category.icon}}" mode="aspectFill"></image>
                <text>{{category.name}}</text>
                <block wx:if="{{category.type=='106'}}">
                  <navigator class="skip-btn" target="miniProgram" path="{{category.path}}" app-id="{{category.link}}" open-type="navigate" />
                </block>
              </view>
            </block>
          </view>
        </swiper-item>
      </block>
    </swiper>
    </block>
  </view>
  <view class="menu-list">
    <view class="menu-item flex-wrap border-r" bindtap="tomyParticipate">
      <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/bargain/icon_myjoin.png" mode="aspectFit"></image>
      <view class="right-intro flex-con">
        <view class="menu-title">我的参与</view>
        <view class="menu-brief">参与的活动</view>
      </view>
    </view>
    <view class="menu-item flex-wrap border-r" bindtap="tomyOrder">
      <image src="https://tiandiantong.oss-cn-beijing.aliyuncs.com/images/bargain/icon_myorder.png" mode="aspectFit"></image>
      <view class="right-intro flex-con">
        <view class="menu-title">我的订单</view>
        <view class="menu-brief">查看订单详情</view>
      </view>
    </view>
  </view>
  <!-- 广告位 -->
  <ad unit-id="{{adInfo.bargainAdId}}" wx:if="{{adInfo.bargainAdOpen==1&&adInfo.bargainAdId!=''}}"></ad>
</view>
<view class="bargain-tab-zhanwei">
  <view class="bargain-tab {{isFixed==1?'fixed':''}} border-b">
    <view class="bargain-tab-item {{activityStatus==3?'active':''}}" data-status="3" bindtap="toggleStatus">已结束</view>
    <view class="bargain-tab-item {{activityStatus==2?'active':''}}" data-status="2" bindtap="toggleStatus">正在进行</view>
    <view class="bargain-tab-item {{activityStatus==1?'active':''}}" data-status="1" bindtap="toggleStatus">即将开始</view>
  </view>
</view>
<view class="bargain-goods" wx:if="{{bargainGoodslist.length>0}}">
   <block wx:key="index" wx:for="{{bargainGoodslist}}" wx:for-item="good">
  <view class="goods-item" data-id="{{good.id}}" bindtap="tobargainGoodDetail">
    <view class="img-box">
      <image src="{{good.cover}}" class="good-img fade_in" mode="aspectFill"></image>
      <!-- <view class="stock-show">剩余{{good.stock}}件</view> -->
      <view class="stock-brow flex-wrap {{good.showNumShow==1?'fixed-all':'fixed-right'}}">
        <view class="stock flex-con"><block wx:if="{{good.showNumShow==1}}">{{good.showNum}}人已查看</block></view>
        <view class="brow-num">剩余{{good.stock}}件</view>
      </view>

    </view>
    <view class="goods-intro">
      <view class="good-title">{{good.name}}</view>
      <view class="user-avatar-box flex-wrap" wx:if="{{good.avatars.length>0}}">
        <block wx:key="index" wx:for="{{good.avatars}}" wx:if="{{index<9}}">
          <image src="{{item}}" class="user-avatar" mode="aspectFill"></image>
        </block>
        <image src="/images/more-avatar.png" class="user-avatar" mode="aspectFill" wx:if="{{good.avatars.length>=9}}"></image>
        <view class="flex-con">共<text>{{good.sold}}</text>人参与</view>
      </view>
      <view class="good-price">
        <view class="now-price">最低￥<text>{{good.minPrice}}</text></view>
        <view class="ori-price" wx:if="{{good.oriPrice>0}}">原价￥{{good.oriPrice}}</view>
      </view>
      <!-- <view class="join-num">{{good.sold}}人参加</view> -->
      <view class="btn-area">
        <view class="join-btn" wx:if="{{good.timeStatus==0}}">即将开始</view>
        <view class="join-btn end" wx:elif="{{good.timeStatus==2}}">已结束</view>
        <view class="join-btn end" wx:elif="{{good.stock==0}}">已抢光</view>
        <view class="join-btn" wx:else>我要参加</view>
      </view>
    </view>
  </view>
  </block>
</view>
<!--上拉加载提示-->
<view class="loading-tip" wx:if="{{showLoading}}">
  <view class="icon_load">
    <view id="floatingBarsG">
      <view class="blockG" id="rotateG_01"></view>
      <view class="blockG" id="rotateG_02"></view>
      <view class="blockG" id="rotateG_03"></view>
      <view class="blockG" id="rotateG_04"></view>
      <view class="blockG" id="rotateG_05"></view>
      <view class="blockG" id="rotateG_06"></view>
      <view class="blockG" id="rotateG_07"></view>
      <view class="blockG" id="rotateG_08"></view>
    </view>
  </view>
  <text>努力加载中...</text>
</view>
<view class="nomore-tip" wx:if="{{noMoretip&&bargainGoodslist.length>0}}">没有更多数据了</view>
<view class="no-data" style="padding:120rpx 0;" wx:if="{{bargainGoodslist&&bargainGoodslist.length<=0}}">
  <image src="http://tiandiantong.oss-cn-beijing.aliyuncs.com/images/zw_shop.png" style="width:120rpx;height:120rpx;"></image>
  <text>暂无相关活动哦~</text>
</view>
<!--错误提示-->
 
