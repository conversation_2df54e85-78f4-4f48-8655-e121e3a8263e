//index.js
//获取应用实例
import { splitArrData } from '../../utils/util.js';
import {getGlobalData} from "../../utils/reuseFunc";
const rqcfg = require('../../utils/constant.js');
const app = getApp();
Page({
  data: {
    page: 0,
    showLoading: true,
    noMoretip: false,
    activityStatus: '2',
    title:'微砍价'
  },
  onLoad: function (e) {
    var that = this;
  },
  onShow: function () {
    var that = this;

    
      that.requestIndex();
      that.requestBargainlist();
      this.getGlobalData('adInfo');
    
    app.setCartnum();//更新购物车数量
  },
  getGlobalData,
  requestIndex: function () {
    var that = this;

    wx.$get({
      map: 'applet_bargain_index'
    }).then(res=>{
      let responseData = res.data;
          if (responseData.template.title) {
            wx.setNavigationBarTitle({
              title: responseData.template.title
            });
            that.setData({
              title:responseData.template.title
            })
          }
          var categoryList = splitArrData(responseData.shortcut, 10);
          that.setData({
            indexInfo: responseData,
            category: responseData.shortcut,
            categoryList: categoryList,
            slideImgUrls: responseData.slide
          })
          that.getPostTabtop();
    }).catch(err=>{
      console.log(err)
    })
  },
  openLink: function (e) {
    var that = this;
    var link = e.currentTarget.dataset.link;
 
    if (link) {
      wx.navigateTo({
        url: link
      });
    }
  },
  requestBargainlist: function () {
    var that = this,
        page = that.data.page,
        data = {
          map: 'applet_bargain_activity_list',
          page: page,
          timeStatus: that.data.activityStatus
        }

    wx.$get(data,{
      pageList:true
    }).then(res=>{
      let responseData = res.data;
      var allArr = [],
      initArr = that.data.bargainGoodslist,
      curArr = responseData,
      lastPageLength = curArr.length;
      if (page > 0) {
        allArr = initArr.concat(curArr);
      } else {
        allArr = responseData;
      }
      that.setData({
        bargainGoodslist: allArr
      })
      if (lastPageLength < 10) {
        that.setData({
          noMoretip: true,
          showLoading: false
        });
      }
    }).catch(err=>{
      console.log(err)
      if (page <= 0) {
        that.setData({
          bargainGoodslist: [],
          showLoading: false
        })
      } else {
        that.setData({
          noMoretip: true,
          showLoading: false
        });
      }
    })
  },
  onPullDownRefresh: function () {
    this.setData({
      page: 0,
      noMoretip: false,
      showLoading: true
    });
    this.onShow();
    this.onLoad();
    
  },
  onReachBottom: function () {
    var that = this,
        isMore = that.data.noMoretip,
        page = that.data.page;
     
    page++;
    that.setData({
      page: page
    });
    if (!isMore) {
      that.requestBargainlist();
    }
  },
  tobargainGoodDetail: function (e) {//砍价商品详情
    var that = this;
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/subpages/bargainGoodDetail/bargainGoodDetail?id=' + id
    })
  },
  tomyParticipate: function () {
    wx.navigateTo({
      url: '/subpages/myParticipate/myParticipate',
    })
  },
  tomyOrder: function () {
    wx.navigateTo({
      url: '/subpages/bargainOrder/bargainOrder',
    })
  },
  toCuslink: function (e) {
    var link = e.currentTarget.dataset.link;
    console.log(link);
    wx.navigateTo({
      url: link
    })
  },
  toggleStatus: function (e) {
    var that = this;
    var status = e.currentTarget.dataset.status;
    that.setData({
      page: 0,
      noMoretip: false,
      showLoading: true,
      activityStatus: status,
      bargainGoodslist: null
    })
    that.requestBargainlist();
  },
  getPostTabtop: function () {
    var that = this;
    wx.createSelectorQuery().select('#topdistance').boundingClientRect(function (rect) {
      that.setData({
        posttabTop: rect.height
      })
    }).exec()
  },
  onPageScroll: function (e) {
    var that = this,
        scrollTop = e.scrollTop,
        posttabTop = that.data.posttabTop;
    if (scrollTop > 480 && that.data.showBacktop != 1) {
      that.setData({ showBacktop: 1 })
    } else if (scrollTop <= 480 && that.data.showBacktop != 0) {
      that.setData({ showBacktop: 0 })
    }
    if (scrollTop >= posttabTop && that.data.isFixed != 1) {
      that.setData({ isFixed: 1 })
    } else if (scrollTop < posttabTop && that.data.isFixed != 0) {
      that.setData({ isFixed: 0 })
    }
  },
  onShareAppMessage: function () {
    var title = this.data.title;
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    app.getPoint(that);
    return {
      title: title,
      imageUrl: cover,
      path: '/pages/bargainIndex/bargainIndex'
    }
  },
  //朋友圈转发
  onShareTimeline(){
    var that = this;
    var title = that.data.title;
    var shareInfo = app.globalData.shareInfo||{};
    title = shareInfo.shareTitle || title;
    var cover = shareInfo.shareCover || '';
    return {
      title: title,
      query: "suid="+rqcfg.suid+'&appid='+rqcfg.appid,
      imageUrl: cover,
      success: function (res) {
        console.log("转发成功");
      },
      fail: function (res) {
        console.log("转发失败");
        console.log(res);
      }
    }
  }
})