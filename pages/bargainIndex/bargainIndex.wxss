/**index.wxss**/
page { background-color: #f3f5f7; border: none; }

.banner-wrap { width: 100%; height: 400rpx; position: relative; }
.banner-wrap swiper { height: 100%; }
.banner-wrap image { width: 100%; height: 100%; }
.banner-wrap .wx-swiper-dots.wx-swiper-dots-horizontal { width: 95%; text-align: center; }
.banner-wrap .wx-swiper-dot { height: 6px; width: 6px; margin: 0 !important; margin-right: 5px !important; }
.menu-list { padding: 10rpx 0; background-color: #fff; overflow: hidden;}
.menu-list .menu-item { padding: 20rpx 40rpx; width: 50%; float: left; box-sizing: border-box; }
.menu-list .menu-item.border-r:after { top: 25rpx; bottom: 25rpx; }
.menu-list .menu-item:nth-of-type(2n):after { width: 0; }
.menu-list .menu-item image { height: 80rpx; width: 80rpx; display: block; margin-right: 20rpx; }
.menu-list .menu-item .menu-title { font-size: 32rpx; max-width: 220rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.menu-list .menu-item .menu-brief { font-size: 26rpx; color: #999; max-width: 220rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
/* 砍价活动tab */
.bargain-tab-zhanwei{height: 88rpx;}
.bargain-tab{height: 88rpx;display: flex;align-items: center;width: 100%;background-color: #fff;margin-top: 16rpx;}
.bargain-tab.fixed{position: fixed;top:0;left:0;width: 100%;z-index: 10;margin-top: 0;}
.bargain-tab-item{flex:1;height: 88rpx;line-height: 88rpx;text-align: center;font-size: 30rpx;color: #666;}
.bargain-tab-item.active{color: #ff8132;position: relative;}
.bargain-tab-item.active::before{content: '';position: absolute;bottom: 0;width: 100rpx;left: 50%;margin-left: -50rpx;height: 6rpx;background-color: #ff8132;z-index: 10;}
/* 砍价商品 */
.goods-item { padding: 20rpx 20rpx 0 20rpx; background-color: #fff; margin-top: 16rpx; align-items: flex-start; }
.goods-item:first-child{margin-top: 0;}
.goods-item .img-box { width: 710rpx; height: 350rpx;position: relative; }
.goods-item .good-img { display: block;width: 100%;height: 100%; }
/* .goods-item .img-box .stock-show{height: 52rpx;line-height: 52rpx;background-color: rgba(0, 0, 0, .5);border-radius: 30rpx 0 0 30rpx;position: absolute;top:20rpx;right: 0;z-index: 1;font-size: 26rpx;padding: 0 12rpx 0 16rpx;color: #fff;} */
.goods-item .img-box .stock-brow{background-color:rgba(0,0,0,0.5);color:#fff;font-size:24rpx;padding:15rpx;position:absolute;right:0;bottom:0;letter-spacing: 1rpx;}
.goods-item .img-box .stock-brow.fixed-all{left:0;}
.goods-item .img-box .stock-brow.fixed-right{right:0;bottom:0;left:none;border-radius:25rpx 0 0 0;padding:10rpx 20rpx;}
.goods-item .goods-intro { position: relative; padding: 20rpx 0; }
.goods-item .good-title { font-size: 32rpx;white-space: nowrap;overflow: hidden;text-overflow: ellipsis; }
.goods-item .good-price { font-size: 26rpx; color: #999; line-height: 1.5;margin-top: 10rpx; }
.goods-item .good-price view { display: inline-block; vertical-align: middle; }
.goods-item .good-price .ori-price{position: relative;top:2rpx;}
.goods-item .good-price .now-price { color: #ff4147; min-width: 190rpx; box-sizing: border-box; padding-right: 15rpx; }
.goods-item .good-price .now-price text { font-size: 36rpx; }
.goods-item .join-num { font-size: 26rpx; color: #999; }
.goods-item .goods-intro .btn-area { position: absolute; bottom: 20rpx; right: 0; text-align: right; }
.goods-item .goods-intro .join-btn { height: 64rpx; line-height: 64rpx; width: 156rpx; border-radius: 36rpx; text-align: center; font-size: 26rpx; background-color: #ff8c47; color: #fff; box-shadow: 0 0 10rpx #ff8637; }
.goods-item .goods-intro .join-btn.end{background-color: #bfbfbf;box-shadow: none;}
.user-avatar-box{padding: 20rpx 10rpx;}
.user-avatar-box .user-avatar{height: 50rpx;width: 50rpx;margin-left: -10rpx;display: block;border:2rpx solid #fff;border-radius: 50%;background-color: #f0f0f0;}
.user-avatar-box view{text-align:right;font-size: 26rpx;color: #999;}
.user-avatar-box view text{color: red;}


.skip-btn{position: absolute; top: 0; left: 0; height: 100%; width: 100%; box-sizing: border-box; margin: 0; z-index: 1; opacity: 0;} 
/* 分类导航 */
.nav-swiper { background-color: #fff;margin-bottom:16rpx; }
.nav-swiper swiper { height: 310rpx; }
.nav-swiper swiper.singleH { height: 150rpx; }
.nav-swiper .wx-swiper-dot { position: relative; top: 16rpx; border-radius: 0 !important; width: 12px !important; height: 3px !important; }
.fl-nav-wrap { background-color: #fff; padding: 25rpx 10rpx 8rpx; font-size: 0; }
.fl-nav-item { width: 20%; margin: 0 auto; display: inline-block; margin-bottom: 8rpx;position:relative; }
.fl-nav-item image { width: 84rpx; height: 84rpx; display: block; margin: 0 auto; border-radius: 35rpx; }
.fl-nav-item text { text-align: center; font-size: 26rpx; line-height: 2; display: block; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }
/* 分类导航小于9个样式 */
.nav-list{padding: 20rpx 0;}
.nav-list .nav-item{position:relative;}
.nav-list .nav-item image{width: 100rpx;height: 100rpx;border-radius: 16rpx;margin:0 auto;display: block;}
.nav-list .nav-item text{font-size: 30rpx;display: block;text-align: center;margin:0 auto;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;margin-top: 12rpx;max-width: 230rpx;}
.nav-list.styletwo .nav-item image{width: 92rpx;height: 92rpx;}
.nav-list.styletwo .nav-item text{font-size: 28rpx;max-width: 180rpx;}
.nav-list.stylethree .nav-item image{width: 84rpx;height: 84rpx;}
.nav-list.stylethree .nav-item text{font-size: 26rpx;max-width: 140rpx;margin-top:10rpx;}
.nav-list.twoline{overflow: hidden;padding-bottom: 5rpx;}
.nav-list.twoline .nav-item{width: 25%;float: left;margin-bottom: 15rpx;}
.nav-list.twoline .nav-item image{width: 92rpx;height: 92rpx;}
.nav-list.twoline .nav-item text{font-size: 28rpx;max-width: 180rpx;}