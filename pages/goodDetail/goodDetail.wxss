page {
  background-color: #F7F7F7;
  height: 100%;
  position: relative;
}

/* 分销邀请 */
.fxtip-img {
  position: fixed;
  z-index: 10;
  height: 120rpx;
  width: 120rpx;
  bottom: 230rpx;
  right: 0;
}

.fxtip-img image {
  display: block;
  width: 100%;
  height: 100%;
}

.good-wrap {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  padding-top: 80rpx;
  padding-bottom: 110rpx;
}

.good-tab {
  position: fixed;
  z-index: 5;
  left: 0;
  top: 0;
  height: 80rpx;
  box-shadow: 0 3rpx 10rpx #ddd;
  background-color: #fff;
  display: table;
  padding-right: 100rpx;
  padding-left: 40rpx;
}

.good-tab .good-tab-item {
  display: table-cell;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  width: 1000rpx;
  font-size: 30rpx;
  color: #5c5c5c;
  transition: all 0.5s;
}

.good-tab .good-tab-item.active {
  color: #ff8232;
}

.good-tab .good-tab-item .icon_dw {
  display: inline-block;
  width: 20rpx;
  height: 28rpx;
  margin-right: 10rpx;
  position: relative;
  top: 4rpx;
  opacity: 0;
  transition: all 0.5s;
}

.good-tab .good-tab-item.active .icon_dw {
  opacity: 1;
}

.good-tab .share-enter {
  position: absolute;
  top: 0;
  right: 0;
  width: 90rpx;
  height: 80rpx;
  z-index: 1;
  box-sizing: border-box;
  padding: 22rpx;
}

.good-tab .share-enter image {
  display: block;
  width: 100%;
  height: 100%;
}

.good-wrap::-webkit-scrollbar {
  display: none;
}

.good-wrap.hidden {
  height: 100%;
  overflow: hidden;
}

.good-pic-banner {
  width: 100%;
  position: relative;
}

.good-pic-banner .wx-swiper-dot {
  height: 6px;
  width: 6px;
  margin: 0 !important;
  margin-right: 5px !important;
}

.good-pic-banner .cover {
  width: 100%;
  height: auto;
}

.good-pic-banner swiper {
  height: 500rpx;
}

.good-pic-banner swiper.square {
  height: 750rpx;
}

.good-pic-banner swiper image {
  width: 100%;
  height: 100%;
  display: block;
}

.good-pic-banner .dots-wrap {
  position: absolute;
  right: 0;
  bottom: 30rpx;
  z-index: 3;
  text-align: right;
  font-size: 26rpx;
  padding: 0 12rpx 0 16rpx;
  height: 48rpx;
  line-height: 48rpx;
  box-sizing: border-box;
  border-radius: 30rpx 0 0 30rpx;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
}

.good-pic-banner .dots-wrap .big {
  font-size: 32rpx;
}

.good-pic-banner .play-video-btn {
  position: absolute;
  top: 50%;
  margin-top: -48rpx;
  left: 50%;
  margin-left: -48rpx;
}

.good-pic-banner .play-video-btn image {
  display: block;
  width: 96rpx;
  height: 96rpx;
}

.good-pic-banner .banner-video {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  font-size: 0;
}

.good-pic-banner .banner-video video {
  height: 420rpx;
  width: 100%;
}

.good-pic-banner .banner-video.square video {
  height: 670rpx;
}

.good-pic-banner .banner-video .close-video {
  width: 100%;
  height: 80rpx;
  background-color: #000;
  text-align: center;
}

.good-pic-banner .banner-video .close-video text {
  background-color: #fff;
  border-radius: 30rpx;
  color: #333;
  font-size: 26rpx;
  display: inline-block;
  height: 50rpx;
  line-height: 50rpx;
  padding: 0 30rpx;
  margin: 15rpx 0;
}

.goodinfo-intro {
  background-color: #fff;
  border-radius: 0 0 20rpx 20rpx;
}

.good-info {
  background-color: #fff;
  border-radius: 0 0 20rpx 20rpx;
}

.seckill-show {
  border-radius: 15rpx 15rpx 0 0;
  margin-top: -14rpx;
  position: relative;
  z-index: 2;
  width: 100%;
  background: #FEF2F4;
  text-align: right;
}

.seckill-show .left-price {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  padding: 12rpx 50rpx 12rpx 25rpx;
  background: -webkit-linear-gradient(top, #F8102E, #F8102E);
  background: linear-gradient(top, #F8102E, #F8102E);
  border-radius: 15rpx 80rpx 0 0;
  white-space: nowrap;
  text-align: left;
  width: 50%;
}

.seckill-show .left-price .label {
  font-size: 30rpx;
  font-weight: 800;
  font-style: italic;
  color: #fff;
  border-radius: 12rpx 12rpx 0 12rpx;
  background-color: #DB420C;
  width: 72rpx;
  display: inline-block;
  vertical-align: middle;
  line-height: 32rpx;
  text-align: center;
  padding: 6rpx 0;
  margin-right: 20rpx;
}

.seckill-show .left-price .price {
  display: inline-block;
  vertical-align: middle;
}

.seckill-show .left-price .seckill-price {
  font-size: 24rpx;
  color: #fff;
}

.seckill-show .left-price .seckill-price .big {
  font-size: 38rpx;
  font-weight: bold;
}

.seckill-show .ori-price {
  font-size: 24rpx;
  color: #fff;
  margin-top: -4rpx;
}

.seckill-show .ori-price text {
  text-decoration: line-through;
}

.seckill-show .remain-time {
  text-align: center;
  padding: 12rpx 25rpx;
  display: inline-block;
  color: #fff;
}

.seckill-show .remain-time .time-tips {
  font-size: 22rpx;
  color: #F40B0C;
}

.seckill-show .remain-time .time-show {
  font-size: 0;
  color: #F40B0C;
}

.seckill-show .remain-time .time-show text {
  font-size: 22rpx;
}

.seckill-show .remain-time .sj {
  font-size: 22rpx;
  display: inline-block;
  min-width: 36rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  margin: 0 5rpx;
  border-radius: 6rpx;
  color: #fff;
  background-color: #F40B0C;
  box-sizing: border-box;
  padding: 0 5rpx;
}


.price-box {
  padding: 20rpx 25rpx;
  margin-top: 16rpx;
}

.price-box .price {
  font-size: 40rpx;
  color: #FF333C;
  font-weight: bold;
}

.price-box .price .vip-tag {
  background-color: #000000;
  color: #F4AB2D;
  font-size: 20rpx;
  border-radius: 0 16rpx 0 16rpx;
  padding: 6rpx 10rpx;
  margin-left: 8rpx;
  font-weight: normal;
  position: relative;
  top: -6rpx;
}

.vip-price-show {
  font-size: 30rpx;
  color: #333;
  font-weight: normal;
}

.price-box .price .big {
  font-size: 38rpx;
}

.price-box .ori-price {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.price-box .ori-price text {
  text-decoration: line-through;
}

.collect-btn {
  display: block;
  box-sizing: border-box;
  font-size: 0;
  padding: 8rpx 20rpx;
  transform: scale(1.2);
}

.seckill-collec-box .collect-btn {
  padding: 10rpx 20rpx;
  margin-right: -20rpx;
}

.collect-btn.border-r::after {
  top: 18rpx;
  bottom: 18rpx;
}

.collect-btn .icon {
  display: block;
  height: 32rpx;
  width: 32rpx;
  margin: 0 auto;
  margin-right: 8rpx;
}

.collect-btn text {
  display: block;
  font-size: 22rpx;
  color: #333;
  text-align: center;
  margin-top: 8rpx;
}

/* 会员价展示 */
.member-price-box {
  background-color: #FDF8EF;
  padding: 8rpx 16rpx;
  width: 94%;
  margin: 0 auto;
  margin-top: 16rpx;
  box-sizing: border-box;
}

.member-price-box .member-price-item {
  margin: 8rpx 0;
}

.member-price-box .member-price,
.member-price-box .member-price-item {
  font-size: 0;
}

.member-price-box .member-price {
  display: inline-block;
  line-height: 48rpx;
  color: #fff;
  border-radius: 8rpx;
  padding: 0 10rpx;
  background: -webkit-linear-gradient(left, #CB9A7B, #E1C3AE);
  background: linear-gradient(left, #CB9A7B, #E1C3AE);
}

.member-price-box .member-price.bg1 {
  background: -webkit-linear-gradient(left, #E19A1F, #F3D6A0);
  background: linear-gradient(left, #E19A1F, #F3D6A0);
}

.member-price-box .member-price.bg2 {
  background: -webkit-linear-gradient(left, #B5B9B8, #DFE2E2);
  background: linear-gradient(left, #B5B9B8, #DFE2E2);
}

.member-price-box .member-price .icon {
  display: inline-block;
  vertical-align: middle;
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

.member-price-box .member-price text {
  display: inline-block;
  vertical-align: middle;
  font-size: 24rpx;
}

.member-price-box .open-memeber {
  display: block;
  width: 74rpx;
  height: 74rpx;
}

.member-price-box .open-memeber image {
  display: block;
  width: 100%;
  height: 100%;
}

.name-brief {
  padding: 20rpx 25rpx;
  background-color: #fff;
}

.name-brief .good-name {
  display: block;
  line-height: 46rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.name-brief .good-name .label {
  border-radius: 30rpx;
  font-size: 22rpx;
  padding: 0 12rpx;
  background-color: #FF333C;
  color: #fff;
  text-align: center;
  font-weight: normal;
  display: inline-block;
  vertical-align: middle;
  line-height: 36rpx;
  position: relative;
  top: -2rpx;
  margin-right: 8rpx;
}

.name-brief .good-brief {
  display: block;
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-top: 10rpx;
}

.name-brief .good-brief .red {
  color: #FF333C;
  display: inline;
}

.name-brief .scan-num {
  font-size: 24rpx;
  color: #999;
}

.name-brief .scan-num .icon {
  display: block;
  width: 26rpx;
  height: 26rpx;
  margin: 0 auto;
}

.fu-info {
  padding: 20rpx 25rpx;
}

.fu-info .icon {
  display: inline-block;
  vertical-align: middle;
  height: 32rpx;
  width: 32rpx;
  margin-right: 2rpx;
  position: relative;
  top: -2rpx;
}

.fu-info view {
  text-align: center;
  font-size: 24rpx;
  color: #666666;
}

/* 分销返佣 */
.distribution-rebate-box {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16rpx;
}

.distribution-rebate-box .bg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 0;
}

.distribution-rebate-box .distribution-rebate {
  position: relative;
  z-index: 2;
  padding: 16rpx 46rpx 16rpx 0;
  font-size: 26rpx;
  line-height: 56rpx;
  width: 100%;
  box-sizing: border-box;
}

.distribution-rebate-box .rebate-tip {
  display: block;
  height: 76rpx;
  width: 76rpx;
  text-align: center;
  line-height: 76rpx;
  font-size: 24rpx;
  color: #F83167;
  margin: 0 30rpx 0 16rpx;
  background-color: #fff;
  border-radius: 50%;
}

.distribution-rebate-box .item-info {
  padding: 10rpx 0;
}

.distribution-rebate-box .item-info.border-b:last-child::after {
  height: 0;
}

.distribution-rebate-box .item-info .distri-btn {
  height: 56rpx;
  line-height: 56rpx;
  color: #F81E1F;
  padding: 0 15rpx;
}

.distribution-rebate-box .item-info .thin-border:after {
  border-color: #F81E1F;
}

.distribution-rebate-box .reward-info {
  font-size: 26rpx;
  white-space: nowrap;
  line-height: 56rpx;
}

.distribution-rebate-box .reward-info text {
  color: #ff2200;
}

.distribution-rebate-box .reward-list1 {
  display: table;
}

.distribution-rebate-box .reward-list1 view {
  display: table-cell;
  text-align: center;
  width: 750px;
}

.distribution-rebate-box .reward-list2 view {
  display: inline-block;
  min-width: 30%;
  text-align: center;
}

.good-opera {
  border-radius: 20rpx;
  background-color: #fff;
  padding: 0 25rpx;
  margin-top: 16rpx;
}

.good-opera .opera-info {
  padding: 25rpx 0;
  align-items: flex-start;
  font-size: 26rpx;
  color: #333;
}

.good-opera .title-name {
  position: relative;
  padding-left: 16rpx;
}

.good-opera .title-name::before {
  content: '';
  position: absolute;
  left: 0;
  height: 28rpx;
  width: 6rpx;
  top: 50%;
  transform: translateY(-50%);
  background: -webkit-linear-gradient(bottom, #ECF5FF, #F97300);
  background: linear-gradient(bottom, #ECF5FF, #F97300);
  border-radius: 4rpx;
}

.opera-info:last-child.border-b::after {
  height: 0;
}

.opera-info .left-label {
  font-size: 26rpx;
  color: #999;
  width: 80rpx;
}

.opera-info .th-tip {
  font-size: 0;
}

.opera-info .th-tip .icon {
  display: inline-block;
  vertical-align: middle;
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.opera-info .th-tip text {
  display: inline-block;
  vertical-align: middle;
  font-size: 26rpx;
}

.opera-info .icon-jt {
  display: block;
  height: 40rpx;
  width: 12rpx;
}

.all-ggtip {
  margin-top: 10rpx;
}

.all-ggtip image {
  display: inline-block;
  vertical-align: middle;
  width: 56rpx;
  height: 56rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.all-ggtip text {
  display: inline-block;
  vertical-align: middle;
  background-color: #f9f9f9;
  font-size: 24rpx;
  color: #999;
  border-radius: 12rpx;
  line-height: 36rpx;
  padding: 10rpx 16rpx;
}

.sales-item {
  font-size: 26rpx;
  padding: 2rpx 0;
  margin-bottom: 8rpx;
}

.sales-item:last-child {
  margin-bottom: 0;
}

.sales-item .tag {
  font-size: 22rpx;
  color: #F94747;
  border-radius: 4rpx;
  padding: 4rpx 6rpx;
  margin-right: 8rpx;
  min-width: 50rpx;
  box-sizing: border-box;
  text-align: center;
}

.sales-item .tag.thin-border:after {
  border-color: #F94747;
}

.coupon-show {
  font-size: 0;
}

.coupon-show .coupon-tips {
  display: inline-block;
  vertical-align: middle;
  min-width: 100rpx;
  height: 38rpx;
  line-height: 38rpx;
  padding: 0 20rpx;
  text-align: center;
  margin-right: 10rpx;
  color: #F94747;
  position: relative;
  box-sizing: border-box;
}

.coupon-show .coupon-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 0;
}

.coupon-show .coupon-tips text {
  position: relative;
  z-index: 2;
  font-size: 26rpx;
}

.labels-box .labels-item {
  display: inline-block;
  vertical-align: middle;
  margin-right: 16rpx;
}

.labels-box .labels-item image {
  width: 26rpx;
  height: 26rpx;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
  position: relative;
  top: -2rpx;
}

.labels-box .labels-item text {
  display: inline-block;
  vertical-align: middle;
  font-size: 26rpx;
  color: #333;
}

/* 评价内容 */
.evaluate-list {
  padding: 20rpx 0 10rpx;
}

.evaluate-part {
  background-color: #fff;
  padding: 0 25rpx;
  margin-top: 15rpx;
  padding-bottom: 12rpx;
}

.evaluate-title {
  font-size: 30rpx;
  padding: 26rpx 0;
}

.evaluate-title .seeall {
  text-align: right;
  color: #ff8232;
  font-size: 28rpx;
}

.evaluate-title .seeall image {
  display: inline-block;
  vertical-align: middle;
  height: 25rpx;
  width: 25rpx;
  position: relative;
  top: -2rpx;
}

.evaluate-item {
  padding-bottom: 10rpx;
}

.evaluate-item .user-info .avatar {
  height: 50rpx;
  width: 50rpx;
  display: block;
  border-radius: 50%;
  margin-right: 15rpx;
}

.evaluate-item .user-info .nickname {
  font-size: 24rpx;
  color: #999;
}

.evaluate-item .user-info .star {
  font-size: 0;
}

.evaluate-item .user-info .star image {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8rpx;
  height: 20rpx;
  width: 20rpx;
}

.evaluate-item .comment-txt {
  color: #333;
  font-size: 26rpx;
  padding: 10rpx 0;
}

.evaluate-item .comment-img {
  overflow: hidden;
  padding: 4rpx 0;
}

.evaluate-item .comment-img .image-item {
  width: 33.33%;
  float: left;
  padding: 6rpx 0;
}

.evaluate-item .comment-img image {
  display: block;
  height: 222rpx;
  width: 222rpx;
}

.evaluate-item .date-format {
  font-size: 24rpx;
  color: #999;
}

.evaluate-item .date-format .date {
  margin-right: 20rpx;
}

.evaluate-item .merchant-reply {
  padding: 15rpx 20rpx;
  background-color: #f6f6f6;
  font-size: 26rpx;
  color: #666;
  position: relative;
  margin-top: 20rpx;
}

.evaluate-item .merchant-reply:before {
  content: '';
  position: absolute;
  left: 30rpx;
  top: -28rpx;
  border-width: 16rpx;
  border-style: dashed dashed solid dashed;
  border-color: transparent transparent #f6f6f6 transparent;
  z-index: 1;
}

.eval-opera-area {
  padding: 30rpx 0 40rpx;
}

.eval-opera-area .all-evaluate {
  height: 48rpx;
  line-height: 48rpx;
  font-size: 24rpx;
  text-align: center;
  width: 180rpx;
  margin: 0 auto;
  color: #fe585d;
  border: 1px solid #fe585d;
  border-radius: 30rpx;
}

.wxParse {
  background-color: #fff;
  padding: 0 0 20rpx;
}

/* 底部操作 */
.bottom-zhanwei {
  height: 96rpx;
  margin-top: 20rpx;
}

.bottom-opera {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 3;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
}

.bottom-opera .collect {
  width: 100rpx;
  padding: 10rpx 0;
  position: relative;
}

.isEmptyCart {
  position: absolute;
  font-size: 20rpx;
  left: 66rpx;
  top: 0;
  background-color: red;
  color: #fff;
  border-radius: 16rpx;
  height: 32rpx;
  line-height: 32rpx;
  min-width: 32rpx;
  text-align: center;
  padding: 0 6rpx;
  box-sizing: border-box;
  white-space: nowrap;
}

.bottom-opera .collect image {
  display: block;
  margin: 0 auto;
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.bottom-opera .collect .text {
  text-align: center;
  font-size: 22rpx;
  color: #989898;
  display: block;
}

.bottom-opera .collect .cart-number {
  min-width: 30rpx;
  background-color: #FF333C;
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  font-size: 20rpx;
  height: 30rpx;
  line-height: 30rpx;
  box-sizing: border-box;
  text-align: center;
  color: #fff;
}

.bottom-opera .btn-area-box {
  border-radius: 50rpx;
  overflow: hidden;
  margin: 10rpx 20rpx;
  position: relative;
}

.bottom-opera .btn-area-box.disabled {
  opacity: 0.4;
}

.bottom-opera .btn-area-box .disabled-area {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  left: 0;
  opacity: 0;
  z-index: 2;
}

.bottom-opera .appoint-btn {
  height: 80rpx;
  line-height: 80rpx;
  background: -webkit-linear-gradient(left, #FCCB17, #FA9403);
  background: linear-gradient(left, #FCCB17, #FA9403);
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  transition: all 0.3s;
}

.bottom-opera .appoint-btn.buy-btn {
  background: -webkit-linear-gradient(left, #F97400, #F94500);
  background: linear-gradient(left, #F97400, #F94500);
}

.bottom-opera .appoint-btn.clear-btn {
  background: #ccc;
}

.btn-hover {
  opacity: 0.9;
}

/* 商品参数/优惠券弹窗 */
.parameter-modal,
.parameter-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.parameter-modal-mask {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.parameter-modal-con {
  position: absolute;
  background-color: #fff;
  z-index: 1;
  bottom: 0;
  left: 0;
  width: 100%;
  border-radius: 15rpx 15rpx 0 0;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
}

.parameter-modal-con .parameter-title {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  background-color: #FAFAFB;
}

.parameter-modal-con .parameter-detail {
  min-height: 350rpx;
  max-height: 750rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  width: 100%;
}

.parameter-modal-con .wxParse {
  padding: 10rpx;
  box-sizing: border-box;
  width: 100%;
}

.parameter-modal-con .coupon-list {
  padding: 0 26rpx 52rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.parameter-modal-con .coupon-list .coupon-item {
  padding: 30rpx 0;
  border-radius: 20rpx;
  box-shadow: 0 0 10rpx #eee;
  margin-top: 30rpx;
  position: relative;
}

.parameter-modal-con .coupon-list .coupon-item .has-icon {
  display: block;
  width: 104rpx;
  height: 95.33rpx;
  position: absolute;
  right: 0;
  bottom: 0;
}

.parameter-modal-con .coupon-list .coupon-num {
  padding: 0 30rpx;
  border-right: 2rpx solid #f6f6f6;
  text-align: center;
}

.parameter-modal-con .coupon-list .coupon-num .coupon-money {
  color: #FF443B;
  font-size: 40rpx;
  font-weight: 700;
}

.parameter-modal-con .coupon-list .coupon-num .coupon-money text {
  font-size: 24rpx;
}

.parameter-modal-con .coupon-list .coupon-limit {
  color: #666;
  font-size: 24rpx;
  margin-top: 16rpx;
}

.parameter-modal-con .coupon-list .coupon-info {
  padding: 0 26rpx;
}

.parameter-modal-con .coupon-list .coupon-info .title {
  color: #333;
  font-size: 32rpx;
  max-width: 320rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.parameter-modal-con .coupon-list .coupon-info .title text {
  display: inline-block;
  color: #FF443B;
  font-size: 22rpx;
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
  border: 1rpx solid #FF443B;
  margin-right: 14rpx;
}

.parameter-modal-con .coupon-list .coupon-info .coupon-time {
  color: #999;
  font-size: 24rpx;
  margin-top: 20rpx;
}

.parameter-modal-con .coupon-list .get-btn {
  padding: 10rpx 0;
  background-color: #FF443B;
  color: #fff;
  font-size: 26rpx;
  border-radius: 10rpx;
  margin-right: 26rpx;
  width: 140rpx;
  text-align: center;
}

.finish-btn {
  width: 100%;
  margin: 0 auto;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  background: -webkit-linear-gradient(left, #FCCB17, #FA9403);
  background: linear-gradient(left, #FCCB17, #FA9403);
  text-align: center;
  border-radius: 40rpx;
}

.good-info-choose .guige-list .guige-text {
  padding: 10rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
  display: inline-block;
  min-width: 60rpx;
  text-align: center;
  background-color: #f5f5f5;
  font-size: 0;
  border: 2rpx solid #f5f5f5;
}

.good-info-choose .guige-list .guige-text.active {
  color: #ff424a;
  border-color: #ff424a;
  background-color: #FFEBEC;
}

.good-info-choose .guige-list .guige-text image {
  display: inline-block;
  vertical-align: middle;
  height: 34rpx;
  width: 34rpx;
  margin-right: 6rpx;
  border-radius: 6rpx;
}

.good-info-choose .guige-list .guige-text text {
  display: inline-block;
  vertical-align: middle;
  line-height: 34rpx;
  font-size: 26rpx;
  color: #444;
}

.good-info-choose .guige-list .guige-text text.disabled {
  color: #ccc;
}

.good-info-choose .guige-list .guige-text.active text {
  color: #ff424a;
}

.buy-modal .btn-area text.confirm-addcart {
  background: -webkit-linear-gradient(left, #FCCB17, #FA9403);
  background: linear-gradient(left, #FCCB17, #FA9403);
}

.buy-modal .btn-area text.next-step {
  background: -webkit-linear-gradient(left, #F97400, #F94500);
  background: linear-gradient(left, #F97400, #F94500);
}

/* 猜你喜欢 */
.recommend-good-list {
  padding: 0;
  overflow: hidden;
  margin: 0 -10rpx;
}

.recommend-good-list swiper {
  height: 375rpx;
}

.recommend-good-list .good-item {
  display: inline-block;
  vertical-align: middle;
  width: 33.33%;
  box-sizing: border-box;
  padding: 0 10rpx;
}

.recommend-good-list .good-item .good-image {
  display: block;
  height: 224rpx;
  width: 100%;
  background-color: #f0f0f0;
  box-sizing: border-box;
  border-radius: 8rpx;
}

.recommend-good-list .good-item .good-intro {
  background-color: #fff;
  padding: 15rpx 0;
  position: relative;
}

.recommend-good-list .good-item .good-title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 28rpx;
}

.recommend-good-list .good-item .price-buy {
  font-size: 24rpx;
  color: #F94500;
}

.recommend-good-list .good-item .price-buy .now-price {
  font-size: 30rpx;
}

/* 秒杀案例 */
.seckill-case {
  background-color: #fff;
  padding: 0 20rpx;
  position: relative;
  margin-top: 15rpx;
  letter-spacing: 1rpx;
}

.seckill-case .icon {
  width: 80rpx;
  height: 80rpx;
  position: absolute;
  z-index: 0;
}

.seckill-case .icon.up {
  top: 0;
  left: 0;
}

.seckill-case .icon.down {
  right: 0;
  bottom: 0;
}

.seckill-case .case-title {
  font-size: 30rpx;
  color: #FF9456;
  text-align: center;
  padding: 20rpx 0 0;
}

.seckill-case .case-item {
  padding: 20rpx 0;
}

.seckill-case .case-item:last-child .border-b:after,
.seckill-case .case-item:last-child.border-b:after {
  height: 0;
}

.seckill-case .case-item .name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.seckill-case .case-item .sold {
  color: #FF4D4D;
  font-size: 26rpx;
}

.seckill-case .case-item .time {
  color: #999;
  font-size: 26rpx;
}

.canvas-wrap {
  position: fixed;
  left: -100%;
}

.activity-brief {
  font-size: 26rpx;
  color: red;
  padding-left: 25rpx;
}

.group-tip {
  padding: 15rpx 20rpx;
  background-color: #fff;
  margin: 16rpx auto;
  font-size: 26rpx;
}

.group-tip .icon {
  display: block;
  height: 40rpx;
  width: 40rpx;
  margin-right: 10rpx;
}

.group-tip .canjia-btn {
  background-color: #ff534c;
  color: #fff;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 36rpx;
  text-align: center;
  padding: 0 25rpx;
  font-size: 26rpx;
}

.item-hover {
  background-color: #f9fafa;
}

.clear-btn {
  background: #ccc;
  height: 110rpx;
  line-height: 110rpx;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
}

.minus-ticket {}

.border-minus {
  border: 1px solid red !important;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
  margin-top: 10rpx;
}