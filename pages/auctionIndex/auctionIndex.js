// subpages0/auctionpage/auctionIndex/auctionIndex.js
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showLoading: true,
    noMoretip: false,
    page: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (e) {
    var that = this;
    that.resuestauctionIndex();
    that.requestAuctionlist();
    
  },
  onShow: function () {
    app.setCartnum();//更新购物车数量
  },
  //快捷导航跳转
  openFenleiLink: function (e) {
    var that = this;
    var type = e.currentTarget.dataset.type;

    if (type == 'index') {
      wx.reLaunch({
        url: '/pages/singlePage/singlePage'
      })
    }
    if (type == '102') {
      var mobile = app.globalData.telphone ? app.globalData.telphone : '';
      if (mobile) {
        app.makeCall(mobile);
      } else {
        wx.$showToast( "暂未获取到电话");
      }
    } else if (type == '103') {
      wx.navigateTo({
        url: '/pages/sharepage/sharepage'
      })
    } else if (type == '105') {
      that.requestSign();
    } else if (type == '3') {
      var url = e.currentTarget.dataset.url;
      var vrInfo = {
        vrurl: url
      }
      wx.setStorage({
        key: 'webviewUrl',
        data: vrInfo,
        success: function () {
          wx.navigateTo({
            url: '/pages/commonView/commonView',
          })
        }
      })
    } else if (type != '106' && type != '101') {
      var url = e.currentTarget.dataset.url;
      // console.log(url);
      if (url) {
        wx.navigateTo({
          url: url
        })
      }
    }
  },
  //请求首页
  resuestauctionIndex: function () {
    var that = this;
    
    wx.$get({
      map: 'applet_auction_index'
    }).then(res=>{
      let responseData = res.data;
          console.log(responseData);
          that.setData({
            auctionInfo: responseData,
            title: responseData.template.title,
            tempInfo: responseData.template
          })
          if (responseData.template.title) {
            app.setNavtitle(responseData.template.title);
          } else {
            app.setNavtitle("拍卖首页");
          }
    }).catch(err=>{
      
      console.log(err)
    })
  },
  requestAuctionlist: function () {
    var that = this,
        page = that.data.page,
        data = {
          map: 'applet_auction_list',
          page: page
        };
        wx.$get(data,{
          pageList:true
        }).then(res=>{
          let responseData = res.data;
          var allArr = [];
          var initArr = that.data.auctionList ? that.data.auctionList : [];
          var curArr = responseData;
          var lastPageLength = curArr.length;
          if (page > 0) {
            allArr = initArr.concat(curArr);
          } else {
            allArr = responseData;
          }
          that.setData({
            auctionList: allArr
          })
          if (lastPageLength < 10) {
            that.setData({
              noMoretip: true,
              showLoading: false
            });
          }
        }).catch(err=>{
          if (page <= 0) {
            that.setData({
              auctionList: [],
              noMoretip: false,
              showLoading: false
            })
          } else {
            that.setData({
              noMoretip: true,
              showLoading: false
            });
          }
          console.log(err)
        })
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    var that = this;
    that.setData({
      showLoading: true,
      noMoretip: false,
      page: 0
    })
    that.resuestauctionIndex();
    that.requestAuctionlist();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var that = this,
        isMore = that.data.noMoretip,
        page = that.data.page;

    page++;
    that.setData({
      page: page
    });
    if (!isMore) {
      that.requestAuctionlist();
    } 
  },
  toauctionDetail: function (e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/subpages0/auctionpage/auctionDetail/auctionDetail?id=' + id,
    })
  },
  toMyauction: function () {
    wx.navigateTo({
      url: '/subpages0/auctionpage/myAuction/myAuction',
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    var that = this, title = that.data.tempInfo.title;
    app.getPoint(that);
    return {
      title: title,
      path: '/pages/auctionIndex/auctionIndex'
    }
  }
})