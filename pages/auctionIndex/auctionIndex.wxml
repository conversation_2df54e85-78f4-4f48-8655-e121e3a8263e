<!--subpages0/auctionpage/auctionIndex/auctionIndex.wxml-->
<!-- <back-home></back-home> -->
<nav-bar page-name="{{title?title:'拍卖'}}"></nav-bar>
<view class="tp-info-intro">
  <view class="banner-wrap">
    <swiper indicator-dots="{{true}}" circular="{{true}}" indicator-color="#fff" indicator-active-color="#0099f6"
  autoplay="{{true}}" interval="4000" duration="800">
      <block wx:key="index" wx:for="{{auctionInfo.slide}}" >
        <swiper-item data-id="{{item.link}}" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="openFenleiLink">
          <image src="{{item.img}}" class="slide-image"/>
          <block wx:if="{{item.type=='106'}}">
            <navigator class="applet-jump" target="miniProgram" path="{{item.path}}" app-id="{{item.url}}" open-type="navigate" />
          </block>
        </swiper-item>
      </block>
    </swiper>
  </view>
  <view class="space"></view>
  <view class="sale-list-wrap">
    <view class="sale-title flex-wrap" bindtap="toMyauction">
      <view class="flex-con">{{tempInfo.listTitle?tempInfo.listTitle:'拍卖列表'}}</view>
      <view class="right-opera">
        <text>{{tempInfo.orderTitle?tempInfo.orderTitle:'我的竞拍'}}</text>
        <image src="/images/food-jt.png" style="height:22rpx;" mode="aspectFit"></image>
      </view>
    </view>
    
    <view class="sale-list" wx:if="{{auctionList.length>0}}">
      <block wx:key="index" wx:for="{{auctionList}}" wx:for-item="auction">
      <view class="sale-item flex-wrap border-b" data-id="{{auction.id}}" bindtap="toauctionDetail">
        <view class="img-box">
          <image src="{{auction.cover}}" mode="aspectFill"></image>
          <view class="state {{auction.status==1?'green':''}}  {{auction.status==3?'gray':''}}">
            <block wx:if="{{auction.status==1}}">未开拍</block>
            <block wx:if="{{auction.status==2}}">进行中</block>
            <block wx:if="{{auction.status==3}}">已结束</block>
          </view>
        </view>
        <view class="item-intro flex-con">
          <view class="good-name">{{auction.title}}</view>
          <view class="good-price" wx:if="{{auction.status==1}}">起拍价 <text class="price"><text class="unit">￥</text>{{auction.startPrice}}</text></view>
          <view class="good-price" wx:if="{{auction.status==2||auction.status==3}}">{{auction.status==2?'当前':'获拍'}}价 <text class="price"><text class="unit">￥</text>{{auction.price}}</text></view>
          <view class="sale-num">{{auction.status==1?'报名':'参加'}}人数 {{auction.joinNum}}</view>
          <view class="good-brief" wx:if="{{auction.status==1}}">开拍时间：{{auction.startTime}}</view>
          <view class="good-brief" wx:if="{{auction.status==2}}">结束时间：{{auction.endTime}}</view>
        </view>
      </view>
      </block>
    </view>
    <view class="no-data" style="padding:160rpx 0;" wx:if="{{auctionList.length<=0}}">
      <image src="/images/empty_img.png" mode="aspectFit"></image>
      <text>暂无相关拍卖哦~</text>
    </view>
    <!--上拉加载提示-->
    <view class="loading-tip" wx:if="{{showLoading}}">
      <view class="icon_load">
        <view id="floatingBarsG">
          <view class="blockG" id="rotateG_01"></view>
          <view class="blockG" id="rotateG_02"></view>
          <view class="blockG" id="rotateG_03"></view>
          <view class="blockG" id="rotateG_04"></view>
          <view class="blockG" id="rotateG_05"></view>
          <view class="blockG" id="rotateG_06"></view>
          <view class="blockG" id="rotateG_07"></view>
          <view class="blockG" id="rotateG_08"></view>
        </view>
      </view>
      <text>数据加载中</text>
    </view>
    <view class="nomore-tip" wx:if="{{noMoretip&&auctionList.length>0}}">没有更多数据了</view>
  </view>
</view>
<!-- 获取用户信息提示框 -->
<view class="get-userinfo-modal" wx:if="{{isShowgetinfo}}">
  <view class="get-userinfo">
    <view class="label-title">小程序授权提示</view>
    <view class="tipx-txt">允许小程序获得你的头像昵称信息</view>
    <view class="flex-wrap border-t" style="padding:10rpx 0;">
      <view class="btn flex-con border-r" bindtap="_hideGetuser">取消</view>
      <functional-page-navigator hover-class="none" version="develop" name="loginAndGetUserInfo" bind:success="_loginSuccess" class="btn flex-con confirm-btn">确定</functional-page-navigator>
    </view>
  </view>
</view>
<!--错误提示-->
 