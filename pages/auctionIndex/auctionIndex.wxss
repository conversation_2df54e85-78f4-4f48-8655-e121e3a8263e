/* subpages0/auctionpage/auctionIndex/auctionIndex.wxss */
page{border:none;background-color: #fff;}
.space{height: 20rpx;background-color: #f8f8f8;}
.banner-wrap{height: 360rpx;width: 100%;position: relative;}
.banner-wrap .notice{position: absolute;top:0;width: 100%;left:0;background-color: rgba(0, 0, 0, .5);z-index: 2;overflow: hidden;display: flex;align-items: center;}
.banner-wrap .notice .icon_tz{display: block;height: 28rpx;width: 30rpx;margin: 20rpx;}
.banner-wrap .notice-txt{flex: 1;overflow: hidden;}
.banner-wrap .notice-txt .text{height: 80rpx;line-height: 80rpx;color: #fff;font-size: 28rpx;}
.banner-wrap .notice-txt .marqueeTitle{white-space: nowrap;position: relative;}
.banner-wrap swiper{width: 100%;height: 100%;}
.banner-wrap .wx-swiper-dot{position:relative;height: 14rpx!important;width: 14rpx!important;bottom:0;}
.banner-wrap .wx-swiper-dot{position:relative;height: 14rpx!important;width: 14rpx!important;}
.slide-image{display: block;width: 100%;height: 100%;}
/* 拍卖列表 */
.sale-list-wrap{padding-top: 15rpx;}
.sale-title{font-size: 32rpx;font-weight: bold;padding-left: 20rpx;}
.sale-title .right-opera{font-weight: normal;padding: 10rpx 12rpx;}
.sale-title .right-opera text{font-size: 26rpx;color: #999;display: inline-block;}
.sale-title .right-opera image{display: inline-block;vertical-align: middle;height: 30rpx;width: 30rpx;}
.sale-list{padding-left: 20rpx;}
.sale-item{padding: 25rpx 20rpx 25rpx 0;align-items: flex-start;}
.sale-item .img-box{height: 210rpx;width: 210rpx;position: relative;margin-right: 25rpx;overflow: hidden;}
.sale-item .img-box image{display: block;height: 100%;width: 100%;background-color: #f8f8f8;}
.sale-item .img-box .state{font-size:20rpx;color:#fff;text-align:center;line-height:1.5;background-color:#ff534c;position:absolute;left:-28rpx;top:16rpx;width:116rpx;transform:rotate(-45deg);padding-top: 2rpx;}
.sale-item .img-box .state.green{background-color: #00b94c;}
.sale-item .img-box .state.gray{background-color: #c1c1c1;}
.sale-item .img-box .remain-tip{position: absolute;bottom:0;left: 0;z-index: 2;background-color: rgba(255, 82, 82, .9);color: #fff;font-size: 22rpx;height: 48rpx;line-height: 50rpx;text-align: center;width: 100%;}
.sale-item .img-box .remain-tip.black{background-color: rgba(0, 0, 0, .5);}
.item-intro{min-height: 210rpx;padding: 5rpx 0;box-sizing: border-box;line-height: 1.7;position: relative;}
.item-intro>view{white-space: nowrap;text-overflow: ellipsis;max-width: 470rpx;font-size: 26rpx;color: #999;}
.item-intro .good-name{font-size: 30rpx;color: #333;margin-bottom: 5rpx;overflow: hidden;}
.item-intro .good-brief{overflow: hidden;margin:6rpx 0;}
.item-intro .sale-num{margin:6rpx 0;}
.item-intro .good-price .price{font-size: 34rpx;color: #ff3446;font-weight: bold;}
.item-intro .good-price .unit{font-size: 26rpx;font-weight: normal;}
.item-intro .opera-btn{position: absolute;right: 0;bottom: 5rpx;}
.item-intro .opera-btn .cus-btn{height: 60rpx;line-height: 60rpx;color: #fff;background-color: #ff5252;box-shadow: 0 0 10rpx #ff4343;padding: 0 30rpx;text-align: center;font-size: 26rpx;border-radius: 36rpx;}
.item-intro .opera-btn .cus-btn.gray{background-color: #dedede;box-shadow: 0 0 10rpx #e3e2e3;}