//index.js
//获取应用实例
const app = getApp();
Page({
  data: {
    sortType: 2,
    shopGoods: [],
    showLoading: true,
    noMoretip: false,
    page: 0
  },
  onLoad: function (e) {
  },
  onShow: function () {
    wx.showNavigationBarLoading();
    var that = this;
    var data = {};
    var page = that.data.page;
    var sortType = that.data.sortType;
    data.map = 'applet_goods_list';
    data.sortType = sortType;
    data.page = page;
    wx.getStorage({
      key: "cuxiao",
      success: function (res) {
        var type = res.data;
        var navTitle;
        console.log(type == 'sale');
        if (type == 'sale') {
          data.recomm = 1;
          navTitle = '促销商品';
        }else{
          navTitle = '全部商品';
        }
        wx.setNavigationBarTitle({
          title: navTitle
        });
        wx.hideNavigationBarLoading();

        wx.$get(data,{
          pageList:true
        }).then(res=>{
          var allArr = [];
          var initArr = that.data.shopGoods;
          var curArr = res.data;
          var lastPageLength = curArr.length;
          if (page > 0) {
            allArr = initArr.concat(curArr);
          } else {
            allArr = res.data;
          }
          that.setData({
            shopGoods: allArr
          })
          if (lastPageLength < 10) {
            that.setData({
              noMoretip: true,
              showLoading: false
            });
          }
        }).catch(err=>{
          console.log(err);
          if (page <= 0) {
            that.setData({
              shopGoods: [],
              noMoretip: false,
              showLoading: false
            })
          } else {
            that.setData({
              noMoretip: true,
              showLoading: false
            });
          }
        })
      }
    })
  },
  requestAllFl:function(){

  },
  onHide:function(){
    wx.removeStorage({
      key: 'cuxiao',
      success: function (res) {
        console.log(res.data)
      }
    })
  },
onPullDownRefresh: function () {
  this.setData({
    page: 0,
    noMoretip: false,
    showLoading: true
  });
  this.onShow();
   
},
onReachBottom: function () {
  var that = this;
   
  var isMore = that.data.noMoretip;
  var page = that.data.page;
  page++;
  that.setData({
    page: page
  });
  if (!isMore) {

    that.onShow();
  }
},
goodDetail: function (e) {
  var goodId = e.currentTarget.dataset.id;
  wx.navigateTo({
    url: '/pages/goodDetail/goodDetail?id=' + goodId
  })
},
changeShowType: function (e) {
  var type = e.target.dataset.type;
  this.setData({
    showType: type
  })
},
searchPage: function () {
  wx.navigateTo({
    url: '../searchList/searchList'
  })
},
sortGood: function (e) {
  var type = e.target.dataset.sort;
  this.setData({
    sortType: type
  })
  this.onPullDownRefresh();
}
})