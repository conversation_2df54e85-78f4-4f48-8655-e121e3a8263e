const app = getApp();
Page({
  data: {
    isUnfold: true,
    name: '',
    phone: '',
    wechatnum: ''
  },
  onLoad: function (e) {
    var that = this;
    // var name = e.name;
    // app.setNavtitle(name);
  },
  onReady: function () {

  },
  onShow: function () {
    var that = this;
    // app.setNavtitle('分销中心');
  
      that.requestPartners();
      that.requestFenxiaoCenter();
      that.setData({
        mobile: app.globalData.memberInfo ? app.globalData.memberInfo.mobile : ''
      })
    
    app.setCartnum();//更新购物车数量
  },
  toApplyFenxiao: function () {
    wx.navigateTo({
      url: '/pages/becomePartners/becomePartners'
    })
  },
  requestFenxiaoCenter: function () {
    var that = this;
    var data = {};
    data.map = 'applet_three_center_cfg_new';
    //发起请求，获取列表列表
    wx.$get(data,{
      stopPull:true
    }).then(res=>{
      let responseData = res.data;
      var memberInfo = responseData.member;
      if (app.globalData.memberInfo) {
        memberInfo = app.globalData.memberInfo;
      }
      app.globalData.memberInfo = memberInfo;
      if (app.globalData.memberInfo.mobile) {
        that.setData({
          mobile: app.globalData.memberInfo.mobile
        })
      }
      that.setData({
        fenxiaoInfo: responseData,
      })
      var level = responseData.level;
      if (level==3){
        var downlevel = responseData.down_level;
        that.setData({
          down_level: responseData.down_level
        })
      } else if (level == 2){
        var downlevel=responseData.down_level;
        that.setData({
          down_level: downlevel.slice(0,2)
        })
      } else if (level == 1){
        var downlevel = responseData.down_level;
        that.setData({
          down_level: downlevel.slice(0, 1)
        })
      }
    }).catch(err=>{
      that.setData({
        noData:true,
        notips: err.em
      })
     console.log(err)
    })

  },
  onPullDownRefresh: function () {
    var that = this;
    that.onShow();
  },
  clickUnfold: function () { // 展开折叠我的会员
    var that = this;
    that.setData({
      isUnfold: !that.data.isUnfold
    })
  },
  toMymember: function (e) {// 我的下级会员页面
    var title = e.currentTarget.dataset.title;
    var level = e.currentTarget.dataset.level;
    var count = e.currentTarget.dataset.count;
    if (count > 0) {
      wx.navigateTo({
        url: '/subpages/myMember/myMember?title=' + title + '&level=' + level
      })
    }
  },
  toShareIncome: function (e) {// 我的分享收入
    wx.navigateTo({
      url: '/subpages/shareIncome/shareIncome'
    })
  },
  toFanxianIncome: function (e) {// 我的返现收入
    wx.navigateTo({
      url: '/subpages0/fanxianIncome/fanxianIncome'
    })
  },
  toJuniorDistributionApply() {  //我的下级分销申请
    wx.navigateTo({
      url: '/subpages0/juniorDistributionApply/juniorDistributionApply'
    })
  },
  toFenxiaoOrder: function (e) {// 分销订单
    wx.navigateTo({
      url: '/subpages/fenxiaoOrder/fenxiaoOrder'
    })
  },
  toSingleFenxiaoOrder:function(e){
    wx.navigateTo({
      url: '/subpages/singleFenxiaoOrder/singleFenxiaoOrder'
    })
  },
  tomyReference: function (e) {// 我的推荐人
    wx.navigateTo({
      url: '/subpages/myReference/myReference'
    })
  },
  tomyProfile: function (e) {// 我的资料
    wx.navigateTo({
      url: '/subpages/myfxProfile/myfxProfile'
    })
  },
  toextendCode: function (e) {// 推广二维码
    var that = this;
    that.requestExtendinfo();
  },
  requestExtendinfo: function () {
    var that = this;
    var memberInfo = app.globalData.memberInfo;
    var data = {};
    data.map = 'applet_three_share_code_new';
    // data.path = '/pages/distributionTip/distributionTip?mid=' + memberInfo.mid;
    //发起请求，获取列表列表
    wx.$get(data).then(res=>{
      that.setData({
        extendInfo: res.data
      })
      wx.setStorage({
        key: 'extendInfo',
        data: that.data.extendInfo,
        success: function () {
          wx.navigateTo({
            url: '/subpages0/extendCode/extendCode'
          })
        }
      })
    }).catch(err=>{
  
    console.log(err)
    })
  },
  toincomeWithdraw: function (e) {// 申请提现
    wx.navigateTo({
      url: '/subpages/incomeWithdraw/incomeWithdraw'
    })
  },
  tosellRanklist: function (e) {// 销售排行榜
    wx.navigateTo({
      url: '/subpages0/sellRanklist/sellRanklist'
    })
  },
  getPhoneNumber: function (e) {
    console.log('获取手机号',e)
    var that = this;
    wx.login({
      success: function (res) {
        if (res.code) {
          var data = {
            map: 'applet_three_save_phone_new',
            code: res.code,
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv
          };
          if (!data.encryptedData) {
            that.tomyProfile();
          } else {
            that.requestMobile(data);
          }
        } else {
          console.log('获取用户登录态失败！' + res.errMsg)
        }
      }
    });
  },
  requestMobile: function (data) {
    var that = this;
    //发起请求，获取列表列表
    wx.$get(data,{
      stopPull:true
    }).then(res=>{
      let responseData = res.data;
      var mobile = responseData;
      var memberInfo = app.globalData.memberInfo;
      memberInfo.mobile = mobile;
      console.log(app.globalData.memberInfo);
      app.globalData.memberInfo = memberInfo;
      that.tomyProfile();
    }).catch(err=>{
  
    console.log(err)
    })
  
  },
  requestPartners: function () {
    var that = this;

    //发起请求，获取列表列表
    wx.$get({
      map : 'applet_three_configure_new'
    }).then(res=>{
      let responseData = res.data;
          that.setData({
            partners: responseData,
            isdistrib: responseData.isdistrib,
            isapply: responseData.isapply
          })
          app.globalData.isdistrib = responseData.isdistrib;
          app.globalData.isapply = responseData.isapply;
          let pageTitle=responseData.pageTitle||'分销中心'
         
            wx.setNavigationBarTitle({
              title: pageTitle
            });
          
    }).catch(err=>{
  
    console.log(err)
    })
    
  },
  nameChange: function (e) {
    var that = this;
    that.setData({
      name: e.detail.value
    })
  },
  phoneChange: function (e) {
    var that = this;
    that.setData({
      phone: e.detail.value
    })
  },
  wechatnumChange: function (e) {
    var that = this;
    that.setData({
      wechatnum: e.detail.value
    })
  },
  submitRequest: function () {
    var that = this;
    app.getSubId( 'applet_three_apply_distribution_new').then(res =>{that.toSubmitRequest()})
  },
  toSubmitRequest: function (e) {
    var that = this;
    var data = {
      map: 'applet_three_apply_distribution_new',
      realname: that.data.name,
      mobile: that.data.phone,
      wxno: that.data.wechatnum,
    }
    var hasname = that.data.partners.hasname;
    var hasphone = that.data.partners.hasphone;
    var haswx = that.data.partners.haswx;
    if (hasname == 1) {
      if (!data.realname) {
        wx.$showToast( "请输入您的姓名");
        return;
      }
    }
    // if (hasphone == 1) {
    //   var isMob = /^(0|86|17951)?(13[0-9]|15[012356789]|17[01678]|18[0-9]|14[57])[0-9]{8}$/;
    //   if (!isMob.test(data.mobile)) {
    //     wx.$showToast( "请输入正确的手机号");
    //     return;
    //   }
    // }
    if (haswx == 1) {
      if (!data.wxno) {
        wx.$showToast( "请输入您的微信号");
        return;
      }
    }
    wx.$get(data).then(res=>{
      let responseData = res.data;
      wx.$showToast( responseData.msg);
      that.setData({
        name: '',
        phone: '',
        wechatnum: '',
        isapply: responseData.isapply
      })
      app.globalData.isapply = responseData.isapply;
    }).catch(err=>{
  
    console.log(err)
    })
  },
  seeDetail: function (e) {
    var id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/informationDetail/informationDetail?id=' + id
    })
  },
  toPosterimg: function (e) {
    var that = this;
    var title = e.currentTarget.dataset.title;
    wx.navigateTo({
      url: '/subpages/posterImg/posterImg?title=' + title
    })
  },
})