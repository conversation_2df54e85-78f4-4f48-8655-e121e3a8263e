const app = getApp();
import AddressParse from './address-parse';
console.log('AddressParse', AddressParse)
import {
  wxChooseLocation
} from './../../utils/wx';
Page({
  data: {
    flag: 0,
    region: [],
    mapKey: '',
    addressdetail: ''
  },
  onLoad: function (e) {
    const that = this;
    let mapKey = wx.getStorageSync('mapKey');
    if (e && e.type) {
      that.setData({
        operaType: e.type
      })
    }
    if (that.data.operaType == 'edit') {
      wx.getStorage({
        key: "curEditaddress",
        success: (res) => {
          let resData = res.data;
          console.log('resData', resData);
          // that.dealAddress(resData.detailNew)
          that.setData({
            name: resData.name,
            mobile: resData.mobile,
            addressdetail: resData.address,
            code: resData.post,
            editId: resData.id,
            curChooseAddress: resData.pcda,
            curChooseLatitude: resData.lat,
            curChooseLongitude: resData.lng,
            isdefault: resData.isdefault,
            region: [resData.province, resData.city, resData.area]
          })
        }
      })
    }
    that.setData({
      extAppid: app.globalData.appid,
      mapKey: mapKey
    })
  },
  bindRegionChange(e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    this.setData({
      region: e.detail.value
    })
  },
  chooseLocation: function () {
    const that = this;
    wxChooseLocation().then(res => {
      console.log(res)
      that.setData({
        addressName: res.name,
        addressdetail: res.name,
        curChooseLatitude: res.lat,
        curChooseLongitude: res.lng,
      }, function () {
        that.dealAddress(res.address)
      })
    }).catch(err => {
      console.log(err)
    })
  },
  hideSetting: function () {
    this.setData({
      isShowsetting: false
    })
  },
  /* 处理地址 */
  dealAddress(address) {
    // 预处理：去除姓名和手机号，只保留地址部分
    let addr = address;
    // 匹配手机号（11位数字）
    addr = addr.replace(/1[3-9]\d{9}/, '');
    // 匹配姓名（假设在最前面，2-6个汉字或带空格）
    addr = addr.replace(/^([\u4e00-\u9fa5]{2,6}|[\u4e00-\u9fa5]{1,6}\s)/, '');
    addr = addr.trim();
    // options为可选参数，不传默认使用正则查找
    const options = {
      type: 0, // 哪种方式解析，0：正则，1：树查找
      textFilter: [], // 预清洗的字段
      nameMaxLength: 4 // 查找最大的中文名字长度
    };
    let autoaddress = addr,
      newAutoaddress = autoaddress;
    // 修改正则表达式，确保直辖市名称前后有合适的边界
    let pat = /(^|[\s省市区县])(上\s*海|北\s*京|重\s*庆|天\s*津)(?!市|路|街|道|区|县|镇|乡)/g;
    let b = pat.exec(autoaddress);
    if (b && b[2]) {
      let replacement = b[1] + (b[2].replace(/\s/g, '')) + '市';
      newAutoaddress = autoaddress.replace(pat, replacement);
    }
    const parseResult = AddressParse(newAutoaddress, options);
    // 优先用AddressParse的结果
    if (parseResult && parseResult.province && parseResult.city && parseResult.area) {
      this.setData({
        region: [parseResult.province, parseResult.city, parseResult.area]
      });
      return;
    }
    // 兜底：遍历citys
    var region = [];
    const citys = wx.getStorageSync('citysNames').citys;
    citys.forEach(item => {
      if (addr.includes(item.name)) {
        region.push(item.name)
        item.children.forEach(subItem => {
          if (addr.includes(subItem.name)) {
            region.push(subItem.name)
            subItem.children.forEach(subChildren => {
              if (addr.includes(subChildren.name)) {
                region.push(subChildren.name)
              }
            })
          }
        })
      }
    })
    console.log(region)
    if (region.length == 0) {
      return
    } else {
      this.setData({
        region: region
      })
    }
  },
  openSetting: function () {
    this.hideSetting();
    wx.openSetting({
      success: (res) => {
        res.authSetting = {
          "scope.userLocation": true
        }
      },
      fail: (res) => {
        console.log(res);
      }
    })
  },
  inputChange: function (e) {
    let type = e.currentTarget.dataset.type;
    this.setData({
      [type]: e.detail.value
    })
  },
  saveAddress: function () {
    const that = this;
    let {
      curChooseLatitude,
      curChooseLongitude,
      name,
      mobile,
      addressdetail,
      code,
      operaType,
      editId
    } = that.data;
    let data = {
      id: operaType == 'edit' ? editId : '',
      map: 'applet_address_add',
      name: name,
      mobile: mobile,
      lat: curChooseLatitude || '',
      lng: curChooseLongitude || '',
      address: addressdetail,
      code: code || ''
    }
    if (!data.name) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return;
    }
    if (!data.mobile) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    if (!that.data.region.length || that.data.region.length == 0) {
      wx.showToast({
        title: '请选择省市区',
        icon: 'none'
      });
      return;
    }
    if (!data.address) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }
    // 只输入文本，没有选择地址
    if (!curChooseLatitude || !curChooseLongitude) {
      let addrdetail = that.data.region.join('') + addressdetail;
      let locationArr = [];
      // 地址转换成经纬度
      wx.request({
        url: `https://restapi.amap.com/v3/geocode/geo?key=${this.data.mapKey}&address=${addrdetail}`,
        success(res) {
          if (res.data.status == 1) {
            let locations = res.data.geocodes;
            let location = locations[0].location;
            // if (locations.length > 1) {
            //   let similarityMax = locations[0];
            //   let similarityValue = that.getSimilarity(addrdetail, locations[0].formatted_address);
            //   locations.forEach(location => {
            //     let curSimilarityValue = that.getSimilarity(addrdetail, location.formatted_address);
            //     if (curSimilarityValue >= similarityValue) {
            //       similarityMax = location;
            //     }
            //   })
            //   location = similarityMax.location;
            // }
            console.log(location);
            locationArr = location.split(',');
            curChooseLatitude = locationArr[1];
            curChooseLongitude = locationArr[0];
            data.lat = curChooseLatitude;
            data.lng = curChooseLongitude;
            that.submitAddress(data);
          } else {
            app.errorTip(that, '地址解析失败', 1500);
            wx.hideLoading();
          }
        },
        fail(res) {
          console.log(res)
        }
      })
    } else {
      console.log(data);
      that.submitAddress(data);
    }
  },
  submitAddress: function (params) {
    wx.$get(params).then(res => {
      wx.$showModal({
        content: res.data.msg,
        showCancel: false
      }).then(res => {
        wx.navigateBack({
          delta: 1
        })
      }).catch(err => {
        console.log(err);
      })
    }).catch(err => {
      console.log(err);
    })
  },
  // 计算地址相似度
  getSimilarity: function (addressOne, addressSec) {
    let sameNum = 0
    //寻找相同字符
    for (let i = 0; i < addressOne.length; i++) {
      for (let j = 0; j < addressSec.length; j++) {
        if (addressOne[i] === addressSec[j]) {
          sameNum++;
          break;
        }
      }
    }
    let length = addressOne.length > addressSec.length ? addressOne.length : addressSec.length;
    console.log((sameNum / length) * 100);
    return (sameNum / length) * 100 || 0;
  },
  // 自动识别地址
  autoAddress: function () {
    // options为可选参数，不传默认使用正则查找
    const options = {
      type: 0, // 哪种方式解析，0：正则，1：树查找
      textFilter: [], // 预清洗的字段
      nameMaxLength: 4 // 查找最大的中文名字长度
    };
    if (!this.data.autoaddress) {
      wx.$showToast('请输入要识别的内容');
      return;
    } else {
      // type参数0表示使用正则解析，1表示采用树查找, textFilter地址预清洗过滤字段。
      let autoaddress = this.data.autoaddress,
        newAutoaddress = autoaddress;
      // 修改正则表达式，确保直辖市名称前后有合适的边界
      let pat = /(^|[\s省市区县])(上\s*海|北\s*京|重\s*庆|天\s*津)(?!市|路|街|道|区|县|镇|乡)/g;
      let b = pat.exec(autoaddress);
      if (b && b[2]) {
        let replacement = b[1] + (b[2].replace(/\s/g, '')) + '市';
        newAutoaddress = autoaddress.replace(pat, replacement);
      }
      console.log(newAutoaddress);
      this.dealAddress(newAutoaddress)
      const parseResult = AddressParse(newAutoaddress, options);
      // The parseResult is an object contain { province: '', name: '', city: '', area: '', detail: '', phone: '', postalCode: '' }
      /* let region = [parseResult.province, parseResult.city, parseResult.area]; */
      this.setData({
        name: parseResult.name,
        mobile: parseResult.phone,
        addressdetail: parseResult.detail,
        code: parseResult.postalCode,
        /* region: region, */
        autoaddress: ''
      })
      // wx.$showToast('为保证准确送达，请手动选择您的收货地区位置信息')
    }
  },
  deleteAddress: function (e) {
    let delId = e.currentTarget.dataset.editid;
    wx.$showModal({
      content: '确认删除该收货地址吗？'
    }).then(res => {
      wx.$get({
        map: 'applet_address_delete',
        suid: app.globalData.suid,
        id: delId
      }).then(res => {
        wx.$showModal({
          content: res.data.msg,
          showCancel: false
        }).then(res => {
          wx.navigateBack({
            delta: 1
          })
        }).catch(err => {
          console.log(err);
        })
      }).catch(err => {
        console.log(err);
      })
    })
  },
  // 表单验证提示
  formVerify(list) {
    let rule = {
      mobile: /^1[3456789]d{9}$/
    }
    for (let item of list) {
      if (item.val) {
        wx.$showToast(item.tip);
        return false;
      }
    }
    return true;
  }
})
